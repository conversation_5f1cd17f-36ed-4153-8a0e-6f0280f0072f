<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/breadcrumb.php';
require_once 'includes/Logger.php';
require_once 'includes/mail_config.php';
require_once 'includes/email_helper.php';

$log = Logger::get('appointment_confirmation');
$page_title = "Afspraak Bevestiging";

// Get encrypted appointment ID from URL
$encrypted_id = isset($_GET['id']) ? $_GET['id'] : '';
$appointment = null;
$emailSent = false;

if (!empty($encrypted_id)) {
    try {
        // Decrypt the appointment ID
        $decryption_key = ENCRYPTION_KEY; // Define this in config.php
        $appointment_id = openssl_decrypt(
            base64_decode($encrypted_id),
            'AES-256-CBC',
            $decryption_key,
            0,
            substr(hash('sha256', $decryption_key), 0, 16)
        );
        
        if ($appointment_id === false) {
            $log->error('Failed to decrypt appointment ID', ['encrypted_id' => $encrypted_id]);
            $appointment_id = 0;
        } else {
            $appointment_id = (int)$appointment_id;
        }
        
        // Get appointment details if decryption was successful
        if ($appointment_id > 0) {
            $pdo = getPDO();
            $stmt = $pdo->prepare("SELECT * FROM appointments WHERE id = ?");
            $stmt->execute([$appointment_id]);
            $appointment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Send confirmation email if not already sent
            if ($appointment && (!isset($appointment['email_sent']) || $appointment['email_sent'] != 1)) {
                $log->info('Sending confirmation email for appointment', ['id' => $appointment_id]);
                
                // Get service name based on service_type and service_id
                $serviceName = '';
                if (!empty($appointment['service_type']) && !empty($appointment['service_id'])) {
                    $service_id = (int)$appointment['service_id'];
                    
                    if ($appointment['service_type'] === 'behandeling') {
                        // List of treatments
                        $behandelingen = [
                            1 => ['name' => 'Abhyanga massage', 'price' => 75.00],
                            2 => ['name' => 'Kruidenstempelmassage 60 min', 'price' => 65.00],
                            3 => ['name' => 'Kruidenstempelmassage 90 min', 'price' => 85.00],
                            4 => ['name' => 'Ontspanningsmassage 30 min', 'price' => 35.00],
                            5 => ['name' => 'Ontspanningsmassage 60 min', 'price' => 65.00],
                            6 => ['name' => 'Ontspanningsmassage 90 min', 'price' => 85.00],
                            7 => ['name' => 'Stemvorktherapie', 'price' => 65.00],
                            8 => ["name" => "Tienermassage", "price" => 45.00],
                            9 => ["name" => "Go4Balance Consult", "price" => 45.00],
                            10 => ["name" => "Indian Foot Massage", "price" => 55.00]
                        ];
                        
                        $serviceName = isset($behandelingen[$service_id]) ? $behandelingen[$service_id]['name'] : '';
                        
                    } elseif ($appointment['service_type'] === 'workshop') {
                        // List of workshops
                        $workshops = [
                            10 => ['name' => 'Mild in het wild Compassiewandeling', 'price' => 45.00],
                            11 => ['name' => 'Workshop Uit je hoofd in je lijf', 'price' => 39.95]
                        ];
                        
                        $serviceName = isset($workshops[$service_id]) ? $workshops[$service_id]['name'] : '';
                    } elseif ($appointment['service_type'] === 'welkomstconsult') {
                        // List of welkomstconsult options
                        $welkomstconsult = [
                            12 => ['name' => 'Shiro Abhyanga (Welkomstconsult)', 'price' => 18.00],
                            13 => ['name' => 'Pad Abhyanga (Welkomstconsult)', 'price' => 18.00]
                        ];
                        
                        $serviceName = isset($welkomstconsult[$service_id]) ? $welkomstconsult[$service_id]['name'] : '';
                    }
                }
                
                // Client email
                $clientMessage = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Bevestiging afspraak - Jacqueline Tjassens</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background-color: #d5bc5a; padding: 20px; color: white; }
        .content { padding: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th { text-align: left; padding: 10px; background-color: #f2f2f2; }
        .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
        .footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Bedankt voor uw afspraak!</h1>
        </div>
        <div class='content'>
            <p>Beste " . htmlspecialchars($appointment['client_name']) . ",</p>
            <p>Uw afspraak is succesvol ingepland:</p>
            <ul>
                <li>Datum: " . date('d-m-Y', strtotime($appointment['appointment_date'])) . "</li>
                <li>Tijd: " . htmlspecialchars($appointment['appointment_time']) . "</li>
                <li>Type: " . htmlspecialchars($appointment['service_type']) . "</li>";
                if (!empty($serviceName)) {
                    $clientMessage .= "<li>Behandeling: " . htmlspecialchars($serviceName) . "</li>";
                }
                $clientMessage .= "</ul>
            <p>Heeft u vragen? Neem dan contact met ons <NAME_EMAIL> of 06-41412241.</p>
        </div>
        <div class='footer'>
            <p>© " . date('Y') . " Jacqueline Tjassens. Alle rechten voorbehouden.</p>
        </div>
    </div>
</body>
</html>";

                // Owner email
                $ownerMessage = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>Nieuwe afspraak ontvangen</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background-color: #d5bc5a; padding: 20px; color: white; }
        .content { padding: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th { text-align: left; padding: 10px; background-color: #f2f2f2; }
        .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Nieuwe afspraak ontvangen</h1>
        </div>
        <div class='content'>
            <p>Er is een nieuwe afspraak ingepland:</p>
            <ul>
                <li>Klant: " . htmlspecialchars($appointment['client_name']) . "</li>
                <li>Email: " . htmlspecialchars($appointment['client_email']) . "</li>
                <li>Telefoon: " . htmlspecialchars($appointment['client_phone'] ?? 'Niet opgegeven') . "</li>
                <li>Datum: " . date('d-m-Y', strtotime($appointment['appointment_date'])) . "</li>
                <li>Tijd: " . htmlspecialchars($appointment['appointment_time']) . "</li>
                <li>Type: " . htmlspecialchars($appointment['service_type']) . "</li>";
                if (!empty($serviceName)) {
                    $ownerMessage .= "<li>Behandeling: " . htmlspecialchars($serviceName) . "</li>";
                }
                if (!empty($appointment['notes'])) {
                    $ownerMessage .= "<li>Opmerkingen: " . nl2br(htmlspecialchars($appointment['notes'])) . "</li>";
                }
                $ownerMessage .= "</ul>
        </div>
    </div>
</body>
</html>";

                // Send emails
                $clientEmailSent = sendEmail(
                    "Bevestiging afspraak - Jacqueline Tjassens",
                    $clientMessage,
                    'Bevestiging van uw afspraak bij Jacqueline Tjassens',
                    $appointment['client_email'],
                    $appointment['client_name']
                );
                
                $ownerEmailSent = sendEmail(
                    "Nieuwe afspraak - " . htmlspecialchars($appointment['client_name']),
                    $ownerMessage,
                    'Nieuwe afspraak ontvangen',
                    '<EMAIL>',
                    'Jacqueline Tjassens'
                );
                
                $emailSent = $clientEmailSent || $ownerEmailSent;
                
                // Mark as sent in database to prevent duplicates
                if ($emailSent) {
                    $updateStmt = $pdo->prepare("UPDATE appointments SET email_sent = 1 WHERE id = ?");
                    $updateStmt->execute([$appointment_id]);
                    $log->info('Confirmation email sent and marked in database', ['id' => $appointment_id]);
                } else {
                    $log->error('Failed to send confirmation email', ['id' => $appointment_id]);
                }
            } else if ($appointment && isset($appointment['email_sent']) && $appointment['email_sent'] == 1) {
                $log->info('Email already sent for this appointment', ['id' => $appointment_id]);
                $emailSent = true;
            }
        }
        
        $log->info('Appointment details retrieved', [
            'encrypted_id' => $encrypted_id,
            'decrypted_id' => $appointment_id,
            'found' => !empty($appointment),
            'email_sent' => $emailSent
        ]);
    } catch (Exception $e) {
        $log->error('Error retrieving appointment', [
            'encrypted_id' => $encrypted_id,
            'error' => $e->getMessage()
        ]);
    }
}

include 'includes/header.php';
include 'includes/menu.php';

renderBreadcrumb(
    $page_title,
    'booking-bg',
    [['title' => 'Afspraak maken', 'url' => 'afspraak-maken.php']]
);
?>

<div class="pa-confirmation spacer-top spacer-bottom">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="confirmation-card">
                    <?php if ($appointment): ?>
                        <div class="card-header text-center">
                            <h2>Bedankt voor uw afspraak!</h2>
                        </div>
                        <div class="card-body">
                            <?php if ($appointment['status'] === 'paid'): ?>
                                <div class="alert alert-success">
                                    <strong>Betaling geslaagd!</strong> Uw afspraak is bevestigd.
                                </div>
                            <?php elseif ($appointment['status'] === 'pending'): ?>
                                <div class="alert alert-warning">
                                    <strong>Betaling in behandeling.</strong> Uw afspraak wordt bevestigd zodra de betaling is voltooid.
                                </div>
                            <?php elseif ($appointment['status'] === 'failed'): ?>
                                <div class="alert alert-danger">
                                    <strong>Betaling mislukt.</strong> Er is een probleem opgetreden met uw betaling. Neem contact met ons op.
                                </div>
                            <?php endif; ?>
                            
                            <h4>Afspraakdetails</h4>
                            <table class="table">
                                <tr>
                                    <th>Dienst:</th>
                                    <td>
                                        <?php 
                                        // Get service name based on service_type and service_id
                                        $service_name = '';
                                        
                                        if ($appointment['service_type'] === 'behandeling') {
                                            // List of treatments
                                            $behandelingen = [
                                                1 => ['name' => 'Abhyanga massage', 'price' => 75.00],
                                                2 => ['name' => 'Kruidenstempelmassage 60 min', 'price' => 65.00],
                                                3 => ['name' => 'Kruidenstempelmassage 90 min', 'price' => 85.00],
                                                4 => ['name' => 'Ontspanningsmassage 30 min', 'price' => 35.00],
                                                5 => ['name' => 'Ontspanningsmassage 60 min', 'price' => 65.00],
                                                6 => ['name' => 'Ontspanningsmassage 90 min', 'price' => 85.00],
                                                7 => ['name' => 'Stemvorktherapie', 'price' => 65.00],
                                                8 => ["name" => "Tienermassage", "price" => 45.00],
                                                9 => ["name" => "Go4Balance Consult", "price" => 45.00],
                                                10 => ["name" => "Indian Foot Massage", "price" => 55.00]
                                            ];
                                            
                                            $service_id = (int)$appointment['service_id'];
                                            $service_name = isset($behandelingen[$service_id]) ? $behandelingen[$service_id]['name'] : 'Onbekende behandeling';
                                            
                                        } elseif ($appointment['service_type'] === 'workshop') {
                                            // List of workshops
                                            $workshops = [
                                                9 => ['name' => 'Mild in het wild Compassiewandeling', 'price' => 12.50],
                                                10 => ['name' => 'Workshop Uit je hoofd in je lijf', 'price' => 39.95]
                                            ];
                                            
                                            $service_id = (int)$appointment['service_id'];
                                            $service_name = isset($workshops[$service_id]) ? $workshops[$service_id]['name'] : 'Onbekende workshop';
                                        }
                                        
                                        echo htmlspecialchars($service_name ?: $appointment['service_name'] ?? '');
                                        ?>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Datum:</th>
                                    <td><?php echo date('d-m-Y', strtotime($appointment['appointment_date'])); ?></td>
                                </tr>
                                <tr>
                                    <th>Tijd:</th>
                                    <td><?php echo htmlspecialchars($appointment['appointment_time']); ?></td>
                                </tr>
                                <tr>
                                    <th>Naam:</th>
                                    <td><?php echo htmlspecialchars($appointment['client_name']); ?></td>
                                </tr>
                                <tr>
                                    <th>Email:</th>
                                    <td><?php echo htmlspecialchars($appointment['client_email']); ?></td>
                                </tr>
                                <?php if (!empty($appointment['client_phone'])): ?>
                                <tr>
                                    <th>Telefoon:</th>
                                    <td><?php echo htmlspecialchars($appointment['client_phone']); ?></td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <th>Status:</th>
                                    <td>
                                        <?php 
                                        switch($appointment['status']) {
                                            case 'paid':
                                                echo '<span class="badge bg-success">Betaald</span>';
                                                break;
                                            case 'pending':
                                                echo '<span class="badge bg-warning">In behandeling</span>';
                                                break;
                                            case 'failed':
                                                echo '<span class="badge bg-danger">Mislukt</span>';
                                                break;
                                            default:
                                                echo '<span class="badge bg-secondary">'.htmlspecialchars($appointment['status']).'</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            </table>
                            
                            <p class="mt-4">Een bevestiging is verzonden naar uw e-mailadres. Bewaar deze e-mail goed.</p>
                            
                            <div class="text-center mt-4">
                                <a href="index.php" class="pa-btn">Terug naar home</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="card-header text-center">
                            <i class="fa fa-exclamation-circle error-icon"></i>
                            <h2>Afspraak niet gevonden</h2>
                        </div>
                        <div class="card-body">
                            <p>De opgevraagde afspraak kon niet worden gevonden. Controleer of u de juiste link heeft gebruikt.</p>
                            <div class="text-center mt-4">
                                <a href="afspraak-maken.php" class="pa-btn">Nieuwe afspraak maken</a>
                                <a href="index.php" class="pa-btn">Terug naar home</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
