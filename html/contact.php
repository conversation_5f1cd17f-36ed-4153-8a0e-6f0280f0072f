<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/breadcrumb.php';
require_once 'includes/recaptcha_config.php';

$page_title = "Contact";
?>

<?php include 'includes/header.php'; ?>
<?php include 'includes/menu.php'; ?>

<?php renderBreadcrumb($page_title, 'contact-bg'); ?>

<!-- contact start -->
<div class="pa-contact spacer-top">
    <div class="container">
        <div class="row">
            <div class="col-md-7">
                <div class="pa-contact-map">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2455.5533253901747!2d4.4925799!3d52.0602298!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47c5c6f4c4c4c4c4%3A0x4c4c4c4c4c4c4c4c!2sScheglaan%2012%2C%202718%20KZ%20Zoetermeer!5e0!3m2!1sen!2snl!4v1699000000000!5m2!1sen!2snl"
                        width="100%" 
                        height="450" 
                        style="border:0;" 
                        allowfullscreen="" 
                        loading="lazy" 
                        referrerpolicy="no-referrer-when-downgrade"
                        aria-hidden="false" 
                        tabindex="0">
                    </iframe>
                </div>
            </div>
            <div class="col-md-5">
                <div class="pa-contact-form">
                    <form method="post" action="process_contact.php">
                        <div class="form-group">
                            <input type="text" placeholder="Vul uw naam in" name="full_name" id="full_name" class="require" />
                            <div class="invalid-feedback">Vul uw naam in</div>
                        </div>
                        
                        <div class="form-group">
                            <input type="text" placeholder="Vul uw email adres in" name="email" id="email" 
                                   class="require" data-valid="email" />
                            <div class="invalid-feedback">Vul een geldig e-mailadres in</div>
                        </div>
                        
                        <div class="form-group">
                            <input type="text" placeholder="Vul het onderwerp in" name="subject" id="subject" class="require" />
                            <div class="invalid-feedback">Vul het onderwerp in</div>
                        </div>
                        
                        <div class="form-group">
                            <textarea placeholder="Vul uw bericht in" name="message" id="message" class="require"
                                      maxlength="500"></textarea>
                            <div class="invalid-feedback">Vul uw bericht in</div>
                            <div class="text-end mt-1">
                                <small class="char-count"><span id="messageCharCount">0</span>/500</small>
                            </div>
                        </div>

                        <!-- Honeypot field (hidden from users, should remain empty) -->
                        <div class="honeypot-field">
                            <input type="text" name="website" tabindex="-1" autocomplete="off" aria-hidden="true" />
                        </div>

                        <!-- Add reCAPTCHA -->
                        <input type="hidden" id="recaptcha_response" name="g-recaptcha-response">

                        <button type="submit" class="pa-btn submitForm">Verstuur</button>
                        <div class="response"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- contact end -->

<!-- contact detail start -->
<div class="pa-contact-detail">
    <div class="container">
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="pa-contact-box">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid" viewBox="0 0 40.062 40">
                            <path d="M32.049,28.887 L39.862,38.730 C40.049,38.965 40.085,39.287 39.954,39.556 C39.824,39.827 39.550,39.998 39.250,39.998 L0.811,39.998 C0.511,39.998 0.238,39.827 0.107,39.556 C-0.023,39.287 0.012,38.965 0.199,38.730 L8.012,28.887 C8.160,28.700 8.385,28.591 8.624,28.591 L15.468,28.591 L9.625,19.428 C8.254,17.373 7.530,14.976 7.530,12.497 C7.530,5.605 13.138,-0.001 20.031,-0.001 C26.924,-0.001 32.531,5.605 32.531,12.497 C32.531,14.976 31.807,17.373 30.436,19.428 L24.593,28.591 L31.438,28.591 C31.676,28.591 31.901,28.700 32.049,28.887 ZM37.633,38.435 L34.967,35.075 L30.242,35.075 L32.909,38.435 L37.633,38.435 ZM30.914,38.435 L28.248,35.075 L11.814,35.075 L9.147,38.435 L30.914,38.435 ZM2.428,38.435 L7.153,38.435 L9.819,35.075 L5.095,35.075 L2.428,38.435 ZM9.001,30.154 L6.335,33.514 L11.059,33.514 L13.725,30.154 L9.001,30.154 ZM13.054,33.514 L18.606,33.514 L16.464,30.154 L15.720,30.154 L13.054,33.514 ZM29.123,18.580 C29.126,18.576 29.129,18.571 29.132,18.567 C30.334,16.768 30.969,14.669 30.969,12.497 C30.969,6.467 26.062,1.560 20.031,1.560 C13.999,1.560 9.093,6.467 9.093,12.497 C9.093,14.669 9.727,16.768 10.929,18.567 C10.932,18.571 10.935,18.576 10.938,18.580 C11.950,20.169 19.037,31.282 20.031,32.841 C20.426,32.222 28.610,19.385 29.123,18.580 ZM24.341,30.154 L23.597,30.154 L21.455,33.514 L27.007,33.514 L24.341,30.154 ZM26.336,30.154 L29.002,33.514 L33.727,33.514 L31.060,30.154 L26.336,30.154 ZM19.627,29.105 C19.258,28.882 19.139,28.402 19.361,28.032 L22.249,23.242 C22.472,22.872 22.952,22.753 23.322,22.977 C23.691,23.199 23.810,23.679 23.587,24.049 L20.700,28.839 C20.478,29.207 19.998,29.329 19.627,29.105 ZM24.718,20.310 C25.150,20.310 25.500,20.659 25.500,21.092 C25.500,21.523 25.150,21.873 24.718,21.873 C24.287,21.873 23.937,21.523 23.937,21.092 C23.937,20.659 24.287,20.310 24.718,20.310 ZM12.218,12.497 C12.218,8.190 15.723,4.685 20.031,4.685 C24.339,4.685 27.844,8.190 27.844,12.497 C27.844,16.837 24.320,20.310 20.031,20.310 C15.742,20.310 12.218,16.838 12.218,12.497 ZM26.281,12.497 C26.281,9.051 23.477,6.248 20.031,6.248 C16.584,6.248 13.780,9.051 13.780,12.497 C13.780,15.976 16.606,18.748 20.031,18.748 C23.454,18.748 26.281,15.976 26.281,12.497 Z"></path>
                        </svg>
                    </span>
                    <h4>Adres</h4>
                    <p><?php echo htmlspecialchars($config['site']['address']); ?></p>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="pa-contact-box">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid" viewBox="0 0 24.969 26">
                            <path d="M24.376,12.318 C24.334,12.323 24.297,12.328 24.255,12.328 C23.911,12.328 23.610,12.075 23.552,11.714 C23.135,9.157 21.972,6.825 20.186,4.971 C18.400,3.121 16.154,1.909 13.692,1.475 C13.301,1.404 13.042,1.025 13.106,0.619 C13.174,0.219 13.539,-0.057 13.924,0.016 C16.683,0.503 19.193,1.853 21.190,3.927 C23.187,6.002 24.487,8.608 24.958,11.473 C25.026,11.873 24.762,12.257 24.376,12.318 ZM13.729,4.728 C15.404,5.024 16.931,5.848 18.141,7.105 C19.351,8.361 20.144,9.947 20.429,11.687 C20.493,12.092 20.234,12.476 19.843,12.543 C19.800,12.548 19.763,12.553 19.721,12.553 C19.378,12.553 19.076,12.301 19.018,11.939 C18.781,10.501 18.131,9.195 17.127,8.153 C16.123,7.110 14.865,6.430 13.481,6.188 C13.090,6.122 12.836,5.733 12.905,5.332 C12.973,4.932 13.338,4.663 13.729,4.728 ZM8.994,9.618 C8.603,10.029 8.202,10.458 7.768,10.869 C8.091,11.609 8.535,12.340 9.195,13.206 C10.558,14.946 11.980,16.290 13.544,17.321 C13.697,17.421 13.877,17.514 14.067,17.613 C14.199,17.684 14.336,17.755 14.474,17.832 L16.144,16.103 C16.677,15.550 17.301,15.259 17.951,15.259 C18.606,15.259 19.224,15.555 19.737,16.109 L22.638,19.133 C23.172,19.681 23.452,20.333 23.452,21.015 C23.452,21.688 23.177,22.348 22.654,22.913 C22.427,23.160 22.194,23.396 21.967,23.621 C21.634,23.955 21.317,24.268 21.042,24.614 C21.037,24.625 21.026,24.630 21.021,24.642 C20.218,25.546 19.193,26.002 17.972,26.002 C17.866,26.002 17.750,25.997 17.639,25.991 C15.827,25.870 14.204,25.162 12.989,24.564 C9.808,22.968 7.023,20.702 4.709,17.832 C2.801,15.450 1.522,13.228 0.667,10.830 C0.318,9.854 -0.137,8.361 -0.010,6.759 C0.075,5.765 0.460,4.910 1.137,4.208 L2.934,2.325 L2.944,2.314 C3.473,1.787 4.096,1.509 4.741,1.509 C5.385,1.509 5.998,1.787 6.511,2.314 C6.854,2.643 7.187,2.995 7.504,3.335 C7.663,3.511 7.832,3.686 7.996,3.856 L9.438,5.355 C10.548,6.506 10.548,7.999 9.438,9.151 C9.290,9.305 9.142,9.464 8.994,9.618 ZM8.434,6.402 L6.992,4.905 C6.823,4.723 6.654,4.548 6.490,4.372 C6.167,4.027 5.861,3.702 5.538,3.390 L5.523,3.373 C5.353,3.198 5.084,2.989 4.751,2.989 C4.482,2.989 4.207,3.126 3.948,3.379 L2.146,5.250 C1.707,5.706 1.470,6.238 1.417,6.879 C1.338,7.883 1.517,8.949 2.009,10.314 C2.807,12.553 4.012,14.638 5.813,16.894 C7.996,19.598 10.622,21.733 13.613,23.236 C14.706,23.779 16.165,24.416 17.734,24.521 C17.814,24.526 17.898,24.526 17.977,24.526 C18.802,24.526 19.430,24.247 19.964,23.653 C20.292,23.253 20.641,22.897 20.984,22.556 C21.211,22.326 21.423,22.118 21.629,21.887 C22.168,21.305 22.168,20.729 21.623,20.169 L18.701,17.135 C18.537,16.949 18.268,16.735 17.940,16.735 C17.608,16.735 17.322,16.960 17.142,17.146 L15.335,19.022 C15.245,19.116 14.971,19.401 14.532,19.401 C14.358,19.401 14.189,19.357 14.009,19.265 C13.988,19.253 13.967,19.237 13.946,19.225 C13.792,19.127 13.613,19.033 13.422,18.935 C13.211,18.825 12.989,18.709 12.772,18.568 C11.076,17.453 9.539,15.999 8.080,14.133 L8.075,14.128 C7.272,13.069 6.743,12.179 6.368,11.237 L6.352,11.187 C6.257,10.874 6.173,10.413 6.627,9.941 C6.633,9.931 6.643,9.925 6.654,9.914 C7.113,9.487 7.536,9.047 7.980,8.575 C8.133,8.421 8.281,8.262 8.434,8.103 C8.994,7.522 8.994,6.984 8.434,6.402 Z"></path>
                        </svg>
                    </span>
                    <h4>Telefoonnummer</h4>
                    <p><?php echo $config['site']['phone']; ?></p>
                    <br/>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="pa-contact-box">
                    <span>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid" viewBox="0 0 20 16">
                            <path d="M13.104,7.998 L20.000,1.129 L20.000,14.868 L13.104,7.998 ZM1.135,-0.002 L18.865,-0.002 L10.000,8.829 L1.135,-0.002 ZM-0.000,14.868 L-0.000,1.129 L6.896,7.998 L-0.000,14.868 ZM10.000,11.091 L11.969,9.129 L18.865,15.998 L1.135,15.998 L8.031,9.129 L10.000,11.091 Z"></path>
                        </svg>
                    </span>
                    <h4>Email</h4>
                    <p><?php echo $config['site']['email']; ?></p>
                    <br/>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- contact detail end -->

<?php include 'includes/footer.php'; ?>
<?php include 'includes/login-modals.php'; ?>

<!-- reCAPTCHA v3 Script -->
<?php echo RecaptchaConfig::getScriptTag(); ?>
<script>
    // Make site key available to JavaScript
    const RECAPTCHA_SITE_KEY = '<?php echo RecaptchaConfig::getSiteKey(); ?>';

    // Execute reCAPTCHA on page load
    grecaptcha.ready(function() {
        // Execute reCAPTCHA with site key
        grecaptcha.execute(RECAPTCHA_SITE_KEY, {action: 'contact'})
            .then(function(token) {
                // Add the token to your form
                document.getElementById('recaptcha_response').value = token;
            });
    });

    // Handle subject from URL parameter
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const subject = urlParams.get('subject');
        
        if (subject) {
            const subjectField = document.getElementById('subject');
            if (subjectField) {
                subjectField.value = decodeURIComponent(subject);
            }
        }
    });
</script>
