<?php
// Direct error logging to ensure we capture fatal errors
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/php_errors.log');
error_log('Payment process started - ' . date('Y-m-d H:i:s'));

// Set error handling
ini_set('display_errors', 0);
error_reporting(E_ALL);
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error: [$errno] $errstr in $errfile on line $errline");
    global $log;
    if (isset($log)) {
        $log->error('PHP Error', [
            'errno' => $errno,
            'errstr' => $errstr,
            'errfile' => $errfile,
            'errline' => $errline
        ]);
    }
    return true;
});

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("FATAL ERROR: " . print_r($error, true));
    }
});

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'includes/Logger.php';
$log = Logger::get('payment_process');
$log->info('Starting includes');

try {
    $log->info('Loading config.php');
    require_once 'includes/config.php';
    
    $log->info('Loading db.php');
    require_once 'includes/db.php';
    // Make sure $pdo is available in this scope
    global $pdo;
    
    $log->info('Loading cart_handler.php');
    require_once 'includes/cart_handler.php';
    
    $log->info('Loading breadcrumb.php');
    require_once 'includes/breadcrumb.php';
    
    $log->info('Loading mollie_config.php');
    require_once 'includes/mollie_config.php';
    
    $log->info('All includes loaded successfully');
} catch (Exception $e) {
    $log->error('Failed loading includes', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    die('Error loading required files');
}

$log = Logger::get('payment_process');
$log->info('Request received', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => $_SERVER['CONTENT_TYPE'] ?? 'not set',
    'post_data' => $_POST
]);

// Handle POST request for payment processing
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log('POST request received: ' . print_r($_POST, true));
    
    // Simple test endpoint
    if (isset($_POST['test']) && $_POST['test'] === 'ping') {
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'pong']);
        exit;
    }
    
    $log->info('POST request detected', [
        'action' => $_POST['action'] ?? 'not set',
        'headers' => getallheaders()
    ]);
    
    if (isset($_POST['action']) && $_POST['action'] === 'process_payment') {
        $log->info('Processing payment action detected');
        header('Content-Type: application/json');
        
        $log = Logger::get('payment_process');
        $log->info('Payment process started', ['session_id' => session_id()]);
        
        try {
            // CSRF validation
            if (!isset($_POST['csrf_token']) || !isset($_SESSION['csrf_token']) || 
                $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                $log->error('CSRF validation failed', [
                    'provided_token' => $_POST['csrf_token'] ?? 'not_set',
                    'session_token' => $_SESSION['csrf_token'] ?? 'not_set'
                ]);
                throw new Exception('Invalid CSRF token');
            }

            // Get delivery method
            $delivery_method = isset($_POST['delivery_method']) ? $_POST['delivery_method'] : 'shipping';
            $log->info('Delivery method', ['method' => $delivery_method]);

            // Validate required fields based on delivery method
            $required_fields = ['name', 'email'];
            
            // Add address fields if shipping is selected
            if ($delivery_method === 'shipping') {
                $required_fields = array_merge($required_fields, ['address', 'postal_code', 'city']);
            }
            
            $missing_fields = [];
            foreach ($required_fields as $field) {
                if (empty($_POST[$field])) {
                    $missing_fields[] = $field;
                }
            }
            
            if (!empty($missing_fields)) {
                $log->error('Missing required fields', ['missing_fields' => $missing_fields]);
                throw new Exception("Missing required fields: " . implode(', ', $missing_fields));
            }

            // Get cart items and calculate total
            $cart_items = getCartItems();
            $log->info('Cart items retrieved', ['count' => count($cart_items)]);

            if (empty($cart_items)) {
                $log->error('Empty cart during payment process');
                throw new Exception('Shopping cart is empty');
            }

            // Log calculation steps
            $log->info('Calculating totals');

            $subtotal = 0;
            foreach ($cart_items as $item) {
                $subtotal += $item['price'] * $item['quantity'];
            }

            // Apply shipping cost only if shipping is selected
            $shipping = ($delivery_method === 'shipping') ? 4.95 : 0;
            $total = $subtotal + $shipping;

            $log->info('Totals calculated', [
                'subtotal' => $subtotal,
                'shipping' => $shipping,
                'total' => $total,
                'delivery_method' => $delivery_method
            ]);

            // Generate unique order ID
            $order_id = uniqid('ORDER_');
            $log->info('Order created', [
                'order_id' => $order_id,
                'total_amount' => $total,
                'items_count' => count($cart_items)
            ]);

            // Create order in database
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO orders (
                        user_id,
                        status,
                        payment_id,
                        shipping_address,
                        total_amount,
                        order_items,
                        delivery_method
                    ) VALUES (
                        NULL,
                        'pending',
                        :order_id,
                        :shipping_address,
                        :total_amount,
                        :order_items,
                        :delivery_method
                    )
                ");
                
                // Create address JSON
                $address = json_encode([
                    'name' => $_POST['name'],
                    'email' => $_POST['email'],
                    'address' => $delivery_method === 'shipping' ? $_POST['address'] : 'Ophalen in Zoetermeer',
                    'postal_code' => $delivery_method === 'shipping' ? $_POST['postal_code'] : '',
                    'city' => $delivery_method === 'shipping' ? $_POST['city'] : 'Zoetermeer',
                    'delivery_method' => $delivery_method
                ]);
                
                // Create order items JSON
                $order_items_json = json_encode($cart_items);
                
                $stmt->execute([
                    ':order_id' => $order_id,
                    ':shipping_address' => $address,
                    ':total_amount' => $total,
                    ':order_items' => $order_items_json,
                    ':delivery_method' => $delivery_method
                ]);
                
                $orderId = $pdo->lastInsertId();
                
                $log->info('Order saved to database', [
                    'order_db_id' => $orderId
                ]);
                
                // Create payment description and redirect URL
                $payment_description = "Order " . $orderId;

                // Encrypt the order ID for the redirect URL
                $encryption_key = ENCRYPTION_KEY;
                $encrypted_id = base64_encode(openssl_encrypt(
                    $orderId,
                    'AES-256-CBC',
                    $encryption_key,
                    0,
                    substr(hash('sha256', $encryption_key), 0, 16)
                ));

                $redirect_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
                                "://" . $_SERVER['HTTP_HOST'] . "/payment_return.php?order=" . urlencode($encrypted_id);

                $log->info('Attempting to create payment link', [
                    'order_id' => $orderId,
                    'total' => $total,
                    'description' => $payment_description,
                    'redirect_url' => $redirect_url
                ]);
                
                // Create payment link
                $payment_url = createPaymentLink(
                    $orderId,
                    $total,
                    $payment_description,
                    $redirect_url,
                    $metadata
                );

                if (empty($payment_url)) {
                    $log->error('Empty payment URL received', [
                        'order_id' => $orderId
                    ]);
                    throw new Exception('Failed to create payment URL');
                }

                // Clear the cart after successful order creation
                clearCart();
                $log->info('Cart cleared after order creation');

                $log->info('Payment link created successfully', [
                    'order_id' => $orderId,
                    'payment_url' => $payment_url
                ]);

                echo json_encode([
                    'success' => true,
                    'redirectUrl' => $payment_url,
                    'message' => 'Payment processing initiated'
                ]);
                exit;

            } catch (Exception $e) {
                $log->error('Payment creation failed', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'order_id' => $orderId ?? null
                ]);
                throw new Exception('Failed to create payment link: ' . $e->getMessage());
            }
        } catch (Exception $e) {
            $log->error('Payment processing failed', [
                'error' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'order_id' => $orderId ?? null
            ]);
            
            echo json_encode([
                'success' => false,
                'message' => 'Er is een fout opgetreden bij het verwerken van de betaling: ' . $e->getMessage()
            ]);
            exit;
        }
    } else {
        $log->error('Invalid or missing action in POST request');
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Invalid request'
        ]);
        exit;
    }
}

// Ensure session_id is set
if (!isset($_SESSION['session_id'])) {
    $_SESSION['session_id'] = session_id();
}

$cart_items = getCartItems();
$subtotal = 0;
foreach ($cart_items as $item) {
    $subtotal += $item['price'] * $item['quantity'];
}

$shipping = 4.95;
$total = $subtotal + $shipping;

$page_title = "Winkelwagen";
?>
<?php include 'includes/header.php'; ?>
<?php include 'includes/menu.php'; ?>

<?php 
renderBreadcrumb(
    $page_title,
    'shop-bg',
    []
); 
?>

<!-- cart wizard start -->
<div class="pa-cart spacer-top spacer-bottom">
    <div class="container">
        <!-- Wizard Progress -->
        <div class="wizard-progress mb-4">
            <div class="step active" data-step="1">
                <div class="step-icon">1</div>
                <div class="step-label">Winkelwagen</div>
            </div>
            <div class="step" data-step="2">
                <div class="step-icon">2</div>
                <div class="step-label">Gegevens en verzendmethode</div>
            </div>
            <div class="step" data-step="3">
                <div class="step-icon">3</div>
                <div class="step-label">Betaling</div>
            </div>
            <div class="progress-bar"></div>
        </div>

        <!-- Step 1: Cart Contents -->
        <div class="wizard-step" id="step-1">
            <div class="row">
                <div class="col-md-12">
                    <div class="pa-cart-box">
                        <table class="cart-table">
                            <thead>
                                <tr>
                                    <th>Afbeelding</th>
                                    <th>Product</th>
                                    <th>Prijs per stuk</th>
                                    <th>Aantal</th>
                                    <th>Totaal</th>
                                    <th>Actie</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if(empty($cart_items)): ?>
                                    <tr>
                                        <td colspan="6" class="text-center">
                                            <p>Je winkelwagen is leeg</p>
                                            <a href="shop.php" class="pa-btn">Verder winkelen</a>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach($cart_items as $item): ?>
                                        <tr>
                                            <td>
                                                <div class="pa-cart-img">
                                                    <img src="<?php echo htmlspecialchars($item['image']); ?>" 
                                                        alt="<?php echo htmlspecialchars($item['name']); ?>" 
                                                        class="img-fluid" />
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($item['name']); ?></td>
                                            <td>€<?php echo number_format($item['price'], 2, ',', '.'); ?></td>
                                            <td>
                                                <div class="pa-cart-quantity">
                                                    <button class="pa-sub" data-id="<?php echo $item['product_id']; ?>"></button>
                                                    <input type="number" 
                                                        value="<?php echo $item['quantity']; ?>" 
                                                        min="1" 
                                                        max="50000" 
                                                        data-id="<?php echo $item['product_id']; ?>" />
                                                    <button class="pa-add" data-id="<?php echo $item['product_id']; ?>"></button>
                                                </div>
                                            </td>
                                            <td>€<?php echo number_format($item['price'] * $item['quantity'], 2, ',', '.'); ?></td>
                                            <td>
                                                <button class="remove-item" data-id="<?php echo $item['product_id']; ?>">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                                                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                    
                                    <!-- Cart Totals -->
                                    <tr>
                                        <td colspan="6">
                                            <div class="cart-totals">
                                                <div class="totals-line">
                                                    <span>Subtotaal:</span>
                                                    <span>€<?php echo number_format($subtotal, 2, ',', '.'); ?></span>
                                                </div>
                                                <div class="totals-line shipping-costs" style="display: none;">
                                                    <span>Verzendkosten (optioneel):</span>
                                                    <span>€<?php echo number_format($shipping, 2, ',', '.'); ?></span>
                                                </div>
                                                <div class="totals-line total">
                                                    <span>Totaal:</span>
                                                    <span>€<?php echo number_format($subtotal, 2, ',', '.'); ?></span>
                                                </div>
                                            </div>
                                            <div class="cart-actions">
                                                <a href="shop.php" class="pa-btn">Verder winkelen</a>
                                                <button type="button" class="pa-btn next-step">Afrekenen</button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                     </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Customer Information -->
        <div class="wizard-step" id="step-2">
            <form id="contact-details-form" method="POST" onsubmit="event.preventDefault(); wizard.processPayment(new FormData(this)); return false;">
                <!-- Hidden fields -->
                <input type="hidden" name="action" value="process_payment">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">
                     
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Naam*</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email">E-mail*</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                </div>

                <!-- Delivery method selection -->
                <div class="row mt-4">
                    <div class="col-12">
                        <h4>Bezorgmethode</h4>
                        <div class="delivery-options">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="delivery_method" id="delivery_shipping" value="shipping" checked>
                                <label class="form-check-label" for="delivery_shipping">
                                    Verzenden (€<?php echo number_format($shipping, 2, ',', '.'); ?>)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="delivery_method" id="delivery_pickup" value="pickup">
                                <label class="form-check-label" for="delivery_pickup">
                                    Ophalen in Zoetermeer (Gratis)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3" id="shipping-address-fields">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="address">Adres*</label>
                            <input type="text" class="form-control" id="address" name="address">
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="postal_code">Postcode*</label>
                            <input type="text" class="form-control" id="postal_code" name="postal_code">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="city">Plaats*</label>
                            <input type="text" class="form-control" id="city" name="city">
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="cart-actions">
                            <button type="button" class="pa-btn prev-step">Terug</button>
                            <button type="submit" class="pa-btn">Betalen</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Step 3: Loading/Processing -->
        <div class="wizard-step" id="step-3">
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="sr-only">Laden...</span>
                </div>
                <p class="mt-3">Uw betaling wordt verwerkt...</p>
            </div>
        </div>
    </div>
</div>
<!-- cart wizard end -->
<?php include 'includes/footer.php'; ?>

<!-- Add this JavaScript to handle the delivery method change -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const deliveryShipping = document.getElementById('delivery_shipping');
    const deliveryPickup = document.getElementById('delivery_pickup');
    const shippingAddressFields = document.getElementById('shipping-address-fields');
    const addressInput = document.getElementById('address');
    const postalCodeInput = document.getElementById('postal_code');
    const cityInput = document.getElementById('city');
    
    // Function to update totals
    function updateTotals() {
        const subtotal = <?php echo $subtotal; ?>;
        const shipping = <?php echo $shipping; ?>;
        let total = subtotal;
        const shippingCostsRow = document.querySelector('.shipping-costs');
        
        // Always show shipping costs row for visibility
        shippingCostsRow.style.display = 'flex';
        
        // Update shipping cost display and total based on delivery method
        if (deliveryShipping.checked) {
            // Add shipping cost for shipping method
            total = subtotal + shipping;
            document.querySelector('.shipping-costs span:last-child').textContent = 
                '€' + shipping.toLocaleString('nl-NL', {minimumFractionDigits: 2, maximumFractionDigits: 2}).replace('.', ',');
        } else if (deliveryPickup.checked) {
            // No shipping cost for pickup
            total = subtotal; // Do not add shipping cost
            document.querySelector('.shipping-costs span:last-child').textContent = 'Gratis';
        }
        
        // Update total display
        document.querySelector('.totals-line.total span:last-child').textContent = 
            '€' + total.toLocaleString('nl-NL', {minimumFractionDigits: 2, maximumFractionDigits: 2}).replace('.', ',');
        
        console.log('Delivery method:', deliveryShipping.checked ? 'shipping' : 'pickup');
        console.log('Subtotal:', subtotal);
        console.log('Shipping:', deliveryShipping.checked ? shipping : 0);
        console.log('Total:', total);
    }
    
    // Function to toggle address fields visibility but keep them required
    function toggleAddressFields() {
        if (deliveryPickup.checked) {
            // Keep address fields visible
            shippingAddressFields.style.display = 'flex';
            
            // Keep address fields required
            addressInput.required = true;
            postalCodeInput.required = true;
            cityInput.required = true;
        } else {
            // Regular shipping address fields
            shippingAddressFields.style.display = 'flex';
            addressInput.required = true;
            postalCodeInput.required = true;
            cityInput.required = true;
        }
        updateTotals();
    }
    
    // Add event listeners
    deliveryShipping.addEventListener('change', function() {
        console.log('Delivery method changed to shipping');
        toggleAddressFields();
    });

    deliveryPickup.addEventListener('change', function() {
        console.log('Delivery method changed to pickup');
        toggleAddressFields();
    });
    
    // Initialize on page load
    toggleAddressFields();
});

// Add this to your existing JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Get the checkout form
    const checkoutForm = document.getElementById('contact-details-form');
    
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Create FormData object
            const formData = new FormData(this);
            
            // Add delivery method to form data
            const deliveryMethod = document.querySelector('input[name="delivery_method"]:checked').value;
            formData.set('delivery_method', deliveryMethod);
            
            console.log('Form submission - delivery method:', deliveryMethod);
            console.log('Form data:', Object.fromEntries(formData.entries()));
            
            // Process payment
            wizard.processPayment(formData);
        });
    }
});
</script>
