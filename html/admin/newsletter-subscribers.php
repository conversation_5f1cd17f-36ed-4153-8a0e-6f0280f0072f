<?php
$page_title = "Nieuwsbrief Abonnees";
require_once '../includes/admin-auth.php';
require_once '../includes/config.php';
require_once '../includes/db.php';

$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed");
}

// Handle actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $id = $_POST['id'] ?? '';
    $email = $_POST['email'] ?? '';
    
    try {
        switch ($action) {
            case 'add':
                $first_name = trim($_POST['first_name'] ?? '');
                $last_name = trim($_POST['last_name'] ?? '');
                
                // Validate email
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception("Ongeldig e-mailadres.");
                }
                
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT id, status FROM newsletter_subscriptions WHERE email = ?");
                $stmt->execute([$email]);
                $existing = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($existing) {
                    if ($existing['status'] === 'active') {
                        throw new Exception("Dit e-mailadres is al ingeschreven.");
                    } else {
                        // Reactivate subscription with updated names
                        $stmt = $pdo->prepare("UPDATE newsletter_subscriptions SET status = 'active', subscribed_at = NOW(), unsubscribed_at = NULL, first_name = ?, last_name = ? WHERE email = ?");
                        $stmt->execute([$first_name, $last_name, $email]);
                        $_SESSION['success_message'] = "E-mailadres succesvol geheractiveerd.";
                    }
                } else {
                    // Insert new subscription
                    $stmt = $pdo->prepare("INSERT INTO newsletter_subscriptions (email, first_name, last_name, status, subscribed_at) VALUES (?, ?, ?, 'active', NOW())");
                    $stmt->execute([$email, $first_name, $last_name]);
                    $_SESSION['success_message'] = "E-mailadres succesvol toegevoegd.";
                }
                break;
                
            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM newsletter_subscriptions WHERE id = ?");
                $stmt->execute([$id]);
                $_SESSION['success_message'] = "Abonnee succesvol verwijderd.";
                break;
                
            case 'toggle_status':
                $stmt = $pdo->prepare("SELECT status FROM newsletter_subscriptions WHERE id = ?");
                $stmt->execute([$id]);
                $current = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $newStatus = $current['status'] === 'active' ? 'unsubscribed' : 'active';
                $unsubscribedAt = $newStatus === 'unsubscribed' ? date('Y-m-d H:i:s') : null;
                
                $stmt = $pdo->prepare("UPDATE newsletter_subscriptions SET status = ?, unsubscribed_at = ? WHERE id = ?");
                $stmt->execute([$newStatus, $unsubscribedAt, $id]);
                $_SESSION['success_message'] = "Status succesvol gewijzigd.";
                break;
        }
    } catch (Exception $e) {
        $_SESSION['error_message'] = "Fout: " . $e->getMessage();
    }
    
    header('Location: newsletter-subscribers.php');
    exit;
}

// Get subscribers
$search = $_GET['search'] ?? '';
$status = $_GET['status'] ?? '';

$whereClause = "WHERE 1=1";
$params = [];

if ($search) {
    $whereClause .= " AND email LIKE ?";
    $params[] = "%$search%";
}

if ($status) {
    $whereClause .= " AND status = ?";
    $params[] = $status;
}

$stmt = $pdo->prepare("SELECT * FROM newsletter_subscriptions $whereClause ORDER BY subscribed_at DESC");
$stmt->execute($params);
$subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);

$success_message = $_SESSION['success_message'] ?? null;
$error_message = $_SESSION['error_message'] ?? null;
unset($_SESSION['success_message'], $_SESSION['error_message']);

ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3>Nieuwsbrief Abonnees</h3>
                    <a href="newsletter-send.php" class="btn btn-primary">Nieuwsbrief Versturen</a>
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
                    <?php endif; ?>
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
                    <?php endif; ?>

                    <!-- Filters -->
                    <form method="GET" class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="search" class="form-label">Zoek op e-mailadres</label>
                                <input type="text" name="search" id="search" class="form-control" placeholder="Zoek op e-mailadres..." value="<?= htmlspecialchars($search) ?>" style="height: 38px;">
                            </div>
                            <div class="col-md-4">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-control" style="height: 38px;">
                                    <option value="">Alle statussen</option>
                                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Actief</option>
                                    <option value="unsubscribed" <?= $status === 'unsubscribed' ? 'selected' : '' ?>>Uitgeschreven</option>
                                </select>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-secondary w-100" style="height: 38px;">Filter</button>
                            </div>
                        </div>
                    </form>

                    <!-- Subscribers table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Naam</th>
                                    <th>E-mail</th>
                                    <th>Status</th>
                                    <th>Ingeschreven op</th>
                                    <th>Uitgeschreven op</th>
                                    <th>Acties</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($subscribers as $subscriber): ?>
                                <tr>
                                    <td>
                                        <?php 
                                        $fullName = trim(($subscriber['first_name'] ?? '') . ' ' . ($subscriber['last_name'] ?? ''));
                                        echo $fullName ? htmlspecialchars($fullName) : '-';
                                        ?>
                                    </td>
                                    <td><?= htmlspecialchars($subscriber['email']) ?></td>
                                    <td>
                                        <span class="badge <?= $subscriber['status'] === 'active' ? 'bg-success' : 'bg-secondary' ?> text-white">
                                            <?= $subscriber['status'] === 'active' ? 'Actief' : 'Uitgeschreven' ?>
                                        </span>
                                    </td>
                                    <td><?= date('d-m-Y H:i', strtotime($subscriber['subscribed_at'])) ?></td>
                                    <td><?= $subscriber['unsubscribed_at'] ? date('d-m-Y H:i', strtotime($subscriber['unsubscribed_at'])) : '-' ?></td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id" value="<?= $subscriber['id'] ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">
                                                <?= $subscriber['status'] === 'active' ? 'Uitschrijven' : 'Activeren' ?>
                                            </button>
                                        </form>
                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Weet je zeker dat je deze abonnee wilt verwijderen?')">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?= $subscriber['id'] ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">Verwijderen</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

              <!-- Add new subscriber form -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5>Nieuwe Abonnee Toevoegen</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" class="row g-3">
                                <input type="hidden" name="action" value="add">
                                <div class="col-md-4">
                                    <label for="first_name" class="form-label">Voornaam</label>
                                    <input type="text" name="first_name" id="first_name" class="form-control" placeholder="Voornaam">
                                </div>
                                <div class="col-md-4">
                                    <label for="last_name" class="form-label">Achternaam</label>
                                    <input type="text" name="last_name" id="last_name" class="form-control" placeholder="Achternaam">
                                </div>
                                <div class="col-md-4">
                                    <label for="email" class="form-label">E-mailadres *</label>
                                    <input type="email" name="email" id="email" class="form-control" placeholder="<EMAIL>" required>
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-success">Toevoegen</button>
                                </div>
                            </form>
                        </div>
                    </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
