<?php
$page_title = "Nieuwsbrief Versturen";
require_once '../includes/admin-auth.php';
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/email_helper.php';

$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed");
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $subject = trim($_POST['subject'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $recipients = $_POST['recipients'] ?? 'all';
    $selected_emails = $_POST['selected_emails'] ?? [];
    
    if (empty($subject) || empty($content)) {
        $_SESSION['error_message'] = "Onderwerp en inhoud zijn verplicht.";
    } else {
        try {
            // Get recipients
            if ($recipients === 'all') {
                $stmt = $pdo->prepare("SELECT email, first_name, last_name FROM newsletter_subscriptions WHERE status = 'active'");
                $stmt->execute();
                $subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $emails = array_column($subscribers, 'email');
            } else {
                $emails = $selected_emails;
                // For selected emails, get names from database too
                if (!empty($emails)) {
                    $placeholders = str_repeat('?,', count($emails) - 1) . '?';
                    $stmt = $pdo->prepare("SELECT email, first_name, last_name FROM newsletter_subscriptions WHERE email IN ($placeholders) AND status = 'active'");
                    $stmt->execute($emails);
                    $subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } else {
                    $subscribers = [];
                }
            }
            
            if (empty($emails)) {
                throw new Exception("Geen ontvangers geselecteerd.");
            }
            
            // Create HTML email with website styling
            $emailHtml = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='utf-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <style>
                    body { 
                        font-family: 'Arial', sans-serif; 
                        line-height: 1.6; 
                        color: #333; 
                        margin: 0; 
                        padding: 0; 
                        background-color: #f8f9fa;
                    }
                    .email-container { 
                        max-width: 600px; 
                        margin: 0 auto; 
                        background-color: #ffffff;
                        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                    }
                    .header { 
                        background: linear-gradient(135deg, #d5bc5a 0%, #c9a96e 100%); 
                        padding: 30px 20px; 
                        text-align: center; 
                        color: white;
                    }
                    .header h1 { 
                        color: white; 
                        margin: 0; 
                        font-size: 28px;
                        font-weight: 300;
                        letter-spacing: 1px;
                    }
                    .header p {
                        margin: 10px 0 0 0;
                        font-style: italic;
                        opacity: 0.9;
                        font-size: 16px;
                    }
                    .content { 
                        padding: 40px 30px; 
                        background-color: #ffffff;
                    }
                    .content p {
                        margin-bottom: 20px;
                        font-size: 16px;
                        line-height: 1.7;
                    }
                    .footer { 
                        background: #f8f9fa; 
                        padding: 30px 20px; 
                        text-align: center; 
                        font-size: 14px; 
                        color: #666;
                        border-top: 1px solid #e9ecef;
                    }
                    .footer p {
                        margin: 10px 0;
                    }
                    .footer a {
                        color: #d5bc5a;
                        text-decoration: none;
                        font-weight: 500;
                    }
                    .footer a:hover {
                        text-decoration: underline;
                    }
                    .signature {
                        margin-top: 30px;
                        padding-top: 20px;
                        border-top: 1px solid #e9ecef;
                        font-style: italic;
                        color: #666;
                    }
                    @media only screen and (max-width: 600px) {
                        .email-container {
                            width: 100% !important;
                        }
                        .content {
                            padding: 20px !important;
                        }
                        .header {
                            padding: 20px !important;
                        }
                        .header h1 {
                            font-size: 24px !important;
                        }
                    }
                </style>
            </head>
            <body>
                <div class='email-container'>
                    <div class='header'>
                        <h1>Jacqueline Tjassens</h1>
                        <p>Holistische Therapie</p>
                    </div>
                    <div class='content'>
                        <p>{greeting},</p>
                        " . $content . "
                        <div class='signature'>
                            <p>Veel liefs,<br>
                            <strong>Jacqueline</strong></p>
                        </div>
                    </div>
                    <div class='footer'>
                        <p>Dit bericht is verzonden naar {email} omdat u bent ingeschreven voor onze nieuwsbrief.</p>
                        <p>
                            <a href='https://jacquelinetjassens.com/unsubscribe.php?email=" . urlencode($email) . "'>Uitschrijven</a> | 
                            <a href='https://jacquelinetjassens.com'>Website bezoeken</a>
                        </p>
                        <p style='margin-top: 20px; font-size: 12px; color: #999;'>
                            Jacqueline Tjassens - Holistische Therapie<br>
                            Voor vragen kunt u contact opnemen via de website
                        </p>
                    </div>
                </div>
            </body>
            </html>";
            
            $successCount = 0;
            $failCount = 0;
            
            foreach ($subscribers as $subscriber) {
                $email = $subscriber['email'];
                $firstName = $subscriber['first_name'] ?? '';
                $greeting = $firstName ? "Beste $firstName" : "Beste lezer";
                
                $personalizedHtml = str_replace('{email}', $email, $emailHtml);
                $personalizedHtml = str_replace('{greeting}', $greeting, $personalizedHtml);
                
                if (sendEmail($subject, $personalizedHtml, '', $email)) {
                    $successCount++;
                } else {
                    $failCount++;
                }
                
                usleep(100000); // 0.1 second
            }
            
            $_SESSION['success_message'] = "Nieuwsbrief verzonden naar $successCount ontvangers." . 
                ($failCount > 0 ? " $failCount verzendingen zijn mislukt." : "");
            
        } catch (Exception $e) {
            $_SESSION['error_message'] = "Fout bij verzenden: " . $e->getMessage();
        }
        
        header('Location: newsletter-send.php');
        exit;
    }
}

// Get all active subscribers for selection
$stmt = $pdo->prepare("SELECT id, email FROM newsletter_subscriptions WHERE status = 'active' ORDER BY email");
$stmt->execute();
$subscribers = $stmt->fetchAll(PDO::FETCH_ASSOC);

$success_message = $_SESSION['success_message'] ?? null;
$error_message = $_SESSION['error_message'] ?? null;
unset($_SESSION['success_message'], $_SESSION['error_message']);

ob_start();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3>Nieuwsbrief Versturen</h3>
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success"><?= htmlspecialchars($success_message) ?></div>
                    <?php endif; ?>
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger"><?= htmlspecialchars($error_message) ?></div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="form-group">
                            <label for="subject">Onderwerp *</label>
                            <input type="text" class="form-control" id="subject" name="subject" required>
                        </div>

                        <div class="form-group">
                            <label for="content">Inhoud *</label>
                            <div id="content-editor" style="min-height: 400px;"></div>
                            <textarea id="content" name="content" style="display: none;"></textarea>
                        </div>

                        <div class="form-group">
                            <label>Ontvangers</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipients" id="all" value="all" checked>
                                <label class="form-check-label" for="all">
                                    Alle actieve abonnees (<?= count($subscribers) ?> personen)
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="recipients" id="selected" value="selected">
                                <label class="form-check-label" for="selected">
                                    Geselecteerde abonnees
                                </label>
                            </div>
                        </div>

                        <div id="subscriber-selection" style="display: none;">
                            <div class="form-group">
                                <label>Selecteer abonnees:</label>
                                <div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                                    <?php foreach ($subscribers as $subscriber): ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="selected_emails[]" 
                                               value="<?= htmlspecialchars($subscriber['email']) ?>" 
                                               id="email_<?= $subscriber['id'] ?>">
                                        <label class="form-check-label" for="email_<?= $subscriber['id'] ?>">
                                            <?= htmlspecialchars($subscriber['email']) ?>
                                        </label>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary" onclick="return confirm('Weet je zeker dat je de nieuwsbrief wilt versturen?')">
                            Nieuwsbrief Versturen
                        </button>
                        <a href="newsletter-subscribers.php" class="btn btn-secondary">Terug naar Abonnees</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Quill.js -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>

<script>
// Initialize Quill editor
var quill = new Quill('#content-editor', {
    theme: 'snow',
    modules: {
        toolbar: [
            ['bold', 'italic', 'underline'],
            ['link', 'image'],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            ['clean']
        ]
    }
});

// Handle image uploads
quill.getModule('toolbar').addHandler('image', function() {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();
    
    input.onchange = function() {
        const file = input.files[0];
        if (file) {
            const formData = new FormData();
            formData.append('image', file);
            
            fetch('upload-newsletter-image.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const range = quill.getSelection();
                    quill.insertEmbed(range.index, 'image', data.url);
                }
            });
        }
    };
});

// Sync content with hidden textarea BEFORE form validation
document.querySelector('form').addEventListener('submit', function(e) {
    // Sync Quill content to textarea before validation
    document.getElementById('content').value = quill.root.innerHTML;
    
    // Custom validation instead of HTML5 required
    if (!quill.root.innerHTML.trim() || quill.root.innerHTML.trim() === '<p><br></p>') {
        e.preventDefault();
        alert('Inhoud is verplicht.');
        return false;
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const allRadio = document.getElementById('all');
    const selectedRadio = document.getElementById('selected');
    const selectionDiv = document.getElementById('subscriber-selection');
    
    function toggleSelection() {
        if (selectedRadio.checked) {
            selectionDiv.style.display = 'block';
        } else {
            selectionDiv.style.display = 'none';
        }
    }
    
    allRadio.addEventListener('change', toggleSelection);
    selectedRadio.addEventListener('change', toggleSelection);
});
</script>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
