<?php
$page_title = "Afspraken ";
require_once '../includes/admin-auth.php';
require_once '../includes/db.php';

// Initialize database connection
$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed");
}

// Pagination settings
$items_per_page = 20;
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($page - 1) * $items_per_page;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$where_clause = '';
$params = [];

if ($search !== '') {
    // Check if search exactly matches an appointment ID
    $check_id_stmt = $pdo->prepare("SELECT id FROM appointments WHERE id = ?");
    $check_id_stmt->execute([$search]);
    $exact_match = $check_id_stmt->fetch(PDO::FETCH_ASSOC);
    
    // If exact match found, redirect to appointment detail page
    if ($exact_match) {
        header("Location: appointment-detail.php?id=" . $exact_match['id']);
        exit;
    }
    
    // Otherwise, continue with search filtering
    $where_clause = "WHERE a.id LIKE ? OR a.client_name LIKE ? OR a.client_email LIKE ?";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

// Get total number of appointments for pagination
$count_sql = "SELECT COUNT(*) FROM appointments a $where_clause";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($params);
$total_appointments = $stmt->fetchColumn();
$total_pages = ceil($total_appointments / $items_per_page);

// Get appointments for current page
$sql = "
    SELECT a.*
    FROM appointments a
    $where_clause
    ORDER BY a.appointment_date DESC, a.appointment_time DESC
    LIMIT ? OFFSET ?
";

$params[] = $items_per_page;
$params[] = $offset;

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$appointments = $stmt->fetchAll(PDO::FETCH_ASSOC);

ob_start();
?>

<!-- Appointments page specific content -->
<div class="row">
    <div class="col-lg-12">
        <div class="pa-heading mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Overzicht Afspraken</h2>
                <form method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Zoek op naam of email..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="pa-btn">Zoeken</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="pa-cart-box">
            <?php if (empty($appointments)): ?>
                <div class="alert alert-info">Geen afspraken gevonden.</div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Naam</th>
                                <th>Datum</th>
                                <th>Tijd</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Acties</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($appointments as $appointment): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($appointment['id']); ?></td>
                                    <td><?php echo htmlspecialchars($appointment['client_name']); ?></td>
                                    <td><?php echo date('d-m-Y', strtotime($appointment['appointment_date'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></td>
                                    <td><?php echo htmlspecialchars($appointment['service_type']); ?></td>
                                    <td>
                                        <span class="pa-badge pa-badge-<?php echo $appointment['status'] === 'confirmed' ? 'completed' : htmlspecialchars($appointment['status']); ?>">
                                            <?php echo htmlspecialchars($appointment['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="appointment-detail.php?id=<?php echo htmlspecialchars($appointment['id']); ?>" 
                                           class="pa-btn pa-btn">Details</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($total_pages > 1): ?>
            <nav aria-label="Paginanavigatie">
                <div class="blog_pagination_wrapper mt-4">
                    <ul>
                        <?php if ($page > 1): ?>
                            <li class="blog_page_prev">
                                <a href="?page=<?php echo ($page - 1); ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    &laquo; Vorige
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php
                        // Calculate range of page numbers to show
                        $range = 3;
                        $start_page = max(1, $page - $range);
                        $end_page = min($total_pages, $page + $range);
                        
                        // Show first page if not in range
                        if ($start_page > 1) {
                            echo '<li><a href="?page=1' . ($search ? '&search=' . urlencode($search) : '') . '">1</a></li>';
                            if ($start_page > 2) {
                                echo '<li class="dot"><a>...</a></li>';
                            }
                        }
                        
                        // Show page numbers
                        for ($i = $start_page; $i <= $end_page; $i++): 
                        ?>
                            <li class="<?php echo $i === $page ? 'active' : ''; ?>">
                                <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; 
                        
                        // Show last page if not in range
                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<li class="dot"><a>...</a></li>';
                            }
                            echo '<li><a href="?page=' . $total_pages . ($search ? '&search=' . urlencode($search) : '') . '">' . $total_pages . '</a></li>';
                        }
                        ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="blog_page_next">
                                <a href="?page=<?php echo ($page + 1); ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    Volgende &raquo;
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
