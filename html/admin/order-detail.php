<?php
$page_title = "Order Details";
require_once '../includes/admin-auth.php';
require_once '../includes/config.php';
require_once '../includes/db.php';

// Initialize database connection
$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed");
}

$order_id = $_GET['id'] ?? '';

if (!$order_id) {
    header('Location: orders.php');
    exit;
}

// Get order details
$stmt = $pdo->prepare("
    SELECT o.*, 
           COUNT(oi.id) as item_count, 
           SUM(oi.quantity) as total_items,
           o.delivery_method
    FROM orders o
    LEFT JOIN order_items oi ON o.id = oi.order_id
    WHERE o.id = ?
    GROUP BY o.id
");
$stmt->execute([$order_id]);
$order = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$order) {
    header('Location: orders.php');
    exit;
}

// Get order items
$stmt = $pdo->prepare("
    SELECT oi.*, p.name as product_name
    FROM order_items oi
    LEFT JOIN products p ON oi.product_id = p.id
    WHERE oi.order_id = ?
");
$stmt->execute([$order['id']]);
$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    try {
        $stmt = $pdo->prepare("UPDATE orders SET status = ? WHERE id = ?");
        $stmt->execute([$_POST['status'], $order_id]);
        
        // Redirect to refresh the page
        header("Location: order-detail.php?id=" . $order_id . "&updated=1");
        exit;
    } catch (Exception $e) {
        $error = "Error updating order status: " . $e->getMessage();
    }
}

ob_start();
?>

<div class="container mt-4">
    <?php if (isset($_GET['updated'])): ?>
        <div class="alert alert-success">Order status updated successfully</div>
    <?php endif; ?>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Order Details #<?php echo htmlspecialchars($order_id); ?></h2>
        <a href="orders.php" class="btn btn-outline-primary">← Terug naar overzicht</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Order Informatie</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Order ID:</th>
                            <td><?php echo htmlspecialchars($order['id']); ?></td>
                        </tr>
                        <tr>
                            <th>Datum:</th>
                            <td><?php echo date('d-m-Y H:i', strtotime($order['created_at'])); ?></td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <form method="post" class="d-flex align-items-center">
                                    <select name="status" class="form-select form-select-sm me-2" style="width: auto;">
                                        <?php
                                        $statuses = ['pending', 'processing', 'completed', 'cancelled'];
                                        foreach ($statuses as $status) {
                                            $selected = $status === $order['status'] ? 'selected' : '';
                                            echo "<option value=\"$status\" $selected>" . ucfirst($status) . "</option>";
                                        }
                                        ?>
                                    </select>
                                    <button type="submit" class="btn btn-sm btn-primary">Update</button>
                                </form>
                            </td>
                        </tr>
                        <tr>
                            <th>Bezorgmethode:</th>
                            <td>
                                <?php 
                                $deliveryMethod = $order['delivery_method'] ?? 'shipping';
                                echo $deliveryMethod === 'pickup' ? 'Ophalen in Zoetermeer' : 'Verzenden'; 
                                ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Totaal items:</th>
                            <td><?php echo $order['total_items']; ?></td>
                        </tr>
                        <tr>
                            <th>Totaalbedrag:</th>
                            <td>€<?php echo number_format($order['total_amount'], 2, ',', '.'); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Verzendgegevens</h5>
                </div>
                <div class="card-body">
                    <?php
                    $shipping = json_decode($order['shipping_address'], true);
                    if ($shipping):
                    ?>
                        <address>
                            <?php echo htmlspecialchars($shipping['name'] ?? ''); ?><br>
                            <?php echo htmlspecialchars($shipping['address'] ?? $shipping['street'] ?? ''); ?><br>
                            <?php echo htmlspecialchars($shipping['postal_code'] ?? $shipping['postcode'] ?? ''); ?> 
                            <?php echo htmlspecialchars($shipping['city'] ?? ''); ?><br>
                            <?php echo htmlspecialchars($shipping['country'] ?? ''); ?>
                        </address>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Order Items</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th class="text-center" style="width: 120px;">Prijs</th>
                            <th class="text-center" style="width: 100px;">Aantal</th>
                            <th class="text-end" style="width: 120px;">Totaal</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $item): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($item['product_name']); ?></td>
                                <td class="text-center">€<?php echo number_format($item['price'], 2, ',', '.'); ?></td>
                                <td class="text-center"><?php echo $item['quantity']; ?></td>
                                <td class="text-end">€<?php echo number_format($item['price'] * $item['quantity'], 2, ',', '.'); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot class="table-light">
                        <?php
                        // Calculate correct values
                        $deliveryMethod = $order['delivery_method'] ?? 'shipping';
                        $shipping = ($deliveryMethod === 'shipping') ? 4.95 : 0;
                        
                        // Calculate subtotal (total without shipping and VAT)
                        $subtotal = 0;
                        foreach ($items as $item) {
                            $subtotal += $item['price'] * $item['quantity'];
                        }
                        
                        // Calculate VAT (21% of subtotal)
                        $vat = $subtotal * 0.21;
                        
                        // Total is subtotal + VAT + shipping
                        $total = $subtotal + $vat + $shipping;
                        ?>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Subtotaal:</strong></td>
                            <td class="text-end"><strong>€<?php echo number_format($subtotal, 2, ',', '.'); ?></strong></td>
                        </tr>
                        <tr>
                            <td colspan="3" class="text-end"><strong>BTW (21%):</strong></td>
                            <td class="text-end"><strong>€<?php echo number_format($vat, 2, ',', '.'); ?></strong></td>
                        </tr>
                        <?php if ($deliveryMethod === 'shipping'): ?>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Verzendkosten:</strong></td>
                            <td class="text-end"><strong>€<?php echo number_format($shipping, 2, ',', '.'); ?></strong></td>
                        </tr>
                        <?php endif; ?>
                        <tr>
                            <td colspan="3" class="text-end"><strong>Totaal:</strong></td>
                            <td class="text-end"><strong>€<?php echo number_format($order['total_amount'], 2, ',', '.'); ?></strong></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
