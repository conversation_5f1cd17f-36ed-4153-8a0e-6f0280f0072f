<?php
$page_title = "Bestellingen ";
require_once '../includes/admin-auth.php';
require_once '../includes/db.php';

// Initialize database connection
$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed");
}

// Pagination settings
$items_per_page = 20;
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($page - 1) * $items_per_page;

// Search functionality
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$where_clause = '';
$params = [];

if ($search !== '') {
    // Check if search exactly matches an order ID
    $check_id_stmt = $pdo->prepare("SELECT id FROM orders WHERE id = ?");
    $check_id_stmt->execute([$search]);
    $exact_match = $check_id_stmt->fetch(PDO::FETCH_ASSOC);
    
    // If exact match found, redirect to order detail page
    if ($exact_match) {
        header("Location: order-detail.php?id=" . $exact_match['id']);
        exit;
    }
    
    // Otherwise, continue with search filtering
    $where_clause = "WHERE o.id LIKE ? OR o.order_id LIKE ?";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

// Get total number of orders for pagination
$count_sql = "SELECT COUNT(*) FROM orders o $where_clause";
$stmt = $pdo->prepare($count_sql);
$stmt->execute($params);
$total_orders = $stmt->fetchColumn();
$total_pages = ceil($total_orders / $items_per_page);

// Get orders for current page
$sql = "
    SELECT o.*, 
           COUNT(oi.id) as total_items,
           o.delivery_method
    FROM orders o
    LEFT JOIN order_items oi ON o.id = oi.order_id
    $where_clause
    GROUP BY o.id
    ORDER BY o.created_at DESC
    LIMIT ? OFFSET ?
";

$params[] = $items_per_page;
$params[] = $offset;

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$orders = $stmt->fetchAll(PDO::FETCH_ASSOC);

ob_start();
?>

<!-- Orders page specific content -->
<div class="row">
    <div class="col-lg-12">
        <div class="pa-heading mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2>Overzicht Bestellingen</h2>
                <form method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" 
                               name="search" 
                               class="form-control" 
                               placeholder="Zoek op order ID..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                        <button type="submit" class="pa-btn">Zoeken</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="pa-cart-box">
            <?php if (empty($orders)): ?>
                <div class="alert alert-info">Geen bestellingen gevonden.</div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Datum</th>
                                <th>Status</th>
                                <th>Bezorgmethode</th>
                                <th>Aantal items</th>
                                <th>Totaalbedrag</th>
                                <th>Acties</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($orders as $order): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($order['id']); ?></td>
                                    <td><?php echo date('d-m-Y H:i', strtotime($order['created_at'])); ?></td>
                                    <td>
                                        <span class="pa-badge pa-badge-<?php echo htmlspecialchars($order['status']); ?>">
                                            <?php echo htmlspecialchars($order['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        $deliveryMethod = $order['delivery_method'] ?? 'shipping';
                                        echo $deliveryMethod === 'pickup' ? 'Ophalen' : 'Verzenden'; 
                                        ?>
                                    </td>
                                    <td><?php echo (int)$order['total_items']; ?></td>
                                    <td>€<?php echo number_format((float)$order['total_amount'], 2, ',', '.'); ?></td>
                                    <td>
                                        <a href="order-detail.php?id=<?php echo htmlspecialchars($order['id']); ?>" 
                                           class="pa-btn pa-btn">Details</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <?php if ($total_pages > 1): ?>
            <nav aria-label="Paginanavigatie">
                <div class="blog_pagination_wrapper mt-4">
                    <ul>
                        <?php if ($page > 1): ?>
                            <li class="blog_page_prev">
                                <a href="?page=<?php echo ($page - 1); ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    &laquo; Vorige
                                </a>
                            </li>
                        <?php endif; ?>
                        
                        <?php
                        // Calculate range of page numbers to show
                        $range = 3;
                        $start_page = max(1, $page - $range);
                        $end_page = min($total_pages, $page + $range);
                        
                        // Show first page if not in range
                        if ($start_page > 1) {
                            echo '<li><a href="?page=1' . ($search ? '&search=' . urlencode($search) : '') . '">1</a></li>';
                            if ($start_page > 2) {
                                echo '<li class="dot"><a>...</a></li>';
                            }
                        }
                        
                        // Show page numbers
                        for ($i = $start_page; $i <= $end_page; $i++): 
                        ?>
                            <li class="<?php echo $i === $page ? 'active' : ''; ?>">
                                <a href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            </li>
                        <?php endfor; 
                        
                        // Show last page if not in range
                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<li class="dot"><a>...</a></li>';
                            }
                            echo '<li><a href="?page=' . $total_pages . ($search ? '&search=' . urlencode($search) : '') . '">' . $total_pages . '</a></li>';
                        }
                        ?>
                        
                        <?php if ($page < $total_pages): ?>
                            <li class="blog_page_next">
                                <a href="?page=<?php echo ($page + 1); ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                    Volgende &raquo;
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </nav>
        <?php endif; ?>
    </div>
</div>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
