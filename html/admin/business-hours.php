<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include required files
require_once '../includes/admin-auth.php';
require_once '../includes/db.php';

// Initialize database connection
$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed");
}

$page_title = "Openingstijden ";

// Define days array
$days = [
    0 => 'Zondag',
    1 => 'Maandag',
    2 => 'Dinsdag',
    3 => 'Woensdag',
    4 => 'Donderdag',
    5 => 'Vrijdag',
    6 => 'Zaterdag'
];

// Handle form submission before any output
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (!$pdo || !($pdo instanceof PDO)) {
            throw new Exception("Database verbinding niet beschikbaar");
        }

        $pdo->setAttribute(PDO::ATTR_AUTOCOMMIT, 0);
        $pdo->beginTransaction();
        
        // Handle special date submission
        if (isset($_POST['special_date'])) {
            $date = $_POST['special_date'];
            $type = $_POST['date_type'];
            $start_time = !empty($_POST['start_time']) ? $_POST['start_time'] : null;
            $end_time = !empty($_POST['end_time']) ? $_POST['end_time'] : null;
            $reason = $_POST['reason'] ?? null;
            
            // For blocked dates, ensure times are null
            if ($type === 'blocked') {
                $start_time = null;
                $end_time = null;
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO special_dates (date, type, start_time, end_time, reason) 
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                type = VALUES(type), 
                start_time = VALUES(start_time), 
                end_time = VALUES(end_time), 
                reason = VALUES(reason)
            ");
            
            $stmt->execute([$date, $type, $start_time, $end_time, $reason]);
            $pdo->commit();
            $_SESSION['success_message'] = "Speciale datum toegevoegd.";
            
        } else {
            // Handle regular business hours (existing code)
            $pdo->exec("DELETE FROM business_hours");
            
            $stmt = $pdo->prepare("
                INSERT INTO business_hours (day_of_week, start_time, end_time) 
                VALUES (?, ?, ?)
            ");
            
            $hasValidEntries = false;
            
            foreach ($_POST as $key => $value) {
                if (strpos($key, 'enabled_') === 0) {
                    $day = (int)substr($key, 8);
                    $start = $_POST["start_$day"] ?? null;
                    $end = $_POST["end_$day"] ?? null;
                    
                    if ($start && $end) {
                        $stmt->execute([$day, $start, $end]);
                        $hasValidEntries = true;
                    }
                }
            }
            
            if ($hasValidEntries) {
                $pdo->commit();
                $_SESSION['success_message'] = "Openingstijden zijn succesvol bijgewerkt.";
            } else {
                $pdo->rollBack();
                $_SESSION['error_message'] = "Geen geldige openingstijden opgegeven.";
            }
        }

        $pdo->setAttribute(PDO::ATTR_AUTOCOMMIT, 1);
        
    } catch (Exception $e) {
        if ($pdo && $pdo instanceof PDO) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $pdo->setAttribute(PDO::ATTR_AUTOCOMMIT, 1);
        }
        
        $_SESSION['error_message'] = "Database fout: " . $e->getMessage();
    }
    
    header('Location: business-hours.php');
    exit;
}

// Handle delete special date
if (isset($_GET['delete_special']) && is_numeric($_GET['delete_special'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM special_dates WHERE id = ?");
        $stmt->execute([$_GET['delete_special']]);
        $_SESSION['success_message'] = "Speciale datum verwijderd.";
    } catch (Exception $e) {
        $_SESSION['error_message'] = "Fout bij verwijderen: " . $e->getMessage();
    }
    header('Location: business-hours.php');
    exit;
}

// Initialize hours_by_day array
$hours_by_day = [];

// Get current business hours
try {
    $stmt = $pdo->query("SELECT * FROM business_hours ORDER BY day_of_week");
    $business_hours = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Create an associative array for easier access
    foreach ($business_hours as $hours) {
        $hours_by_day[$hours['day_of_week']] = $hours;
    }
} catch (Exception $e) {
    $_SESSION['error_message'] = "Fout bij ophalen openingstijden: " . $e->getMessage();
}

// Get messages from session
$success_message = $_SESSION['success_message'] ?? null;
$error_message = $_SESSION['error_message'] ?? null;

// Clear messages from session
unset($_SESSION['success_message'], $_SESSION['error_message']);

// Start main content
ob_start();
?>

<div class="container">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <?php if ($success_message): ?>
                        <div class="alert alert-success"><?php echo htmlspecialchars($success_message); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($error_message); ?></div>
                    <?php endif; ?>

                    <form method="post" id="businessHoursForm">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Dag</th>
                                    <th>Open</th>
                                    <th>Openingstijd</th>
                                    <th>Sluitingstijd</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($days as $day_num => $day_name):
                                    $is_open = isset($hours_by_day[$day_num]);
                                    $start_time = $is_open ? $hours_by_day[$day_num]['start_time'] : '';
                                    $end_time = $is_open ? $hours_by_day[$day_num]['end_time'] : '';
                                ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($day_name); ?></td>
                                        <td>
                                            <div class="form-check">
                                                <input type="checkbox" 
                                                       class="form-check-input day-enabled" 
                                                       id="enabled_<?php echo $day_num; ?>" 
                                                       name="enabled_<?php echo $day_num; ?>"
                                                       <?php echo $is_open ? 'checked' : ''; ?>>
                                            </div>
                                        </td>
                                        <td>
                                            <input type="time" 
                                                   class="form-control time-input" 
                                                   name="start_<?php echo $day_num; ?>" 
                                                   value="<?php echo htmlspecialchars($start_time); ?>"
                                                   <?php echo !$is_open ? 'disabled' : ''; ?>>
                                        </td>
                                        <td>
                                            <input type="time" 
                                                   class="form-control time-input" 
                                                   name="end_<?php echo $day_num; ?>" 
                                                   value="<?php echo htmlspecialchars($end_time); ?>"
                                                   <?php echo !$is_open ? 'disabled' : ''; ?>>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <div class="mt-3">
                            <button type="submit" class="pa-btn">Opslaan</button>
                        </div>
                    </form>
                    
                    <!-- Move special dates section inside the same card -->
                    <hr class="my-4">
                    <h5>Speciale datums</h5>
                    
                    <style>
                        .special-date-form .form-control,
                        .special-date-form .btn {
                            height: 38px;
                            line-height: 1.5;
                        }

                        .special-date-form select.form-control {
                            height: 38px !important;
                            padding: 6px 12px;
                        }

                        .special-date-form .row {
                            align-items: flex-end;
                        }
                    </style>

                    <!-- Add special date form -->
                    <form method="post" class="mb-4 special-date-form">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">Datum</label>
                                <input type="date" name="special_date" class="form-control" required>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Type</label>
                                <select name="date_type" class="form-control" id="dateType" required>
                                    <option value="">Type</option>
                                    <option value="blocked">Gesloten</option>
                                    <option value="custom_hours">Afwijkende tijden</option>
                                </select>
                            </div>
                            <div class="col-md-2" id="startTimeDiv" style="display:none;">
                                <label class="form-label">Van</label>
                                <input type="time" name="start_time" class="form-control">
                            </div>
                            <div class="col-md-2" id="endTimeDiv" style="display:none;">
                                <label class="form-label">Tot</label>
                                <input type="time" name="end_time" class="form-control">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Reden</label>
                                <input type="text" name="reason" class="form-control" placeholder="Optioneel">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary">Toevoegen</button>
                            </div>
                        </div>
                    </form>

                    <!-- List existing special dates -->
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Datum</th>
                                <th>Type</th>
                                <th>Tijden</th>
                                <th>Reden</th>
                                <th>Acties</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $stmt = $pdo->query("SELECT * FROM special_dates ORDER BY date");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)):
                            ?>
                            <tr>
                                <td><?php echo date('d-m-Y', strtotime($row['date'])); ?></td>
                                <td><?php echo $row['type'] === 'blocked' ? 'Gesloten' : 'Afwijkende tijden'; ?></td>
                                <td>
                                    <?php if ($row['type'] === 'custom_hours'): ?>
                                        <?php echo $row['start_time'] . ' - ' . $row['end_time']; ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($row['reason'] ?? ''); ?></td>
                                <td>
                                    <a href="business-hours.php?delete_special=<?php echo $row['id']; ?>" 
                                       onclick="return confirm('Weet je het zeker?')" class="btn btn-sm btn-danger">Verwijderen</a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('dateType').addEventListener('change', function() {
    const startDiv = document.getElementById('startTimeDiv');
    const endDiv = document.getElementById('endTimeDiv');
    const startInput = document.querySelector('input[name="start_time"]');
    const endInput = document.querySelector('input[name="end_time"]');
    
    if (this.value === 'custom_hours') {
        startDiv.style.display = 'block';
        endDiv.style.display = 'block';
        startInput.required = true;
        endInput.required = true;
    } else {
        startDiv.style.display = 'none';
        endDiv.style.display = 'none';
        startInput.required = false;
        endInput.required = false;
        startInput.value = '';
        endInput.value = '';
    }
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle checkbox changes
    document.querySelectorAll('.day-enabled').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const dayNum = this.id.split('_')[1];
            const timeInputs = document.querySelectorAll(`input[name^="start_${dayNum}"], input[name^="end_${dayNum}"]`);
            
            timeInputs.forEach(function(input) {
                input.disabled = !checkbox.checked;
                if (!checkbox.checked) {
                    input.value = '';
                }
            });
        });
    });
    
    // Form validation
    document.getElementById('businessHoursForm').addEventListener('submit', function(e) {
        e.preventDefault(); // Prevent default form submission
        
        const checkedDays = document.querySelectorAll('.day-enabled:checked');
        let isValid = true;
        
        for (const checkbox of checkedDays) {
            const dayNum = checkbox.id.split('_')[1];
            const start = document.querySelector(`input[name="start_${dayNum}"]`).value;
            const end = document.querySelector(`input[name="end_${dayNum}"]`).value;
            
            if (!start || !end) {
                alert('Vul zowel een openings- als sluitingstijd in voor alle geselecteerde dagen.');
                isValid = false;
                break;
            }
            
            if (start >= end) {
                alert('De sluitingstijd moet later zijn dan de openingstijd.');
                isValid = false;
                break;
            }
        }
        
        if (isValid) {
            // Submit the form traditionally
            this.submit();
        }
    });
});
</script>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
