<?php
$page_title = "Afspraak Details";
require_once '../includes/admin-auth.php';
require_once '../includes/config.php';
require_once '../includes/db.php';

// Initialize database connection
$pdo = getPDO();
if (!$pdo) {
    die("Database connection failed"); 
}

$appointment_id = $_GET['id'] ?? '';

if (!$appointment_id) {
    header('Location: appointments.php');
    exit;
}

// Get appointment details
$stmt = $pdo->prepare("SELECT * FROM appointments WHERE id = ?");
$stmt->execute([$appointment_id]);
$appointment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$appointment) {
    header('Location: appointments.php');
    exit;
}

// Get service name based on service_id and service_type
$service_name = '';
if (!empty($appointment['service_id']) && !empty($appointment['service_type'])) {
    if ($appointment['service_type'] === 'behandeling') {
        // List of treatments
        $behandelingen = [
            1 => ['name' => 'Abhyanga massage', 'price' => 75.00],
            2 => ['name' => 'Kruidenstempelmassage 60 min', 'price' => 65.00],
            3 => ['name' => 'Kruidenstempelmassage 90 min', 'price' => 85.00],
            4 => ['name' => 'Ontspanningsmassage 30 min', 'price' => 35.00],
            5 => ['name' => 'Ontspanningsmassage 60 min', 'price' => 65.00],
            6 => ['name' => 'Ontspanningsmassage 90 min', 'price' => 85.00],
            7 => ['name' => 'Stemvorktherapie', 'price' => 65.00],
            8 => ["name" => "Tienermassage", "price" => 45.00],
            9 => ["name" => "Go4Balance Consult", "price" => 45.00]
        ];
        
        $service_id = (int)$appointment['service_id'];
        $service_name = isset($behandelingen[$service_id]) ? $behandelingen[$service_id]['name'] : 'Onbekende behandeling';
        
    } elseif ($appointment['service_type'] === 'workshop') {
        // List of workshops
        $workshops = [
            9 => ['name' => 'Mild in het wild Compassiewandeling', 'price' => 45.00],
            10 => ['name' => 'Workshop Uit je hoofd in je lijf', 'price' => 39.95]
        ];
        
        $service_id = (int)$appointment['service_id'];
        $service_name = isset($workshops[$service_id]) ? $workshops[$service_id]['name'] : 'Onbekende workshop';
        
    } elseif ($appointment['service_type'] === 'welkomstconsult') {
        // List of welkomstconsult options
        $welkomstconsult = [
            12 => ['name' => 'Shiro Abhyanga (Welkomstconsult)', 'price' => 18.00],
            13 => ['name' => 'Pad Abhyanga (Welkomstconsult)', 'price' => 18.00]
        ];
        
        $service_id = (int)$appointment['service_id'];
        $service_name = isset($welkomstconsult[$service_id]) ? $welkomstconsult[$service_id]['name'] : 'Onbekend welkomstconsult';
    }
}

// Handle status update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['status'])) {
    try {
        $stmt = $pdo->prepare("UPDATE appointments SET status = ? WHERE id = ?");
        $stmt->execute([$_POST['status'], $appointment_id]);
        
        // Redirect to refresh the page
        header("Location: appointment-detail.php?id=" . $appointment_id . "&updated=1");
        exit;
    } catch (Exception $e) {
        $error = "Error updating appointment status: " . $e->getMessage();
    }
}

ob_start();
?>

<div class="container mt-4">
    <?php if (isset($_GET['updated'])): ?>
        <div class="alert alert-success">Afspraak status succesvol bijgewerkt</div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
    <?php endif; ?>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Afspraak Details #<?php echo htmlspecialchars($appointment_id); ?></h2>
        <a href="appointments.php" class="btn btn-outline-primary">← Terug naar overzicht</a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Afspraak Informatie</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Afspraak ID:</th>
                            <td><?php echo htmlspecialchars($appointment['id']); ?></td>
                        </tr>
                        <tr>
                            <th>Datum:</th>
                            <td><?php echo date('d-m-Y', strtotime($appointment['appointment_date'])); ?></td>
                        </tr>
                        <tr>
                            <th>Tijd:</th>
                            <td><?php echo date('H:i', strtotime($appointment['appointment_time'])); ?></td>
                        </tr>
                        <tr>
                            <th>Type:</th>
                            <td><?php echo htmlspecialchars($appointment['service_type']); ?></td>
                        </tr>
                        <tr>
                            <th>Dienst:</th>
                            <td>
                                <?php if (!empty($service_name)): ?>
                                    <?php echo htmlspecialchars($service_name); ?>
                                <?php else: ?>
                                    <?php echo htmlspecialchars($appointment['service_id']); ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Status:</th>
                            <td>
                                <form method="post" class="d-flex align-items-center">
                                    <select name="status" class="form-select form-select-sm me-2" style="width: auto;">
                                        <?php
                                        $statuses = ['pending', 'confirmed', 'cancelled'];
                                        foreach ($statuses as $status) {
                                            $selected = $status === $appointment['status'] ? 'selected' : '';
                                            echo "<option value=\"$status\" $selected>" . ucfirst($status) . "</option>";
                                        }
                                        ?>
                                    </select>
                                    <button type="submit" class="btn btn-sm btn-primary">Update</button>
                                </form>
                            </td>
                        </tr>
                        <tr>
                            <th>Aangemaakt op:</th>
                            <td><?php echo date('d-m-Y H:i', strtotime($appointment['created_at'])); ?></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Klant Informatie</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <th>Naam:</th>
                            <td><?php echo htmlspecialchars($appointment['client_name']); ?></td>
                        </tr>
                        <tr>
                            <th>Email:</th>
                            <td><?php echo htmlspecialchars($appointment['client_email']); ?></td>
                        </tr>
                        <?php if (!empty($appointment['client_phone'])): ?>
                        <tr>
                            <th>Telefoon:</th>
                            <td><?php echo htmlspecialchars($appointment['client_phone']); ?></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
            
            <?php if (!empty($appointment['notes'])): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Opmerkingen</h5>
                </div>
                <div class="card-body">
                    <p><?php echo nl2br(htmlspecialchars($appointment['notes'])); ?></p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php
$content = ob_get_clean();
require_once '../includes/admin-page.php';
?>
