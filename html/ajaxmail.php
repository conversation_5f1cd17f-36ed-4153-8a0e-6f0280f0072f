<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/email_helper.php';
require 'vendor/autoload.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify reCAPTCHA
    $recaptcha_secret = "6Lf7Te0oAAAAAMelSFNpj38tZIjVXwZALjg8g2-v";
    $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';

    if (empty($recaptcha_response)) {
        echo json_encode(['success' => false, 'message' => 'reCAPTCHA verificatie is vereist.']);
        exit;
    }

    $verify_url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => $recaptcha_secret,
        'response' => $recaptcha_response,
        'remoteip' => $_SERVER['REMOTE_ADDR']
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];

    $context = stream_context_create($options);
    $verify_response = file_get_contents($verify_url, false, $context);
    $response_data = json_decode($verify_response);

    if (!$response_data->success) {
        error_log('reCAPTCHA verification failed: ' . print_r($response_data, true));
        echo json_encode(['success' => false, 'message' => 'reCAPTCHA verificatie mislukt. Probeer het opnieuw.']);
        exit;
    }
    
    // Get form data
    $name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Naam is verplicht';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Geldig e-mailadres is verplicht';
    }
    
    if (empty($subject)) {
        $errors[] = 'Onderwerp is verplicht';
    }
    
    if (empty($message)) {
        $errors[] = 'Bericht is verplicht';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode('<br>', $errors)]);
        exit;
    }
    
    // Prepare email content
    $email_message = "
        <html>
        <head>
            <title>Contact Form Submission</title>
        </head>
        <body>
            <h2>Contact Form Submission</h2>
            <p><strong>Name:</strong> " . htmlspecialchars($name) . "</p>
            <p><strong>Email:</strong> " . htmlspecialchars($email) . "</p>
            <p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>
            <p><strong>Message:</strong></p>
            <p>" . nl2br(htmlspecialchars($message)) . "</p>
            <p><small>This message was sent from the contact form on " . date('Y-m-d H:i:s') . "</small></p>
        </body>
        </html>
    ";

    if (sendEmail("Contact Form: $subject", $email_message, '', '<EMAIL>')) {
        echo json_encode(['success' => true, 'message' => 'Bedankt voor uw bericht. We nemen zo spoedig mogelijk contact met u op.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Er is een probleem opgetreden bij het verzenden van uw bericht. Probeer het later opnieuw.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
