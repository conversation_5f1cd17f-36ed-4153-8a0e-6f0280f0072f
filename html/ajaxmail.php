<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/email_helper.php';
require_once 'includes/spam_protection.php';
require 'vendor/autoload.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $client_ip = $_SERVER['REMOTE_ADDR'];

    // Comprehensive spam protection check
    $spam_check = checkSpamProtection($_POST, $client_ip, 'contact', [
        'max_submissions' => 3,
        'time_window' => 300, // 5 minutes
        'min_score' => 0.5,
        'expected_action' => 'contact'
    ]);

    if (!$spam_check['allowed']) {
        echo json_encode(['success' => false, 'message' => $spam_check['error_message']]);
        exit;
    }
    
    // Get form data
    $name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $subject = trim($_POST['subject'] ?? '');
    $message = trim($_POST['message'] ?? '');
    
    // Validate form data
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Naam is verplicht';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Geldig e-mailadres is verplicht';
    }
    
    if (empty($subject)) {
        $errors[] = 'Onderwerp is verplicht';
    }
    
    if (empty($message)) {
        $errors[] = 'Bericht is verplicht';
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode('<br>', $errors)]);
        exit;
    }
    
    // Prepare email content
    $email_message = "
        <html>
        <head>
            <title>Contact Form Submission</title>
        </head>
        <body>
            <h2>Contact Form Submission</h2>
            <p><strong>Name:</strong> " . htmlspecialchars($name) . "</p>
            <p><strong>Email:</strong> " . htmlspecialchars($email) . "</p>
            <p><strong>Subject:</strong> " . htmlspecialchars($subject) . "</p>
            <p><strong>Message:</strong></p>
            <p>" . nl2br(htmlspecialchars($message)) . "</p>
            <p><small>This message was sent from the contact form on " . date('Y-m-d H:i:s') . "</small></p>
        </body>
        </html>
    ";

    if (sendEmail("Contact Form: $subject", $email_message, '', '<EMAIL>')) {
        // Record successful submission for rate limiting
        recordSubmission($client_ip, 'contact', $spam_check['details']['rate_limit']['submissions']);

        echo json_encode(['success' => true, 'message' => 'Bedankt voor uw bericht. We nemen zo spoedig mogelijk contact met u op.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Er is een probleem opgetreden bij het verzenden van uw bericht. Probeer het later opnieuw.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
?>
