<?php
function renderBreadcrumb($title, $background_class = '', $additional_links = []) {
    // Determine the base path dynamically
    $base_path = '';
    if (isset($_SERVER['SCRIPT_NAME'])) {
        $script_path = $_SERVER['SCRIPT_NAME'];
        $subdirectories = ['/behandelingen/', '/workshops/', '/blog/', '/producten/', '/admin/', '/includes/'];
        
        foreach ($subdirectories as $dir) {
            if (strpos($script_path, $dir) !== false) {
                $base_path = '../';
                break;
            }
        }
    }

    // Build breadcrumb links array starting with Home
    $links = [
        ['url' => 'index.php', 'name' => 'Home']
    ];
    
    // Add any additional intermediate links
    foreach ($additional_links as $link) {
        $links[] = $link;
    }
    
    // Add current page as last item (without link)
    $links[] = ['url' => '', 'name' => $title];
?>
    <!-- breadcrumb start -->
    <div class="pa-breadcrumb <?php echo $background_class; ?>">
        <div class="container-fluid">
            <div class="pa-breadcrumb-box">
                <h1><?php echo $title; ?></h1>
                <ul>
                    <?php foreach ($links as $index => $link): ?>
                        <li>
                            <?php if ($link['url']): ?>
                                <a href="<?php echo $base_path . $link['url']; ?>"><?php echo $link['name']; ?></a>
                            <?php else: ?>
                                <?php echo $link['name']; ?>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>
    </div>
    <!-- breadcrumb end -->
<?php
}
?>
