<?php
/**
 * Spam Protection Helper Functions
 *
 * This file contains helper functions for protecting forms against spam,
 * including rate limiting, honeypot validation, and reCAPTCHA verification.
 */

require_once __DIR__ . '/recaptcha_config.php';

/**
 * Check if honeypot field is filled (indicates spam)
 * 
 * @param array $post_data POST data array
 * @param string $honeypot_field Name of the honeypot field (default: 'website')
 * @return bool True if spam detected, false otherwise
 */
function checkHoneypot($post_data, $honeypot_field = 'website') {
    return !empty($post_data[$honeypot_field]);
}

/**
 * Check rate limiting for an IP address
 * 
 * @param string $client_ip IP address to check
 * @param string $form_type Type of form (e.g., 'contact', 'newsletter')
 * @param int $max_submissions Maximum submissions allowed
 * @param int $time_window Time window in seconds
 * @return array ['allowed' => bool, 'remaining' => int, 'reset_time' => int]
 */
function checkRateLimit($client_ip, $form_type, $max_submissions = 3, $time_window = 300) {
    $rate_limit_file = sys_get_temp_dir() . '/' . $form_type . '_rate_limit_' . md5($client_ip);
    $current_time = time();
    
    if (file_exists($rate_limit_file)) {
        $submissions = json_decode(file_get_contents($rate_limit_file), true) ?: [];
        // Remove old submissions outside the window
        $submissions = array_filter($submissions, function($timestamp) use ($current_time, $time_window) {
            return ($current_time - $timestamp) < $time_window;
        });
    } else {
        $submissions = [];
    }
    
    $remaining = $max_submissions - count($submissions);
    $allowed = count($submissions) < $max_submissions;
    
    // Calculate reset time (when the oldest submission expires)
    $reset_time = 0;
    if (!empty($submissions)) {
        $oldest_submission = min($submissions);
        $reset_time = $oldest_submission + $time_window;
    }
    
    return [
        'allowed' => $allowed,
        'remaining' => max(0, $remaining),
        'reset_time' => $reset_time,
        'submissions' => $submissions
    ];
}

/**
 * Record a successful form submission for rate limiting
 * 
 * @param string $client_ip IP address
 * @param string $form_type Type of form
 * @param array $existing_submissions Existing submissions array from checkRateLimit
 * @return bool True on success, false on failure
 */
function recordSubmission($client_ip, $form_type, $existing_submissions = []) {
    $rate_limit_file = sys_get_temp_dir() . '/' . $form_type . '_rate_limit_' . md5($client_ip);
    $current_time = time();
    
    $existing_submissions[] = $current_time;
    
    return file_put_contents($rate_limit_file, json_encode($existing_submissions)) !== false;
}

/**
 * Verify reCAPTCHA v3 response
 * 
 * @param string $recaptcha_response The reCAPTCHA response token
 * @param string $client_ip Client IP address
 * @param float $min_score Minimum score required (0.0 to 1.0)
 * @param string $expected_action Expected action name
 * @return array ['success' => bool, 'score' => float, 'action' => string, 'error' => string]
 */
function verifyRecaptcha($recaptcha_response, $client_ip, $min_score = null, $expected_action = null) {
    // Use default min score if not provided
    if ($min_score === null) {
        $min_score = RecaptchaConfig::getDefaultMinScore();
    }

    $recaptcha_secret = RecaptchaConfig::getSecretKey();

    if (empty($recaptcha_response)) {
        return [
            'success' => false,
            'score' => 0.0,
            'action' => '',
            'error' => 'reCAPTCHA response is empty'
        ];
    }

    $verify_url = RecaptchaConfig::getVerifyUrl();
    $data = [
        'secret' => $recaptcha_secret,
        'response' => $recaptcha_response,
        'remoteip' => $client_ip
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data),
            'timeout' => RecaptchaConfig::getVerifyTimeout()
        ]
    ];

    $context = stream_context_create($options);
    $verify_response = file_get_contents($verify_url, false, $context);
    
    if ($verify_response === false) {
        return [
            'success' => false,
            'score' => 0.0,
            'action' => '',
            'error' => 'Failed to connect to reCAPTCHA service'
        ];
    }
    
    $response_data = json_decode($verify_response);
    
    if (!$response_data || !$response_data->success) {
        $error_codes = isset($response_data->{'error-codes'}) ? implode(', ', $response_data->{'error-codes'}) : 'Unknown error';
        return [
            'success' => false,
            'score' => 0.0,
            'action' => '',
            'error' => 'reCAPTCHA verification failed: ' . $error_codes
        ];
    }
    
    // Check score if provided
    $score = isset($response_data->score) ? $response_data->score : 1.0;
    if ($score < $min_score) {
        return [
            'success' => false,
            'score' => $score,
            'action' => $response_data->action ?? '',
            'error' => 'reCAPTCHA score too low: ' . $score
        ];
    }
    
    // Check action if provided
    if ($expected_action && isset($response_data->action) && $response_data->action !== $expected_action) {
        return [
            'success' => false,
            'score' => $score,
            'action' => $response_data->action,
            'error' => 'reCAPTCHA action mismatch: expected ' . $expected_action . ', got ' . $response_data->action
        ];
    }
    
    return [
        'success' => true,
        'score' => $score,
        'action' => $response_data->action ?? '',
        'error' => ''
    ];
}

/**
 * Comprehensive spam check for forms
 * 
 * @param array $post_data POST data
 * @param string $client_ip Client IP address
 * @param string $form_type Form type for rate limiting
 * @param array $options Options array with keys: max_submissions, time_window, min_score, expected_action
 * @return array ['allowed' => bool, 'error_message' => string, 'details' => array]
 */
function checkSpamProtection($post_data, $client_ip, $form_type, $options = []) {
    $defaults = [
        'max_submissions' => 3,
        'time_window' => 300,
        'min_score' => 0.5,
        'expected_action' => null,
        'honeypot_field' => 'website'
    ];
    
    $options = array_merge($defaults, $options);
    
    // Check honeypot
    if (checkHoneypot($post_data, $options['honeypot_field'])) {
        error_log("Spam detected: honeypot field filled from IP: $client_ip for form: $form_type");
        return [
            'allowed' => false,
            'error_message' => 'Er is een probleem opgetreden. Probeer het later opnieuw.',
            'details' => ['type' => 'honeypot']
        ];
    }
    
    // Check rate limiting
    $rate_check = checkRateLimit($client_ip, $form_type, $options['max_submissions'], $options['time_window']);
    if (!$rate_check['allowed']) {
        error_log("Rate limit exceeded for IP: $client_ip for form: $form_type");
        return [
            'allowed' => false,
            'error_message' => 'Te veel verzoeken. Probeer het over een paar minuten opnieuw.',
            'details' => ['type' => 'rate_limit', 'reset_time' => $rate_check['reset_time']]
        ];
    }
    
    // Check reCAPTCHA
    $recaptcha_response = $post_data['g-recaptcha-response'] ?? '';
    $recaptcha_check = verifyRecaptcha($recaptcha_response, $client_ip, $options['min_score'], $options['expected_action']);
    
    if (!$recaptcha_check['success']) {
        error_log("reCAPTCHA failed for IP: $client_ip for form: $form_type - " . $recaptcha_check['error']);
        return [
            'allowed' => false,
            'error_message' => 'reCAPTCHA verificatie mislukt. Probeer het opnieuw.',
            'details' => ['type' => 'recaptcha', 'error' => $recaptcha_check['error']]
        ];
    }
    
    return [
        'allowed' => true,
        'error_message' => '',
        'details' => [
            'rate_limit' => $rate_check,
            'recaptcha' => $recaptcha_check
        ]
    ];
}
?>
