[2025-05-05 13:29:28][INFO] Starting payment processor
[2025-05-05 13:29:28][INFO] Loading includes
[2025-05-05 15:29:28][INFO] Mollie configuration loading
[2025-05-05 15:29:28][INFO] All includes loaded successfully
[2025-05-05 15:29:28][INFO] Request received
Data: {
    "method": "POST",
    "content_type": "multipart\/form-data; boundary=----WebKitFormBoundaryzKY09JA6xom3OG3Z",
    "post_data": {
        "action": "process_payment",
        "csrf_token": "a7515d186240a29c12979c1ffe4ef9c87b8772aa9c1f099bc8f942eae194543f",
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "address": "dssdds",
        "postal_code": "2493XJ",
        "city": "Den Haag"
    }
}
[2025-05-05 15:29:28][INFO] Processing payment action detected
[2025-05-05 15:29:28][INFO] Fetching cart items for session
Data: {
    "session_id": "fpv4nq373k6pfrlhvvae9lae1i"
}
[2025-05-05 15:29:28][INFO] Cart items fetched successfully
Data: {
    "count": 2
}
[2025-05-05 15:29:28][INFO] Cart items retrieved
Data: {
    "count": 2
}
[2025-05-05 15:29:28][INFO] Totals calculated
Data: {
    "subtotal": 46.9,
    "shipping": 4.95,
    "total": 51.85
}
[2025-05-05 15:29:28][INFO] Order created
Data: {
    "order_id": "ORDER_6818bd381ac7a",
    "total_amount": 51.85,
    "items_count": 2
}
[2025-05-05 13:35:02][INFO] Starting payment processor
[2025-05-05 13:35:02][INFO] Loading includes
[2025-05-05 15:35:02][INFO] Mollie configuration loading
[2025-05-05 15:35:02][INFO] All includes loaded successfully
[2025-05-05 15:35:02][INFO] Request received
Data: {
    "method": "POST",
    "content_type": "multipart\/form-data; boundary=----WebKitFormBoundaryBMk0fWt9zz3dARJT",
    "post_data": {
        "action": "process_payment",
        "csrf_token": "a7515d186240a29c12979c1ffe4ef9c87b8772aa9c1f099bc8f942eae194543f",
        "name": "Dennis Tjassens",
        "email": "<EMAIL>",
        "address": "dddd",
        "postal_code": "2493XJ",
        "city": "Den Haag"
    }
}
[2025-05-05 15:35:02][INFO] Processing payment action detected
[2025-05-05 15:35:02][INFO] Fetching cart items for session
Data: {
    "session_id": "fpv4nq373k6pfrlhvvae9lae1i"
}
[2025-05-05 15:35:02][INFO] Cart items fetched successfully
Data: {
    "count": 2
}
[2025-05-05 15:35:02][INFO] Cart items retrieved
Data: {
    "count": 2
}
[2025-05-05 15:35:02][INFO] Totals calculated
Data: {
    "subtotal": 46.9,
    "shipping": 4.95,
    "total": 51.85
}
[2025-05-05 15:35:02][INFO] Order created
Data: {
    "order_id": "ORDER_6818be864b6a0",
    "total_amount": 51.85,
    "items_count": 2
}
[2025-05-05 14:24:51][INFO] Starting payment processor
[2025-05-05 14:24:51][INFO] Loading includes
[2025-05-05 16:24:51][INFO] Mollie configuration loading
[2025-05-05 16:24:51][INFO] All includes loaded successfully
[2025-05-05 16:24:51][INFO] Request received
Data: {
    "method": "POST",
    "content_type": "multipart\/form-data; boundary=----WebKitFormBoundaryiqlPUe9pKrW8zcGb",
    "post_data": {
        "action": "process_payment",
        "csrf_token": "a7515d186240a29c12979c1ffe4ef9c87b8772aa9c1f099bc8f942eae194543f",
        "name": "Dennis Tjassens",
        "email": "<EMAIL>",
        "address": "sddssd",
        "postal_code": "2493XJ",
        "city": "Den Haag"
    }
}
[2025-05-05 16:24:51][INFO] Processing payment action detected
[2025-05-05 16:24:51][INFO] Fetching cart items for session
Data: {
    "session_id": "fpv4nq373k6pfrlhvvae9lae1i"
}
[2025-05-05 16:24:51][INFO] Cart items fetched successfully
Data: {
    "count": 2
}
[2025-05-05 16:24:51][INFO] Cart items retrieved
Data: {
    "count": 2
}
[2025-05-05 16:24:51][INFO] Totals calculated
Data: {
    "subtotal": 46.9,
    "shipping": 4.95,
    "total": 51.85
}
[2025-05-05 16:24:51][INFO] Order created
Data: {
    "order_id": "ORDER_6818ca333b8c8",
    "total_amount": 51.85,
    "items_count": 2
}
[2025-05-05 14:28:51][INFO] Starting payment processor
[2025-05-05 14:28:52][INFO] Loading includes
[2025-05-05 16:28:52][INFO] Mollie configuration loading
[2025-05-05 16:28:52][INFO] All includes loaded successfully
[2025-05-05 16:28:52][INFO] Request received
Data: {
    "method": "POST",
    "content_type": "multipart\/form-data; boundary=----WebKitFormBoundaryBCXgiEbfFuN3VBXc",
    "post_data": {
        "action": "process_payment",
        "csrf_token": "a7515d186240a29c12979c1ffe4ef9c87b8772aa9c1f099bc8f942eae194543f",
        "name": "Dennis Tjassens",
        "email": "<EMAIL>",
        "address": "dsfd",
        "postal_code": "2493XJ",
        "city": "Den Haag"
    }
}
[2025-05-05 16:28:52][INFO] Processing payment action detected
[2025-05-05 16:28:52][INFO] Fetching cart items for session
Data: {
    "session_id": "fpv4nq373k6pfrlhvvae9lae1i"
}
[2025-05-05 16:28:52][INFO] Fetching cart items for session
Data: {
    "session_id": "fpv4nq373k6pfrlhvvae9lae1i"
}
[2025-05-05 16:28:52][INFO] Cart items fetched successfully
Data: {
    "count": 2
}
[2025-05-05 16:28:52][INFO] Cart items fetched successfully
Data: {
    "count": 2
}
[2025-05-05 16:28:52][INFO] Cart items retrieved
Data: {
    "count": 2
}
[2025-05-05 16:28:52][INFO] Totals calculated
Data: {
    "subtotal": 46.9,
    "shipping": 4.95,
    "total": 51.85
}
[2025-05-05 16:28:52][INFO] Order created
Data: {
    "order_id": "ORDER_6818cb240a6df",
    "total_amount": 51.85,
    "items_count": 2
}
