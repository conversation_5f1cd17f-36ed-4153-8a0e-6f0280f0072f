<?php
// Set custom error log location with absolute path for reliability
$logDir = __DIR__ . '/logs';
if (!file_exists($logDir)) {
    mkdir($logDir, 0755, true);
}
ini_set('error_log', $logDir . '/registration.log');
error_reporting(E_ALL);
ini_set('display_errors', 0); // Disable display errors in production

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize CSRF token if not set
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Log with timestamp and request ID for better traceability
$requestId = uniqid();
function logMessage($message) {
    global $requestId;
    error_log("[" . date('Y-m-d H:i:s') . "][REQ:$requestId] $message");
}

logMessage("=== New Registration Attempt ===");

require_once 'auth.php';

// Function to safely redirect with error message
function redirectWithError($message) {
    $_SESSION['signup_error'] = $message;
    logMessage("Error: $message - Redirecting to index");
    header('Location: ../index.php');
    exit;
}

// Only process POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logMessage("Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    redirectWithError('Ongeldige aanvraag');
}

// Verify CSRF token
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    logMessage("CSRF token validation failed");
    redirectWithError('Beveiligingscontrole mislukt. Probeer het opnieuw.');
}

logMessage("POST data received: " . json_encode(array_map(function($key, $value) {
    return $key === 'password' || $key === 'confirm_password' ? '[REDACTED]' : $value;
}, array_keys($_POST), $_POST)));

// Sanitize and validate input
$username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$password = $_POST['password'] ?? '';
$confirm_password = $_POST['confirm_password'] ?? '';

logMessage("Sanitized input - Username: $username, Email: $email");

// Comprehensive validation
$errors = [];

// Check for empty fields
if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
    $errors[] = 'Alle velden zijn verplicht';
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Ongeldig e-mailadres';
} elseif (isRestrictedDomain($email)) {
    $errors[] = 'Registratie met dit e-maildomein is niet toegestaan';
}

// Validate username (alphanumeric, 3-20 chars)
if (!preg_match('/^[a-zA-Z0-9_]{3,20}$/', $username)) {
    $errors[] = 'Gebruikersnaam moet tussen 3 en 20 tekens zijn en mag alleen letters, cijfers en underscores bevatten';
}

// Validate password strength
if (strlen($password) < 8) {
    $errors[] = 'Wachtwoord moet minimaal 8 tekens bevatten';
} elseif (!preg_match('/[A-Z]/', $password) || !preg_match('/[a-z]/', $password) || !preg_match('/[0-9]/', $password)) {
    $errors[] = 'Wachtwoord moet minimaal één hoofdletter, één kleine letter en één cijfer bevatten';
}

// Check if passwords match
if ($password !== $confirm_password) {
    $errors[] = 'Wachtwoorden komen niet overeen';
}

// If validation errors exist, redirect back with errors
if (!empty($errors)) {
    logMessage("Validation errors: " . implode(', ', $errors));
    $_SESSION['signup_errors'] = $errors;
    header('Location: ../index.php');
    exit;
}

try {
    // Test database connection
    $pdo = getPDO();
    if (!$pdo) {
        throw new Exception("Database connection failed");
    }
    
    // Register user with rate limiting
    $ip = $_SERVER['REMOTE_ADDR'];
    if (isRateLimited($ip, 'registration', 5, 3600)) { // 5 attempts per hour
        throw new Exception("Te veel registratiepogingen. Probeer het later opnieuw.");
    }
    
    logMessage("Attempting user registration");
    $result = registerUser($username, $email, $password);
    logMessage("Registration result: " . ($result['success'] ? 'Success' : 'Failed') . " - " . $result['message']);
    
    if ($result['success']) {
        // Regenerate session ID after successful registration
        session_regenerate_id(true);
        $_SESSION['signup_success'] = $result['message'];
        
        // Optional: Auto-login after registration
        // $_SESSION['user_id'] = $result['user_id'];
        // $_SESSION['username'] = $username;
        
        // Generate new CSRF token
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    } else {
        $_SESSION['signup_error'] = $result['message'];
    }
    
    header('Location: ../index.php');
    exit;
    
} catch (Exception $e) {
    logMessage("Exception: " . $e->getMessage());
    redirectWithError('Er is een fout opgetreden: ' . $e->getMessage());
}

// Rate limiting function (should be in auth.php in production)
function isRateLimited($identifier, $action, $maxAttempts, $timeWindow) {
    global $pdo;
    
    try {
        // Clean up old attempts
        $stmt = $pdo->prepare("DELETE FROM rate_limits WHERE action = ? AND created_at < ?");
        $stmt->execute([$action, date('Y-m-d H:i:s', time() - $timeWindow)]);
        
        // Count recent attempts
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM rate_limits WHERE identifier = ? AND action = ? AND created_at > ?");
        $stmt->execute([$identifier, $action, date('Y-m-d H:i:s', time() - $timeWindow)]);
        $count = $stmt->fetchColumn();
        
        if ($count >= $maxAttempts) {
            return true;
        }
        
        // Log this attempt
        $stmt = $pdo->prepare("INSERT INTO rate_limits (identifier, action, created_at) VALUES (?, ?, ?)");
        $stmt->execute([$identifier, $action, date('Y-m-d H:i:s')]);
        
        return false;
    } catch (Exception $e) {
        logMessage("Rate limiting error: " . $e->getMessage());
        return false; // Don't block on error
    }
}
