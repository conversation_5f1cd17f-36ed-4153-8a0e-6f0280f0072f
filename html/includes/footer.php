<?php 
require_once 'config.php';

// Determine the base path dynamically
$base_path = (isset($_SERVER['SCRIPT_NAME']) && (
    strpos($_SERVER['SCRIPT_NAME'], '/behandelingen/') !== false || 
    strpos($_SERVER['SCRIPT_NAME'], '/workshops/') !== false || 
    strpos($_SERVER['SCRIPT_NAME'], '/blog/') !== false || 
    strpos($_SERVER['SCRIPT_NAME'], '/producten/') !== false ||
    strpos($_SERVER['SCRIPT_NAME'], '/admin/') !== false ||
    strpos($_SERVER['SCRIPT_NAME'], '/includes/') !== false
)) ? '../' : '';
?>

<!-- footer start -->
<div class="pa-footer">
    <div class="container">
        <div class="row">
            <?php foreach ($footer['sections'] as $section): ?>
            <div class="col-lg-3 col-md-6">
                <div class="pa-foot-box">
                    <h2 class="pa-foot-title"><?php echo $section['title']; ?></h2>
                    <ul>
                        <?php foreach ($section['links'] as $link): ?>
                            <li>
                                <a href="<?php echo $base_path . $link['url']; ?>"><?php echo $link['name']; ?></a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
            <?php endforeach; ?>
            <div class="col-lg-3">
                <div class="pa-foot-box pa-foot-subscribe">
                    <div class="pa-newsletter">
                        <form id="newsletterForm" method="post">
                            <div class="row mb-3">
                                <div class="col-6 pe-2">
                                    <input type="text" name="first_name" placeholder="Voornaam">
                                </div>
                                <div class="col-6 ps-2">
                                    <input type="text" name="last_name" placeholder="Achternaam">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="input-group">
                                        <input type="email" name="email" placeholder="Vul jouw email in" required>
                                        <button type="submit" class="pa-btn">Schrijf je in</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div id="newsletterResponse" class="mt-2"></div>
                    </div>
                    <p>Schrijf je in voor de nieuwsbrief met informatie over nieuwe behandelingen, workshops en producten.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- footer end -->
<!-- copyright start -->
<div class="pa-copyright">
    <div class="container">
        <p><?php echo $footer['copyright']; ?></p>
    </div>
</div>
<!-- copyright end -->

<!-- go top wrapper start -->
<div id="scroll" class="main_go_top">
    <div class="go_top item-bounce">
        <img src="<?php echo $base_path; ?>assets/images/testi_compact.webp" alt="">
    </div>
</div>
<!-- go top wrapper end -->

<!-- Include login modals -->
<?php include 'login-modals.php'; ?>

<!-- Scripts with defer attribute -->
<script src="<?php echo $base_path; ?>assets/js/jquery.min.js" defer></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js" defer></script>
<script src="<?php echo $base_path; ?>assets/js/bootstrap.min.js" defer></script>
<script src="<?php echo $base_path; ?>assets/js/SmoothScroll.min.js" defer></script>
<script src="<?php echo $base_path; ?>assets/js/swiper.min.js" defer></script>
<script src="<?php echo $base_path; ?>assets/js/custom.js?v=1.0" defer></script>
<script src="<?php echo $base_path; ?>assets/js/cart.js?v=1.0" defer></script>
<script src="<?php echo $base_path; ?>assets/js/search.js?v=1.0" defer></script>
<script src="<?php echo $base_path; ?>assets/js/booking.js?v=1.0" defer></script>
<script src="<?php echo $base_path; ?>assets/js/cookie-consent.js?v=1.0" defer></script>
</body>
</html>
