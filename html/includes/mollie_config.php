<?php
/**
 * Mollie API Configuration and Helper Functions
 * 
 * This file contains functions for initializing the Mollie API client
 * and helper functions for payment processing.
 */

require_once __DIR__ . '/Logger.php';

// Initialize logger with explicit debug message
$mollieLogger = Logger::get('mollie');

// Add a test log entry to verify logging is working
$mollieLogger->info('Mollie configuration loading');

// Require Composer autoloader if not already included
if (!class_exists('\Mollie\Api\MollieApiClient')) {
    require_once __DIR__ . '/../vendor/autoload.php';
}

/**
 * Get Mollie API key based on environment
 * 
 * @return string The appropriate Mollie API key
 */
function getMollieApiKey() {
    global $mollieLogger;
    
    // Define environment-specific API keys
    $apiKeys = [
        // 'test' => getenv('MOLLIE_TEST_API_KEY') ?: 'test_Gc3UPSA8rpHTFNFrUrWsRrJjD87rAV',
        'test' => getenv('MOLLIE_TEST_API_KEY') ?: 'live_QyxVC7tvAD7BqvnkrtxF6JMaq8RmfN',
        'live' => getenv('MOLLIE_LIVE_API_KEY') ?: 'live_QyxVC7tvAD7BqvnkrtxF6JMaq8RmfN'
    ];
    
    // Determine environment
    $environment = $GLOBALS['_environment'] ?? 
                  (getenv('APP_ENV') ?: 
                  (($_SERVER['SERVER_NAME'] === 'localhost' || strpos($_SERVER['SERVER_NAME'], '.test') !== false || 
                    strpos($_SERVER['SERVER_NAME'], '.local') !== false) ? 'development' : 'production'));
    
    // Log which environment we're using
    $mollieLogger->info("Using Mollie environment", [
        'environment' => $environment,
        'mode' => ($environment === 'production' ? 'LIVE' : 'TEST')
    ]);
    
    return $apiKeys[$environment] ?? $apiKeys['test'];
}

/**
 * Initialize and return a configured Mollie API client
 * 
 * @param bool $forceNew Whether to force creation of a new client instance
 * @return \Mollie\Api\MollieApiClient|null Configured Mollie client or null on error
 */
function getMollieClient($forceNew = false) {
    // Use static variable to cache the client instance
    static $mollieClient = null;
    
    // Return cached instance unless forced to create new one
    if ($mollieClient !== null && !$forceNew) {
        return $mollieClient;
    }
    
    try {
        // Create Mollie client without specific HTTP client - it will pick the best available
        $mollie = new \Mollie\Api\MollieApiClient();
        $mollie->setApiKey(getMollieApiKey());
        
        // Cache and return the client
        $mollieClient = $mollie;
        return $mollie;
    } catch (\Mollie\Api\Exceptions\ApiException $e) {
        logMollieError("Mollie API exception", $e);
        return null;
    } catch (Exception $e) {
        logMollieError("Mollie general exception", $e);
        return null;
    }
}

/**
 * Log Mollie-related errors with context
 * 
 * @param string $message Error message
 * @param Exception $exception The exception that occurred
 * @param array $context Additional context
 */
function logMollieError($message, $exception, $context = []) {
    global $mollieLogger;
    
    $logMessage = sprintf(
        "[MOLLIE ERROR] %s: %s in %s on line %d\nStack trace: %s",
        $message,
        $exception->getMessage(),
        $exception->getFile(),
        $exception->getLine(),
        $exception->getTraceAsString()
    );
    
    if (!empty($context)) {
        $logMessage .= "\nContext: " . json_encode($context, JSON_PRETTY_PRINT);
    }
    
    // Additional data for Mollie API exceptions
    if ($exception instanceof \Mollie\Api\Exceptions\ApiException) {
        $logMessage .= sprintf(
            "\nMollie Details: Code: %s, API Message: %s",
            $exception->getCode(),
            $exception->getMessage()
        );
    }
    
    $mollieLogger->error($logMessage);
}

/**
 * Format amount for Mollie API (ensure 2 decimal places)
 * 
 * @param float|string $amount The amount to format
 * @return string Formatted amount string
 */
function formatAmount($amount) {
    // Ensure we're working with a numeric value
    $amount = is_numeric($amount) ? $amount : floatval($amount);
    
    // Format with exactly 2 decimal places
    return number_format($amount, 2, '.', '');
}

/**
 * Generate a secure, unique order ID
 * 
 * @param string $prefix Optional prefix for the order ID
 * @return string Unique order ID
 */
function generateOrderId($prefix = 'JT') {
    // Generate components
    $timestamp = date('YmdHis');
    $random = bin2hex(random_bytes(4));
    
    // Combine with separator for readability
    return $prefix . '-' . $timestamp . '-' . $random;
}

/**
 * Get available payment methods from Mollie
 * 
 * @param float $amount Amount of the payment
 * @param string $currency Currency code (default: EUR)
 * @return array Array of available payment methods
 */
function getMolliePaymentMethods($amount, $currency = 'EUR') {
    try {
        $mollie = getMollieClient();
        if (!$mollie) {
            return [];
        }
        
        $methods = $mollie->methods->all([
            'amount' => [
                'currency' => $currency,
                'value' => formatAmount($amount)
            ],
            'resource' => 'orders',
            'includeWallets' => 'applepay',
            'locale' => 'nl_NL'
        ]);
        
        return $methods->getArrayCopy();
    } catch (Exception $e) {
        logMollieError("Failed to get payment methods", $e, ['amount' => $amount, 'currency' => $currency]);
        return [];
    }
}

/**
 * Create a payment link using Mollie API
 * 
 * @param int $orderId Order ID
 * @param float $amount Total amount
 * @param string $description Payment description
 * @param string $redirectUrl Redirect URL after payment
 * @param array $metadata Additional metadata
 * @param string|null $webhookUrl Optional webhook URL (null to skip)
 * @return string Payment URL
 */
function createPaymentLink($orderId, $amount, $description, $redirectUrl, $metadata = [], $webhookUrl = null) {
    global $mollieLogger;
    
    try {
        $mollieLogger->info('Creating payment link', [
            'order_id' => $orderId,
            'amount' => $amount,
            'description' => $description
        ]);
        
        // Format amount to 2 decimal places
        $formattedAmount = formatAmount($amount);
        $mollieLogger->info('Formatted amount', ['amount' => $formattedAmount]);
        
        // Initialize Mollie client
        $mollieLogger->info('Initializing Mollie client');
        $mollie = new \Mollie\Api\MollieApiClient();
        $mollie->setApiKey(getMollieApiKey());
        $mollieLogger->info('Mollie client initialized with API key');
        
        // Create payment data
        $paymentData = [
            "amount" => [
                "currency" => "EUR",
                "value" => $formattedAmount
            ],
            "description" => $description,
            "redirectUrl" => $redirectUrl,
            "metadata" => array_merge(['order_id' => $orderId], $metadata),
            "method" => null, // Allow all payment methods
            "locale" => "nl_NL"
        ];
        
        // Add webhook URL if provided
        if ($webhookUrl !== null) {
            $paymentData["webhookUrl"] = $webhookUrl;
        } else if (isset($_SERVER['HTTP_HOST'])) {
            // Create default webhook URL if server info is available
            $defaultWebhookUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] . "/mollie_webhook.php";
            $paymentData["webhookUrl"] = $defaultWebhookUrl;
            $mollieLogger->info('Default webhook URL created', ['url' => $defaultWebhookUrl]);
        }
        
        // Create the payment
        $mollieLogger->info('Creating payment with Mollie API');
        $payment = $mollie->payments->create($paymentData);
        
        $mollieLogger->info('Payment created successfully', [
            'payment_id' => $payment->id,
            'order_id' => $orderId,
            'checkout_url' => $payment->getCheckoutUrl()
        ]);
        
        // Return checkout URL
        return $payment->getCheckoutUrl();
    } catch (\Mollie\Api\Exceptions\ApiException $e) {
        logMollieError("Mollie API Exception", $e, [
            'order_id' => $orderId,
            'amount' => $amount,
            'description' => $description
        ]);
        return null;
    } catch (Exception $e) {
        logMollieError("Exception in createPaymentLink", $e, [
            'order_id' => $orderId,
            'amount' => $amount,
            'description' => $description
        ]);
        return null;
    }
}

/**
 * Get payment details by ID
 * 
 * @param string $paymentId Mollie payment ID
 * @return \Mollie\Api\Resources\Payment|null Payment object or null on error
 */
function getMolliePayment($paymentId) {
    try {
        $mollie = getMollieClient();
        if (!$mollie || empty($paymentId)) {
            return null;
        }
        
        return $mollie->payments->get($paymentId);
    } catch (Exception $e) {
        logMollieError("Failed to get payment details", $e, ['payment_id' => $paymentId]);
        return null;
    }
}

/**
 * Issue a refund for a payment
 * 
 * @param string $paymentId Mollie payment ID
 * @param float|null $amount Amount to refund (null for full refund)
 * @param string $description Refund description
 * @return bool Whether refund was successful
 */
function refundMolliePayment($paymentId, $amount = null, $description = 'Refund') {
    try {
        $mollie = getMollieClient();
        if (!$mollie) {
            return false;
        }
        
        $payment = $mollie->payments->get($paymentId);
        
        // Check if payment can be refunded
        if (!$payment->canBeRefunded()) {
            throw new Exception("Payment cannot be refunded");
        }
        
        $refundData = ['description' => $description];
        
        // Add amount if specified (partial refund)
        if ($amount !== null) {
            $refundData['amount'] = [
                'currency' => 'EUR',
                'value' => formatAmount($amount)
            ];
        }
        
        $payment->refund($refundData);
        return true;
    } catch (Exception $e) {
        logMollieError("Refund failed", $e, [
            'payment_id' => $paymentId,
            'amount' => $amount,
            'description' => $description
        ]);
        return false;
    }
}

/**
 * Check if Mollie API is configured and working correctly
 * 
 * @return bool Whether Mollie API is working
 */
function isMollieWorking() {
    try {
        $mollie = getMollieClient(true); // Force new client
        if (!$mollie) {
            return false;
        }
        
        // Try to get API status by fetching a method
        $mollie->methods->get('ideal');
        return true;
    } catch (Exception $e) {
        logMollieError("Mollie API check failed", $e);
        return false;
    }
}
