<?php
/**
 * Database Connection Manager
 * 
 * Handles database connections, initialization, and provides utility functions
 * for database operations.
 * 
 * @version 1.1
 */

// Initialize PDO instance container
$GLOBALS['_pdo_instance'] = null;
$GLOBALS['_pdo_config'] = [
    'development' => [
        'host' => '127.0.0.1',
        'port' => '3306',
        'dbname' => 'tjassens',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false, // Important!
            PDO::MYSQL_ATTR_FOUND_ROWS => true,
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            // Disable multi-queries
            PDO::MYSQL_ATTR_MULTI_STATEMENTS => false
        ]
    ],
    'production' => [
        'host' => getenv('DB_HOST') ?: 'localhost',
        'port' => getenv('DB_PORT') ?: '3306',
        'dbname' => getenv('DB_NAME') ?: 'tjassens',
        'username' => getenv('DB_USER') ?: 'jacqueline',
        'password' => getenv('DB_PASS') ?: '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false, // Important!
            PDO::MYSQL_ATTR_FOUND_ROWS => true,
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            // Disable multi-queries
            PDO::MYSQL_ATTR_MULTI_STATEMENTS => false
        ]
    ]
];

// Determine environment
$GLOBALS['_environment'] = (getenv('APP_ENV') ?: 
                          (isset($_SERVER['APP_ENV']) ? $_SERVER['APP_ENV'] : 
                          (($_SERVER['SERVER_NAME'] ?? 'localhost') === 'localhost' ? 'development' : 'production')));

/**
 * Get PDO database connection instance
 * 
 * Creates a new PDO connection if one doesn't exist, or returns the existing one
 * 
 * @param bool $forceNew Force creation of a new connection
 * @return PDO|null PDO instance or null on failure
 */
function getPDO($forceNew = false) {
    if ($forceNew || $GLOBALS['_pdo_instance'] === null) {
        try {
            // Get configuration for current environment
            $env = $GLOBALS['_environment'];
            $config = $GLOBALS['_pdo_config'][$env];
            
            // Build DSN string
            $dsn = sprintf(
                'mysql:host=%s;port=%s;charset=%s',
                $config['host'],
                $config['port'],
                $config['charset']
            );
            
            // First check if we can connect to MySQL server
            $tempPdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
            
            // Try to create the database if it doesn't exist
            $dbname = $config['dbname'];
            $tempPdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            
            // Now connect to the specific database
            $dsn .= ";dbname=$dbname";
            $GLOBALS['_pdo_instance'] = new PDO($dsn, $config['username'], $config['password'], $config['options']);
            
            // Initialize database schema if needed
            initializeDatabase($GLOBALS['_pdo_instance']);
            
            return $GLOBALS['_pdo_instance'];
        } catch (PDOException $e) {
            logDatabaseError('Connection failed', $e);
            return null;
        }
    }
    
    return $GLOBALS['_pdo_instance'];
}

/**
 * Initialize database schema
 * 
 * Creates necessary tables if they don't exist
 * 
 * @param PDO $pdo PDO connection instance
 * @return bool Success status
 */
function initializeDatabase($pdo) {
    try {
        // Users table
        $pdo->exec("CREATE TABLE IF NOT EXISTS `users` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `username` VARCHAR(50) NOT NULL UNIQUE,
            `email` VARCHAR(100) NOT NULL UNIQUE,
            `password` VARCHAR(255) NOT NULL,
            `first_name` VARCHAR(50),
            `last_name` VARCHAR(50),
            `role` ENUM('admin', 'customer', 'staff') NOT NULL DEFAULT 'customer',
            `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX `idx_username` (`username`),
            INDEX `idx_email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Password reset tokens
        $pdo->exec("CREATE TABLE IF NOT EXISTS `password_resets` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `email` VARCHAR(100) NOT NULL,
            `token` VARCHAR(100) NOT NULL,
            `expires_at` DATETIME NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX `idx_email` (`email`),
            INDEX `idx_token` (`token`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Authentication tokens (for remember me)
        $pdo->exec("CREATE TABLE IF NOT EXISTS `auth_tokens` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `user_id` INT UNSIGNED NOT NULL,
            `selector` VARCHAR(255) NOT NULL,
            `hashed_validator` VARCHAR(255) NOT NULL,
            `expires` DATETIME NOT NULL,
            `token_type` VARCHAR(20) NOT NULL DEFAULT 'remember',
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_selector` (`selector`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // User activity log
        $pdo->exec("CREATE TABLE IF NOT EXISTS `user_activity_log` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `user_id` INT UNSIGNED NOT NULL,
            `action` VARCHAR(50) NOT NULL,
            `details` TEXT,
            `ip_address` VARCHAR(45),
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_action` (`action`),
            INDEX `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Failed login attempts
        $pdo->exec("CREATE TABLE IF NOT EXISTS `failed_logins` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `username` VARCHAR(50) NOT NULL,
            `ip_address` VARCHAR(45) NOT NULL,
            `attempt_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX `idx_username` (`username`),
            INDEX `idx_ip_address` (`ip_address`),
            INDEX `idx_attempt_time` (`attempt_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Product categories
        $pdo->exec("CREATE TABLE IF NOT EXISTS `product_categories` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `slug` VARCHAR(50) NOT NULL UNIQUE,
            `name` VARCHAR(100) NOT NULL,
            `description` TEXT,
            `image` VARCHAR(255),
            `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX `idx_slug` (`slug`),
            INDEX `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Products
        $pdo->exec("CREATE TABLE IF NOT EXISTS `products` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `category_id` INT UNSIGNED NOT NULL,
            `sku` VARCHAR(50) NOT NULL UNIQUE,
            `name` VARCHAR(255) NOT NULL,
            `slug` VARCHAR(255) NOT NULL UNIQUE,
            `description` TEXT,
            `price` DECIMAL(10,2) NOT NULL,
            `sale_price` DECIMAL(10,2),
            `stock` INT NOT NULL DEFAULT 0,
            `image` VARCHAR(255),
            `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (`category_id`) REFERENCES `product_categories`(`id`) ON DELETE CASCADE,
            INDEX `idx_category_id` (`category_id`),
            INDEX `idx_sku` (`sku`),
            INDEX `idx_slug` (`slug`),
            INDEX `idx_is_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Orders
        $pdo->exec("CREATE TABLE IF NOT EXISTS `orders` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `order_number` VARCHAR(50) NOT NULL UNIQUE,
            `user_id` INT UNSIGNED,
            `status` ENUM('pending', 'processing', 'completed', 'cancelled') NOT NULL DEFAULT 'pending',
            `total_amount` DECIMAL(10,2) NOT NULL,
            `shipping_address` TEXT,
            `billing_address` TEXT,
            `payment_method` VARCHAR(50),
            `notes` TEXT,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE SET NULL,
            INDEX `idx_order_number` (`order_number`),
            INDEX `idx_user_id` (`user_id`),
            INDEX `idx_status` (`status`),
            INDEX `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        // Order items
        $pdo->exec("CREATE TABLE IF NOT EXISTS `order_items` (
            `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            `order_id` INT UNSIGNED NOT NULL,
            `product_id` INT UNSIGNED NOT NULL,
            `quantity` INT NOT NULL,
            `price` DECIMAL(10,2) NOT NULL,
            `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE,
            FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE,
            INDEX `idx_order_id` (`order_id`),
            INDEX `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        return true;
    } catch (PDOException $e) {
        logDatabaseError('Schema initialization failed', $e);
        return false;
    }
}

/**
 * Execute a query and return the results
 * 
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters for the prepared statement
 * @param bool $fetchAll Whether to fetch all results or just one row
 * @return array|bool Query results or false on failure
 */
function dbQuery($sql, $params = [], $fetchAll = true) {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $fetchAll ? $stmt->fetchAll() : $stmt->fetch();
    } catch (PDOException $e) {
        logDatabaseError('Query execution failed', $e, [
            'sql' => $sql,
            'params' => $params
        ]);
        return false;
    }
}

/**
 * Execute a query and return the number of affected rows
 * 
 * @param string $sql SQL query with placeholders
 * @param array $params Parameters for the prepared statement
 * @return int|bool Number of affected rows or false on failure
 */
function dbExecute($sql, $params = []) {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    } catch (PDOException $e) {
        logDatabaseError('Query execution failed', $e, [
            'sql' => $sql,
            'params' => $params
        ]);
        return false;
    }
}

/**
 * Insert data into a table and return the last insert ID
 * 
 * @param string $table Table name
 * @param array $data Associative array of column => value pairs
 * @return int|bool Last insert ID or false on failure
 */
function dbInsert($table, $data) {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        // Sanitize table name - IMPORTANT!
        $table = preg_replace('/[^a-zA-Z0-9_]/', '', $table);
        
        // Build column and placeholder lists using named parameters
        $columns = array_keys($data);
        $placeholders = array_map(function($col) {
            return ":$col";
        }, $columns);
        
        $columnsStr = '`' . implode('`, `', $columns) . '`';
        $placeholdersStr = implode(', ', $placeholders);
        
        $sql = "INSERT INTO `$table` ($columnsStr) VALUES ($placeholdersStr)";
        $stmt = $pdo->prepare($sql);
        
        // Bind parameters using named parameters
        foreach ($data as $key => $value) {
            $stmt->bindValue(":$key", $value);
        }
        
        $stmt->execute();
        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        logDatabaseError('Insert operation failed', $e, [
            'table' => $table,
            'data' => $data
        ]);
        return false;
    }
}

/**
 * Update data in a table
 * 
 * @param string $table Table name
 * @param array $data Associative array of column => value pairs to update
 * @param string $where WHERE clause
 * @param array $whereParams Parameters for the WHERE clause
 * @return int|bool Number of affected rows or false on failure
 */
function dbUpdate($table, $data, $where, $whereParams = []) {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        // Build SET clause
        $setClauses = [];
        foreach ($data as $column => $value) {
            $setClauses[] = "`$column` = ?";
        }
        $setClause = implode(', ', $setClauses);
        
        $sql = "UPDATE `$table` SET $setClause WHERE $where";
        $stmt = $pdo->prepare($sql);
        
        // Combine data values and where params
        $params = array_merge(array_values($data), $whereParams);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    } catch (PDOException $e) {
        logDatabaseError('Update operation failed', $e, [
            'table' => $table,
            'data' => $data,
            'where' => $where
        ]);
        return false;
    }
}

/**
 * Delete data from a table
 * 
 * @param string $table Table name
 * @param string $where WHERE clause
 * @param array $params Parameters for the WHERE clause
 * @return int|bool Number of affected rows or false on failure
 */
function dbDelete($table, $where, $params = []) {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        $sql = "DELETE FROM `$table` WHERE $where";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    } catch (PDOException $e) {
        logDatabaseError('Delete operation failed', $e, [
            'table' => $table,
            'where' => $where
        ]);
        return false;
    }
}

/**
 * Begin a database transaction
 * 
 * @return bool Success status
 */
function dbBeginTransaction() {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        return $pdo->beginTransaction();
    } catch (PDOException $e) {
        logDatabaseError('Transaction start failed', $e);
        return false;
    }
}

/**
 * Commit a database transaction
 * 
 * @return bool Success status
 */
function dbCommit() {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        return $pdo->commit();
    } catch (PDOException $e) {
        logDatabaseError('Transaction commit failed', $e);
        return false;
    }
}

/**
 * Roll back a database transaction
 * 
 * @return bool Success status
 */
function dbRollback() {
    try {
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        return $pdo->rollBack();
    } catch (PDOException $e) {
        logDatabaseError('Transaction rollback failed', $e);
        return false;
    }
}

/**
 * Log database errors
 * 
 * @param string $message Error message
 * @param PDOException $exception The exception that occurred
 * @param array $context Additional context information
 * @return void
 */
function logDatabaseError($message, PDOException $exception, $context = []) {
    $env = $GLOBALS['_environment'];
    $errorInfo = $exception->errorInfo ?? null;
    
    $logMessage = sprintf(
        "[Database Error] %s: %s (Code: %s, SQL State: %s)",
        $message,
        $exception->getMessage(),
        $exception->getCode(),
        is_array($errorInfo) && isset($errorInfo[0]) ? $errorInfo[0] : 'Unknown'
    );
    
    if (!empty($context)) {
        $logMessage .= "\nContext: " . json_encode($context);
    }
    
    // In development, show errors for debugging
    if ($env === 'development') {
        error_log($logMessage);
        
        // Only display errors if display_errors is enabled
        if (ini_get('display_errors')) {
            echo "<div style='color:red;background:#ffeeee;padding:10px;margin:10px 0;border:1px solid #ff0000;'>";
            echo "<strong>Database Error:</strong> ";
            echo htmlspecialchars($message . ': ' . $exception->getMessage(), ENT_QUOTES, 'UTF-8');
            echo "</div>";
        }
    } else {
        // In production, only log to error log, never display
        error_log($logMessage);
    }
}

// Create database connection on script load only in development mode
if ($GLOBALS['_environment'] === 'development') {
    $pdo = getPDO();
    if ($pdo) {
        // Connection successful - silent in production
        if (ini_get('display_errors')) {
            error_log("Database connection successful");
        }
    }
}

// Add this function to validate table names
function isValidTableName($table) {
    return preg_match('/^[a-zA-Z0-9_]+$/', $table);
}

// Add this function for additional query security
function dbQuerySecure($sql, $params = [], $fetchAll = true) {
    try {
        // Prevent multiple statement execution
        if (stripos($sql, ';') !== false) {
            logDatabaseError('Multiple SQL statements not allowed', new Exception('Security violation'));
            return false;
        }
        
        // Only allow SELECT statements for this function
        if (!preg_match('/^[\s]*(SELECT|SHOW|DESCRIBE)/i', $sql)) {
            logDatabaseError('Only SELECT queries allowed', new Exception('Security violation'));
            return false;
        }
        
        $pdo = getPDO();
        if (!$pdo) {
            return false;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $fetchAll ? $stmt->fetchAll() : $stmt->fetch();
    } catch (PDOException $e) {
        logDatabaseError('Query execution failed', $e, [
            'sql' => $sql,
            'params' => $params
        ]);
        return false;
    }
}
