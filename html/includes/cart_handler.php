<?php
/**
 * <PERSON><PERSON>
 * 
 * Manages shopping cart operations including adding, removing, and updating items.
 * Provides synchronization between database and session storage.
 * 
 * @version 1.1
 */

// Ensure session is started at the very beginning
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/Logger.php';

$log = Logger::get('cart_handler');

// Enable error reporting only in development environment
if (isset($GLOBALS['_environment']) && $GLOBALS['_environment'] === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Constants
define('VAT_RATE', 0.21); // 21% VAT

// Always ensure we have a session_id
if (!isset($_SESSION['session_id'])) {
    $_SESSION['session_id'] = session_id();
    
    // Only log in development
    if (isset($GLOBALS['_environment']) && $GLOBALS['_environment'] === 'development') {
        error_log('New session ID created: ' . $_SESSION['session_id']);
    }
}

// Initialize cart in session if not exists
if (!isset($_SESSION['cart'])) {
    $_SESSION['cart'] = [];
}

/**
 * Get the number of items in the cart
 * 
 * @return int Total quantity of items in cart
 */
function getCartCount() {
    try {
        $pdo = getPDO();
        
        if (!$pdo || !isset($_SESSION['session_id'])) {
            return 0;
        }
        
        $stmt = $pdo->prepare("
            SELECT SUM(quantity) as total 
            FROM cart 
            WHERE session_id = :sessionId
        ");
        
        $sessionId = $_SESSION['session_id'];
        $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return intval($result['total'] ?? 0);
    } catch (PDOException $e) {
        logCartError('Error getting cart count', $e);
        return 0;
    }
}

/**
 * Get detailed information about cart items
 * 
 * @return array Cart items with product details
 */
function getCartItems() {
    try {
        $pdo = getPDO();
        
        if (!$pdo || !isset($_SESSION['session_id'])) {
            return [];
        }
        
        global $log;
        $sessionId = $_SESSION['session_id'];
        $log->info('Fetching cart items for session', ['session_id' => $sessionId]);
        
        $stmt = $pdo->prepare("
            SELECT 
                c.id as cart_id,
                c.product_id,
                c.quantity,
                p.name,
                p.price,
                p.image,
                (p.price * c.quantity) as item_total
            FROM cart c 
            JOIN products p ON c.product_id = p.id 
            WHERE c.session_id = :sessionId
            ORDER BY c.id DESC
        ");
        
        $sessionId = $_SESSION['session_id'];
        $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
        $stmt->execute();
        
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $log->info('Cart items fetched successfully', ['count' => count($items)]);
        
        return $items;
    } catch (PDOException $e) {
        $log->error('Error fetching cart items', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return [];
    }
}

/**
 * Calculate cart totals
 * 
 * @param array $cartItems Optional array of cart items, fetched if not provided
 * @return array Cart totals including subtotal, shipping, VAT, and total
 */
function calculateCartTotals($cartItems = null) {
    if ($cartItems === null) {
        $cartItems = getCartItems();
    }
    
    $subtotal = 0;
    foreach ($cartItems as $item) {
        $subtotal += $item['price'] * $item['quantity'];
    }
    
    $shipping = empty($cartItems) ? 0 : SHIPPING_COST;
    $vat = $subtotal * VAT_RATE;
    $total = $subtotal + $shipping + $vat;
    
    return [
        'subtotal' => $subtotal,
        'shipping' => $shipping,
        'vat' => $vat,
        'total' => $total,
        'formatted' => [
            'subtotal' => number_format($subtotal, 2, ',', '.'),
            'shipping' => number_format($shipping, 2, ',', '.'),
            'vat' => number_format($vat, 2, ',', '.'),
            'total' => number_format($total, 2, ',', '.')
        ]
    ];
}

/**
 * Add a product to the cart
 * 
 * @param int $productId Product ID to add
 * @param int $quantity Quantity to add (default 1)
 * @return array Response with success status and message
 */
function addToCart($productId, $quantity = 1) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
        
        // Validate product exists
        $stmt = $pdo->prepare("SELECT id, price FROM products WHERE id = :productId");
        $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
        $stmt->execute();
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            throw new Exception('Product not found');
        }
        
        // Validate quantity
        $quantity = max(1, intval($quantity));
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Check if product already exists in cart
        $sessionId = $_SESSION['session_id'];
        $stmt = $pdo->prepare("
            SELECT quantity 
            FROM cart 
            WHERE session_id = :sessionId AND product_id = :productId
        ");
        
        $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
        $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
        $stmt->execute();
        $cartItem = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($cartItem) {
            // Update existing cart item
            $newQuantity = $cartItem['quantity'] + $quantity;
            $stmt = $pdo->prepare("
                UPDATE cart 
                SET quantity = :quantity 
                WHERE session_id = :sessionId AND product_id = :productId
            ");
            
            $stmt->bindParam(':quantity', $newQuantity, PDO::PARAM_INT);
            $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
            $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
            $stmt->execute();
        } else {
            // Insert new cart item
            $stmt = $pdo->prepare("
                INSERT INTO cart (session_id, product_id, quantity, created_at) 
                VALUES (:sessionId, :productId, :quantity, NOW())
            ");
            
            $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
            $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
            $stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);
            $stmt->execute();
        }
        
        // Update session cart (for backward compatibility)
        if (!isset($_SESSION['cart'][$productId])) {
            $_SESSION['cart'][$productId] = ['quantity' => $quantity];
        } else {
            $_SESSION['cart'][$productId]['quantity'] += $quantity;
        }
        
        // Commit transaction
        $pdo->commit();
        
        // Get updated cart count
        $count = getCartCount();
        
        return [
            'success' => true,
            'count' => $count,
            'message' => 'Product toegevoegd aan winkelwagen'
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        logCartError('Error adding product to cart', $e);
        
        return [
            'success' => false,
            'message' => 'Could not add product to cart: ' . $e->getMessage()
        ];
    }
}

/**
 * Remove a product from the cart
 * 
 * @param int $productId Product ID to remove
 * @return array Response with success status and updated totals
 */
function removeFromCart($productId) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
        
        $sessionId = $_SESSION['session_id'];
        $productId = intval($productId);
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Remove item from cart table
        $stmt = $pdo->prepare("
            DELETE FROM cart 
            WHERE session_id = :sessionId AND product_id = :productId
        ");
        
        $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
        $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
        $stmt->execute();
        
        // Remove from session cart
        if (isset($_SESSION['cart'][$productId])) {
            unset($_SESSION['cart'][$productId]);
        }
        
        // Get updated cart items and totals
        $cartItems = getCartItems();
        $totals = calculateCartTotals($cartItems);
        
        // Commit transaction
        $pdo->commit();
        
        return [
            'success' => true,
            'count' => getCartCount(),
            'message' => 'Product removed from cart',
            'totals' => $totals['formatted'],
            'items' => $cartItems
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        logCartError('Error removing product from cart', $e);
        
        return [
            'success' => false,
            'message' => 'Could not remove product from cart: ' . $e->getMessage()
        ];
    }
}

/**
 * Update the quantity of a product in the cart
 * 
 * @param int $productId Product ID to update
 * @param int $quantity New quantity
 * @return array Response with success status and updated totals
 */
function updateCartQuantity($productId, $quantity) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            throw new Exception('Database connection failed');
        }
        
        $sessionId = $_SESSION['session_id'];
        $productId = intval($productId);
        $quantity = max(1, intval($quantity)); // Ensure quantity is at least 1
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Update cart table
        $stmt = $pdo->prepare("
            UPDATE cart 
            SET quantity = :quantity 
            WHERE session_id = :sessionId AND product_id = :productId
        ");
        
        $stmt->bindParam(':quantity', $quantity, PDO::PARAM_INT);
        $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
        $stmt->bindParam(':productId', $productId, PDO::PARAM_INT);
        $stmt->execute();
        
        // Update session cart
        if (isset($_SESSION['cart'][$productId])) {
            $_SESSION['cart'][$productId]['quantity'] = $quantity;
        }
        
        // Get updated cart items and totals
        $cartItems = getCartItems();
        $totals = calculateCartTotals($cartItems);
        
        // Get item total for the updated product
        $itemTotal = 0;
        foreach ($cartItems as $item) {
            if ($item['product_id'] == $productId) {
                $itemTotal = $item['item_total'];
                break;
            }
        }
        
        // Commit transaction
        $pdo->commit();
        
        return [
            'success' => true,
            'count' => getCartCount(),
            'itemTotal' => number_format($itemTotal, 2, ',', '.'),
            'totals' => $totals['formatted'],
            'items' => $cartItems
        ];
        
    } catch (Exception $e) {
        // Rollback transaction on error
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        logCartError('Error updating cart quantity', $e);
        
        return [
            'success' => false,
            'message' => 'Could not update quantity: ' . $e->getMessage()
        ];
    }
}

/**
 * Clear all items from the cart
 * 
 * @return bool Success status
 */
function clearCart() {
    try {
        global $pdo;
        $log = Logger::get('cart_handler');
        
        if (!isset($_SESSION['session_id'])) {
            $log->warning('No session ID found when clearing cart');
            return false;
        }
        
        $sessionId = $_SESSION['session_id'];
        $log->info('Clearing cart', ['session_id' => $sessionId]);
        
        // Delete from database
        $stmt = $pdo->prepare("DELETE FROM cart WHERE session_id = :sessionId");
        $stmt->bindParam(':sessionId', $sessionId, PDO::PARAM_STR);
        $stmt->execute();
        
        // Clear session cart
        $_SESSION['cart'] = [];
        
        $log->info('Cart cleared successfully', ['session_id' => $sessionId]);
        return true;
    } catch (PDOException $e) {
        $log = Logger::get('cart_handler');
        $log->error('Error clearing cart', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        return false;
    }
}

/**
 * Log cart errors
 * 
 * @param string $message Error message
 * @param Exception $exception The exception that occurred
 * @param array $context Additional context information
 * @return void
 */
function logCartError($message, Exception $exception, $context = []) {
    $logMessage = sprintf(
        "[Cart Error] %s: %s (Code: %s)",
        $message,
        $exception->getMessage(),
        $exception->getCode()
    );
    
    if (!empty($context)) {
        $logMessage .= "\nContext: " . json_encode($context);
    }
    
    if (function_exists('logError')) {
        logError($logMessage, 'error');
    } else {
        error_log($logMessage);
    }
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && 
    (!isset($_POST['action']) || 
     in_array($_POST['action'], ['add', 'remove', 'update_quantity', 'clear']))) {
    $action = $_POST['action'] ?? '';
    $productId = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
    
    // Set content type for all responses
    header('Content-Type: application/json');
    
    switch($action) {
        case 'add':
            $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
            $response = addToCart($productId, $quantity);
            echo json_encode($response);
            break;
            
        case 'remove':
            $response = removeFromCart($productId);
            echo json_encode($response);
            break;
            
        case 'update_quantity':
            $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
            $response = updateCartQuantity($productId, $quantity);
            echo json_encode($response);
            break;
            
        case 'clear':
            $response = clearCart();
            echo json_encode($response);
            break;
            
        default:
            echo json_encode([
                'success' => false, 
                'message' => 'Invalid action'
            ]);
    }
    exit;
}

// Remove any direct script execution at the end of the file
if (isset($_POST['action']) && basename($_SERVER['PHP_SELF']) === 'cart_handler.php') {
    $log->warning('Direct access to cart_handler.php detected');
    exit('Direct access not allowed');
}
