<?php
function generateMetaTags($data = []) {
    $defaults = [
        'title' => $GLOBALS['config']['site']['name'],
        'description' => $GLOBALS['config']['site']['description'],
        'image' => '/assets/images/img/Logo.png',
        'type' => 'website',
        'robots' => 'index, follow',
        'canonical' => null
    ];

    $data = array_merge($defaults, $data);
    
    // Ensure title includes site name if it's a specific page
    if ($data['title'] !== $defaults['title']) {
        $data['title'] .= ' | ' . $defaults['title'];
    }

    // Get current URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $current_url = $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    
    // Use provided canonical URL or current URL
    $canonical_url = $data['canonical'] ?? $current_url;

    return <<<HTML
    <!-- Primary Meta Tags -->
    <title>{$data['title']}</title>
    <meta name="title" content="{$data['title']}">
    <meta name="description" content="{$data['description']}">
    <meta name="robots" content="{$data['robots']}">
    <link rel="canonical" href="{$canonical_url}">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="{$data['type']}">
    <meta property="og:url" content="{$canonical_url}">
    <meta property="og:title" content="{$data['title']}">
    <meta property="og:description" content="{$data['description']}">
    <meta property="og:image" content="{$GLOBALS['config']['site']['url']}{$data['image']}">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{$canonical_url}">
    <meta property="twitter:title" content="{$data['title']}">
    <meta property="twitter:description" content="{$data['description']}">
    <meta property="twitter:image" content="{$GLOBALS['config']['site']['url']}{$data['image']}">
    
    <!-- Additional SEO tags -->
    <meta name="author" content="{$GLOBALS['config']['site']['name']}">
    <meta name="geo.region" content="NL">
    <meta name="geo.placename" content="{$GLOBALS['config']['site']['address']}">
    <link rel="alternate" hreflang="nl" href="{$canonical_url}">
    HTML;
}

function generateStructuredData($type, $data = []) {
    $structured_data = [];
    
    switch ($type) {
        case 'LocalBusiness':
            $structured_data = [
                "@context" => "https://schema.org",
                "@type" => "LocalBusiness",
                "name" => $GLOBALS['config']['site']['name'],
                "image" => $GLOBALS['config']['site']['url'] . "/assets/images/img/Logo.png",
                "description" => $GLOBALS['config']['site']['description'],
                "address" => [
                    "@type" => "PostalAddress",
                    "addressLocality" => $GLOBALS['config']['site']['address'],
                    "addressCountry" => "NL"
                ],
                "telephone" => $GLOBALS['config']['site']['phone'],
                "email" => $GLOBALS['config']['site']['email'],
                "url" => $GLOBALS['config']['site']['url']
            ];
            break;
            
        case 'Product':
            $structured_data = [
                "@context" => "https://schema.org",
                "@type" => "Product",
                "name" => $data['name'],
                "description" => $data['description'],
                "image" => $GLOBALS['config']['site']['url'] . $data['image'],
                "offers" => [
                    "@type" => "Offer",
                    "price" => $data['price'],
                    "priceCurrency" => "EUR",
                    "availability" => "https://schema.org/InStock"
                ]
            ];
            break;
            
        case 'Article':
            $structured_data = [
                "@context" => "https://schema.org",
                "@type" => "Article",
                "headline" => $data['title'],
                "description" => $data['description'],
                "image" => $GLOBALS['config']['site']['url'] . $data['image'],
                "datePublished" => $data['date'],
                "author" => [
                    "@type" => "Person",
                    "name" => $GLOBALS['config']['site']['name']
                ]
            ];
            break;
    }
    
    return '<script type="application/ld+json">' . json_encode($structured_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . '</script>';
}