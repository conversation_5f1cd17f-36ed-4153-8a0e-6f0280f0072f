<?php
/**
 * Site Configuration File
 *
 * Contains global site settings, navigation structure, and utility functions.
 * 
 * @version 1.0
 */

// Start session securely if not already started
if (session_status() === PHP_SESSION_NONE) {
    // Set secure session parameters
    $session_options = [
        'cookie_httponly' => true,
        'cookie_secure' => isset($_SERVER['HTTPS']), // Only set secure if HTTPS is used
        'cookie_samesite' => 'Lax',
        'use_strict_mode' => true,
        'gc_maxlifetime' => 3600 // 1 hour session lifetime
    ];
    
    session_start($session_options);
}

// Environment detection
$is_production = ($_SERVER['SERVER_NAME'] ?? 'localhost') !== 'localhost';

// Site configuration
$config = [
    // Basic site information
    'site' => [
        'name' => "<PERSON> Tjassens",
        'url' => $is_production ? "https://jacquelinetjassens.com" : "http://localhost",
        'description' => "Holistische behandelingen, massages en natuurlijke producten",
        'email' => "<EMAIL>",
        'phone' => "+31 6 41224141",
        'address' => "Beauty Club Nederland, Scheglaan 12, 2718 KZ Zoetermeer"
    ],
    
    // Application settings
    'settings' => [
        'debug' => !$is_production,
        'timezone' => 'Europe/Amsterdam',
        'locale' => 'nl_NL',
        'items_per_page' => 12,
        'tax_rate' => 0.21, // 21% VAT
        'currency' => 'EUR'
    ]
];

// Set timezone
date_default_timezone_set($config['settings']['timezone']);

// Encryption key for appointment IDs
define('ENCRYPTION_KEY', 'vsZhf2c+W2HiEyezWMKB+kxHnZ9k8xBlUCRfksscLpQLuG81ipkaM5bEYMgIGeNC');

// Define shipping cost
define('SHIPPING_COST', 4.95);

// Navigation menu structure
$navigation = [
    [
        "url" => "index.php", 
        "name" => "Home",
        "icon" => "home"
    ],
    [
        "url" => "shop.php", 
        "name" => "Shop", 
        "icon" => "shopping-bag",
        "submenu" => [
            ["url" => "shop.php?category=druppels20ml", "name" => "Druppels 20ml"],
            ["url" => "shop.php?category=druppels30ml", "name" => "Druppels (Glycerine)"],
            ["url" => "shop.php?category=cremes", "name" => "Crèmes"],
            ["url" => "shop.php?category=olierollers", "name" => "Olie rollers"]
        ]
    ],
    [
        "url" => "behandelingen.php", 
        "name" => "Behandelingen", 
        "icon" => "heart",
        "submenu" => [
            ["url" => "behandelingen/abhyanga-massage.php", "name" => "Abhyanga massage"],
            ["url" => "behandelingen/kruidenstempelmassage.php", "name" => "Kruidenstempel massage"],
            ["url" => "behandelingen/ontspanningsmassage.php", "name" => "Ontspannings massage"],
            ["url" => "behandelingen/stemvorktherapie.php", "name" => "Stemvork therapie"],
            ["url" => "behandelingen/tienermassage.php", "name" => "Tiener massage"],
            ["url" => "behandelingen/indian-foot-massage.php", "name" => "Indian Foot Massage"],
            ["url" => "behandelingen/go4balance-consult.php", "name" => "Go4Balance Consult"],
            ["url" => "welkomstconsult.php", "name" => "Welkomstconsult"],
            ["url" => "voordeelkaarten.php", "name" => "Voordeelkaarten"]
        ]
    ],
    [
        "url" => "workshops.php", 
        "name" => "Workshops", 
        "icon" => "book-open",
        "submenu" => [
            ["url" => "workshops/mild-in-het-wild-compassiewandeling.php", "name" => "Mild in het wild"],
            ["url" => "workshops/workshop-uit-je-hoofd-in-je-lijf.php", "name" => "Uit je hoofd in je lijf"]
        ]
    ],
    [
        "url" => "blog.php", 
        "name" => "Blog", 
        "icon" => "edit",
        "submenu" => [
            ["url" => "blog/mijneigenreis.php", "name" => "Mijn eigen reis"],
            ["url" => "blog/adem.php", "name" => "Jouw adem, jouw medicijn"]
        ]
    ],
    [
        "url" => "overmij.php", 
        "name" => "Over mij",
        "icon" => "user"
    ],
    [
        "url" => "contact.php", 
        "name" => "Contact",
        "icon" => "mail"
    ]
];

// Footer structure
$footer = [
    'sections' => [
        [
            'title' => 'Producten',
            'links' => [
                ["url" => "shop.php?category=druppels20ml", "name" => "Druppels 20ml"],
                ["url" => "shop.php?category=druppels30ml", "name" => "Druppels (Glycerine)"],
                ["url" => "shop.php?category=cremes", "name" => "Crèmes"],
                ["url" => "shop.php?category=olierollers", "name" => "Olie rollers"]
            ]
        ],
        [
            'title' => 'Behandelingen',
            'links' => [
                ["url" => "behandelingen/abhyanga-massage.php", "name" => "Abhyanga massage"],
                ["url" => "behandelingen/kruidenstempelmassage.php", "name" => "Kruidenstempel massage"],
                ["url" => "behandelingen/ontspanningsmassage.php", "name" => "Ontspannings massage"],
                ["url" => "behandelingen/stemvorktherapie.php", "name" => "Stemvork therapie"],
                ["url" => "behandelingen/tienermassage.php", "name" => "Tiener massage"],
                ["url" => "voordeelkaarten.php", "name" => "Voordeelkaarten"]
            ]
        ],
        [
            'title' => 'Links',
            'links' => [
                ["url" => "afspraak-maken.php", "name" => "Afspraak maken"],
                ["url" => "contact.php", "name" => "Contact"],
                ["url" => "privacy-policy.php", "name" => "Privacybeleid"],
                ["url" => "algemene-voorwaarden.php", "name" => "Algemene voorwaarden"]
            ]
        ]
    ],
    'copyright' => '© ' . date('Y') . ' ' . $config['site']['name'] . '. Alle rechten voorbehouden.'
];

// Category definitions
$product_categories = [
    'druppels20ml' => [
        'name' => 'Druppels 20ml',
        'description' => 'Bach bloesem druppels in 20ml flesje',
        'image' => 'images/categories/druppels20ml.jpg'
    ],
    'druppels30ml' => [
        'name' => 'Druppels (Glycerine)',
        'description' => 'Bach bloesem druppels met glycerine in 30ml flesje',
        'image' => 'images/categories/druppels30ml.jpg'
    ],
    'cremes' => [
        'name' => 'Crèmes',
        'description' => 'Natuurlijke crèmes voor verschillende huidtypes',
        'image' => 'images/categories/cremes.jpg'
    ],
    'olierollers' => [
        'name' => 'Olie rollers',
        'description' => 'Essentiële olie rollers voor onderweg',
        'image' => 'images/categories/olierollers.jpg'
    ]
];

/**
 * Checks if a user is currently logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Gets the current user's information
 *
 * @return array|null User data array or null if not logged in
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    // In a production app, you would fetch user details from database
    // This is a placeholder for demonstration
    $userId = $_SESSION['user_id'];
    
    try {
        require_once 'db.php';
        $pdo = getPDO();
        
        if (!$pdo) {
            return [
                'id' => $userId,
                'name' => $_SESSION['user_name'] ?? 'User'
            ];
        }
        
        $stmt = $pdo->prepare("SELECT id, username, email, first_name, last_name, created_at FROM users WHERE id = :userId");
        $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            // User not found in database but has session - this is unusual
            // Log this situation and return basic info
            error_log("User with ID {$userId} has active session but not found in database");
            return [
                'id' => $userId,
                'name' => $_SESSION['user_name'] ?? 'User'
            ];
        }
        
        // Add display name
        $user['display_name'] = !empty($user['first_name']) ? 
            $user['first_name'] . ' ' . $user['last_name'] : 
            $user['username'];
            
        return $user;
        
    } catch (Exception $e) {
        error_log("Error fetching user data: " . $e->getMessage());
        
        // Fallback to session data
        return [
            'id' => $userId,
            'name' => $_SESSION['user_name'] ?? 'User'
        ];
    }
}

/**
 * Gets the active page identifier based on current URL
 *
 * @return string The current page identifier
 */
function getCurrentPage() {
    $path = $_SERVER['PHP_SELF'];
    $pathParts = explode('/', $path);
    return end($pathParts);
}

/**
 * Checks if the given URL matches the current page
 *
 * @param string $url The URL to check
 * @return bool True if current page matches the URL
 */
function isCurrentPage($url) {
    $currentPage = getCurrentPage();
    $urlParts = explode('?', $url);
    $urlPage = basename($urlParts[0]);
    
    return $currentPage === $urlPage;
}

/**
 * Safely outputs text with HTML escaping
 *
 * @param string $text The text to output
 * @return void
 */
function e($text) {
    echo htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

/**
 * Generates a CSRF token and stores it in the session
 *
 * @return string The generated CSRF token
 */
function generateCsrfToken() {
    if (empty($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Formats currency values according to locale settings
 *
 * @param float $amount The amount to format
 * @return string Formatted currency string
 */
function formatCurrency($amount) {
    return '€' . number_format($amount, 2, ',', '.');
}

/**
 * Logs application errors
 *
 * @param string $message Error message
 * @param string $level Error level (error, warning, info)
 * @return void
 */
function logError($message, $level = 'error') {
    $logFile = __DIR__ . '/../logs/' . date('Y-m-d') . '.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    // Only write to log file in production to avoid local file permission issues
    if ($GLOBALS['is_production']) {
        error_log($logMessage, 3, $logFile);
    } else {
        error_log($message);
    }
}

/**
 * Gets the base URL for the site
 *
 * @return string The base URL
 */
function getBaseUrl() {
    return $GLOBALS['config']['site']['url'];
}

/**
 * Redirects to a new page
 *
 * @param string $path The path to redirect to
 * @return void
 */
function redirect($path) {
    $baseUrl = getBaseUrl();
    $url = $baseUrl . '/' . ltrim($path, '/');
    header("Location: {$url}");
    exit;
}
?>
