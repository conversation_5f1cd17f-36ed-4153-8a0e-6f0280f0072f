<?php
/**
 * Login Handler Script
 * 
 * Processes user login requests and manages authentication.
 */
require_once 'auth.php';

// Only process POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    // Redirect non-POST requests to the home page
    header('Location: ../index.php');
    exit;
}

// CSRF protection
if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'] ?? '', $_POST['csrf_token'])) {
    $_SESSION['login_error'] = 'Invalid request. Please try again.';
    header('Location: ../index.php');
    exit;
}

// Get and validate input
$username = trim($_POST['username'] ?? '');
$password = $_POST['password'] ?? '';
$remember = isset($_POST['remember']) && $_POST['remember'] === 'on';

// Validate required fields
if (empty($username) || empty($password)) {
    $_SESSION['login_error'] = 'Username and password are required';
    $_SESSION['form_data'] = ['username' => $username]; // Remember the username but not the password
    header('Location: ../index.php');
    exit;
}

// Rate limiting (simple implementation)
$ipAddress = $_SERVER['REMOTE_ADDR'];
$currentTime = time();
$timeWindow = 15 * 60; // 15 minutes
$maxAttempts = 5;

// Initialize or update the login attempts tracking
if (!isset($_SESSION['login_attempts'])) {
    $_SESSION['login_attempts'] = [
        'count' => 0,
        'first_attempt' => $currentTime,
        'ip' => $ipAddress
    ];
}

// Reset attempts if time window has passed or IP changed
if (($currentTime - $_SESSION['login_attempts']['first_attempt']) > $timeWindow || 
    $_SESSION['login_attempts']['ip'] !== $ipAddress) {
    $_SESSION['login_attempts'] = [
        'count' => 0,
        'first_attempt' => $currentTime,
        'ip' => $ipAddress
    ];
}

// Check for too many attempts
if ($_SESSION['login_attempts']['count'] >= $maxAttempts) {
    $timeLeft = $timeWindow - ($currentTime - $_SESSION['login_attempts']['first_attempt']);
    $minutesLeft = ceil($timeLeft / 60);
    
    $_SESSION['login_error'] = "Too many failed attempts. Please try again in {$minutesLeft} minutes.";
    header('Location: ../index.php');
    exit;
}

// Attempt login
if (loginUser($username, $password)) {
    // Reset login attempts on successful login
    unset($_SESSION['login_attempts']);
    unset($_SESSION['login_error']);
    
    // Set remember-me cookie if requested
    if ($remember) {
        // Generate a secure token instead of storing the username
        $selector = bin2hex(random_bytes(8));
        $validator = bin2hex(random_bytes(32));
        
        // Store the hashed validator in the database (implement this function)
        $expires = time() + (86400 * 30); // 30 days
        storeRememberToken($_SESSION['user_id'], $selector, hash('sha256', $validator), date('Y-m-d H:i:s', $expires));
        
        // Set the cookie with the selector and validator
        setcookie(
            'remember_token',
            $selector . ':' . $validator,
            $expires,
            '/',
            '',  // domain
            true, // secure
            true  // httponly
        );
    }
    
    // Log successful login
    $userId = $_SESSION['user_id'];
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    logUserActivity($userId, 'login', "Successful login from IP: $ipAddress, User-Agent: $userAgent");
    
    // Redirect to current page or home
    $redirect = $_SERVER['HTTP_REFERER'] ?? '../index.php';
    unset($_SESSION['redirect_after_login']);
    
    header("Location: $redirect");
    exit;
} else {
    // Increment failed attempts
    $_SESSION['login_attempts']['count']++;
    
    // Set error message based on attempts
    $attemptsLeft = $maxAttempts - $_SESSION['login_attempts']['count'];
    if ($attemptsLeft <= 3 && $attemptsLeft > 0) {
        $_SESSION['login_error'] = "Invalid username or password. $attemptsLeft attempts remaining.";
    } else {
        $_SESSION['login_error'] = 'Invalid username or password.';
    }
    
    // Log failed login attempt
    logFailedLogin($username, $ipAddress);
    
    // Redirect back to login page
    $_SESSION['form_data'] = ['username' => $username]; // Remember the username but not the password
    header('Location: ../index.php');
    exit;
}

/**
 * Stores a remember-me token in the database
 * 
 * @param int $userId User ID
 * @param string $selector Token selector
 * @param string $hashedValidator Hashed token validator
 * @param string $expires Expiration date (Y-m-d H:i:s format)
 * @return bool Success or failure
 */
function storeRememberToken($userId, $selector, $hashedValidator, $expires) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            return false;
        }
        
        // First delete any existing tokens for this user
        $deleteStmt = $pdo->prepare("DELETE FROM auth_tokens WHERE user_id = :userId AND token_type = 'remember'");
        $deleteStmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $deleteStmt->execute();
        
        // Insert new token
        $insertStmt = $pdo->prepare("
            INSERT INTO auth_tokens (user_id, selector, hashed_validator, expires, token_type) 
            VALUES (:userId, :selector, :hashedValidator, :expires, 'remember')
        ");
        
        $insertStmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $insertStmt->bindParam(':selector', $selector, PDO::PARAM_STR);
        $insertStmt->bindParam(':hashedValidator', $hashedValidator, PDO::PARAM_STR);
        $insertStmt->bindParam(':expires', $expires, PDO::PARAM_STR);
        
        return $insertStmt->execute();
        
    } catch (PDOException $e) {
        error_log("Error storing remember token: " . $e->getMessage());
        return false;
    }
}

/**
 * Logs user activity
 * 
 * @param int $userId User ID
 * @param string $action Action performed
 * @param string $details Additional details
 * @return bool Success or failure
 */
function logUserActivity($userId, $action, $details = '') {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO user_activity_log (user_id, action, details, ip_address, created_at) 
            VALUES (:userId, :action, :details, :ipAddress, NOW())
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'];
        
        $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':action', $action, PDO::PARAM_STR);
        $stmt->bindParam(':details', $details, PDO::PARAM_STR);
        $stmt->bindParam(':ipAddress', $ipAddress, PDO::PARAM_STR);
        
        return $stmt->execute();
        
    } catch (PDOException $e) {
        error_log("Error logging user activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Logs failed login attempts
 * 
 * @param string $username Attempted username
 * @param string $ipAddress IP address of the attempt
 * @return bool Success or failure
 */
function logFailedLogin($username, $ipAddress) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            return false;
        }
        
        $stmt = $pdo->prepare("
            INSERT INTO failed_logins (username, ip_address, attempt_time) 
            VALUES (:username, :ipAddress, NOW())
        ");
        
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->bindParam(':ipAddress', $ipAddress, PDO::PARAM_STR);
        
        return $stmt->execute();
        
    } catch (PDOException $e) {
        error_log("Error logging failed login: " . $e->getMessage());
        return false;
    }
}
?>
