<?php
// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("FATAL ERROR: " . print_r($error, true));
        
        // Send JSON error response if possible
        if (!headers_sent()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'A fatal error occurred: ' . $error['message']
            ]);
        }
    }
});

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'Logger.php';
$log = Logger::get('appointment_payment_processor');
$log->info('Starting appointment payment processor');

try {
    $log->info('Loading includes');
    require_once 'config.php';
    require_once 'db.php';
    
    // Explicitly initialize the database connection
    $pdo = getPDO();
    
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
    
    $log->info('Database connection established');
    require_once 'mollie_config.php';
    $log->info('All includes loaded successfully');
} catch (Exception $e) {
    $log->error('Failed loading includes', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error loading required files'
    ]);
    exit;
}

// Log request details
$log->info('Request received', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '',
    'post_data' => $_POST
]);

// Process appointment payment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'process_appointment_payment') {
    $log->info('Processing appointment payment action detected');
    
    try {
        // Get session ID
        $session_id = session_id();
        $log->info('Processing appointment payment', ['session_id' => $session_id]);
        
        // Get appointment details
        $service_type = isset($_POST['service_type']) ? trim($_POST['service_type']) : '';
        $service_id = 0;
        $service_name = '';
        $service_price = 0;

        if ($service_type === 'behandeling') {
            $service_id = isset($_POST['behandeling_id']) ? (int)$_POST['behandeling_id'] : 0;
            
            // Instead of querying the behandelingen table, use a predefined array
            $behandelingen = [
                1 => ['name' => 'Abhyanga massage', 'price' => 95.00],
                2 => ['name' => 'Kruidenstempelmassage 60 min', 'price' => 75.00],
                3 => ['name' => 'Kruidenstempelmassage 90 min', 'price' => 100.00],
                4 => ['name' => 'Ontspanningsmassage 30 min', 'price' => 45.00],
                5 => ['name' => 'Ontspanningsmassage 60 min', 'price' => 60.00],
                6 => ['name' => 'Ontspanningsmassage 90 min', 'price' => 90.00],
                7 => ['name' => 'Stemvorktherapie', 'price' => 55.00],
                8 => ["name" => "Tienermassage", "price" => 45.00],
                9 => ["name" => "Go4Balance Consult", "price" => 45.00],
                10 => ["name" => "Indian Foot Massage", "price" => 55.00]
            ];
            
            if (!isset($behandelingen[$service_id])) {
                throw new Exception('Invalid service selected');
            }
            
            $service_name = $behandelingen[$service_id]['name'];
            $service_price = $behandelingen[$service_id]['price'];
        } elseif ($service_type === 'workshop') {
            $service_id = isset($_POST['workshop_id']) ? (int)$_POST['workshop_id'] : 0;
            
            // Instead of querying the workshops table, use a predefined array
            $workshops = [
                10 => ['name' => 'Mild in het wild', 'price' => 45.00],
                11 => ['name' => 'Uit je hoofd in je lijf', 'price' => 39.95]
            ];
            
            if (!isset($workshops[$service_id])) {
                throw new Exception('Invalid workshop selected');
            }
            
            $service_name = $workshops[$service_id]['name'];
            $service_price = $workshops[$service_id]['price'];
        } elseif ($service_type === 'welkomstconsult') {
            $service_id = isset($_POST['welkomstconsult_id']) ? (int)$_POST['welkomstconsult_id'] : 0;
            
            // Welkomstconsult options
            $welkomstconsult = [
                12 => ['name' => 'Shiro Abhyanga (Welkomstconsult)', 'price' => 18.00],
                13 => ['name' => 'Pad Abhyanga (Welkomstconsult)', 'price' => 18.00]
            ];
            
            if (!isset($welkomstconsult[$service_id])) {
                throw new Exception('Invalid welkomstconsult selected');
            }
            
            $service_name = $welkomstconsult[$service_id]['name'];
            $service_price = $welkomstconsult[$service_id]['price'];
        } else {
            throw new Exception('Invalid service type');
        }
        
        $appointment_date = isset($_POST['appointment_date']) ? trim($_POST['appointment_date']) : '';
        $appointment_time = isset($_POST['appointment_time']) ? trim($_POST['appointment_time']) : '';
        
        if (empty($appointment_date) || empty($appointment_time)) {
            throw new Exception('Please select a valid date and time');
        }
        
        $log->info('Appointment details', [
            'service_type' => $service_type,
            'service_id' => $service_id,
            'service_name' => $service_name,
            'service_price' => $service_price,
            'appointment_date' => $appointment_date,
            'appointment_time' => $appointment_time
        ]);
        
        // Get customer details
        $customer_name = isset($_POST['client_name']) ? trim($_POST['client_name']) : '';
        $customer_email = isset($_POST['client_email']) ? trim($_POST['client_email']) : '';
        $customer_phone = isset($_POST['client_phone']) ? trim($_POST['client_phone']) : '';
        
        // Validate customer details
        if (empty($customer_name) || empty($customer_email)) {
            throw new Exception('Please fill in all required fields');
        }
        
        // Save appointment to database
        $stmt = $pdo->prepare("INSERT INTO appointments (
                                service_type,
                                service_id,
                                client_name,
                                client_email,
                                client_phone,
                                appointment_date,
                                appointment_time,
                                status,
                                notes
                              ) VALUES (
                                ?, ?, ?, ?, ?, ?, ?, 'pending', ?
                              )");

        $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

        $stmt->execute([
            $service_type,
            $service_id,
            $customer_name,
            $customer_email,
            $customer_phone,
            $appointment_date,
            $appointment_time,
            $notes
        ]);

        $appointment_db_id = $pdo->lastInsertId();
        $log->info('Appointment saved to database', ['appointment_db_id' => $appointment_db_id]);
        
        // Create payment description
        $payment_description = "Afspraak " . $service_name . " - " . $appointment_date;

        // Encrypt the appointment ID for the redirect URL
        $encryption_key = ENCRYPTION_KEY;
        $encrypted_id = base64_encode(openssl_encrypt(
            $appointment_db_id,
            'AES-256-CBC',
            $encryption_key,
            0,
            substr(hash('sha256', $encryption_key), 0, 16)
        ));

        // Create payment link with encrypted ID
        $redirect_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
                        "://" . $_SERVER['HTTP_HOST'] . "/appointment_confirmation.php?id=" . urlencode($encrypted_id);

        $log->info('Creating payment link', [
            'appointment_id' => $appointment_db_id,
            'total' => $service_price,
            'description' => $payment_description,
            'redirect_url' => $redirect_url
        ]);

        // Create payment with Mollie
        $payment_url = createPaymentLink($appointment_db_id, $service_price, $payment_description, $redirect_url, [
            'appointment_db_id' => $appointment_db_id,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email
        ]);

        if (empty($payment_url)) {
            $log->error('Empty payment URL received', [
                'appointment_id' => $appointment_db_id
            ]);
            throw new Exception('Failed to create payment URL');
        }

        $log->info('Payment link created successfully', [
            'appointment_id' => $appointment_db_id,
            'payment_url' => $payment_url
        ]);

        $log->info('Preparing JSON response');
        $response = [
            'success' => true,
            'redirectUrl' => $payment_url,
            'message' => 'Payment processing initiated'
        ];
        $log->info('Sending JSON response', $response);

        header('Content-Type: application/json');
        echo json_encode($response);
        $log->info('Response sent');
        exit;

    } catch (Exception $e) {
        $log->error('Payment processing error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
} else {
    $log->error('Invalid request method or action', [
        'method' => $_SERVER['REQUEST_METHOD'],
        'action' => $_POST['action'] ?? 'not set'
    ]);

    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request'
    ]);
    exit;
}
