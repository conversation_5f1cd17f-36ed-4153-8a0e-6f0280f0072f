<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/mollie_config.php';

/**
 * Log webhook events with detailed information
 */
function logWebhookEvent($message, $data = []) {
    $logFile = __DIR__ . '/logs/webhook_events.log';
    $logDir = dirname($logFile);
    
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $requestIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // Sanitize sensitive data
    if (isset($data['payment']) && isset($data['payment']->details)) {
        $data['payment']->details = '[REDACTED]';
    }
    
    // Format log entry as JSON for easier parsing
    $logEntry = [
        'timestamp' => $timestamp,
        'ip' => $requestIp,
        'message' => $message,
        'data' => $data
    ];
    
    file_put_contents(
        $logFile, 
        json_encode($logEntry, JSON_PRETTY_PRINT) . "\n---\n", 
        FILE_APPEND
    );
}

/**
 * Verify webhook request is legitimate
 */
function verifyWebhookRequest() {
    // Verify the request is POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        return false;
    }
    
    // Verify payment ID exists
    if (empty($_POST['id'])) {
        return false;
    }
    
    // Verify IP is from Mollie (optional but adds security)
    $mollieIps = [
        '************/24',
        '*************/24',
        '2001:67c:2d0::/48',
    ];
    
    $clientIp = $_SERVER['REMOTE_ADDR'];
    $isValidIp = false;
    
    foreach ($mollieIps as $range) {
        if (strpos($range, '/') !== false) {
            // CIDR notation
            list($subnet, $bits) = explode('/', $range);
            $ip = inet_pton($clientIp);
            $subnet = inet_pton($subnet);
            $mask = ~(pow(2, (32 - $bits)) - 1);
            $isValidIp = ((ip2long($clientIp) & $mask) == (ip2long($subnet) & $mask));
        } else {
            // Single IP
            $isValidIp = ($clientIp === $range);
        }
        
        if ($isValidIp) break;
    }
    
    // In development, you might want to bypass IP check
    if (ENVIRONMENT === 'development') {
        $isValidIp = true;
    }
    
    return $isValidIp;
}

/**
 * Process payment status update
 */
function processPaymentUpdate($payment, $pdo) {
    $orderId = $payment->metadata->order_id ?? null;
    
    if (!$orderId) {
        throw new Exception("No order ID found in payment metadata");
    }
    
    // Get current order status
    $stmt = $pdo->prepare("SELECT status, payment_id FROM orders WHERE order_id = ?");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        throw new Exception("Order not found: $orderId");
    }
    
    // Verify payment ID matches what we have stored
    if ($order['payment_id'] !== $payment->id) {
        throw new Exception("Payment ID mismatch for order $orderId");
    }
    
    // Map Mollie status to our system status
    $newStatus = mapMollieStatus($payment->status);
    
    // Only update if status has changed
    if ($newStatus !== $order['status']) {
        $pdo->beginTransaction();
        
        try {
            // Update order status
            $stmt = $pdo->prepare("
                UPDATE orders 
                SET status = ?, 
                    paid_at = CASE WHEN ? = 'paid' THEN NOW() ELSE paid_at END,
                    updated_at = NOW()
                WHERE order_id = ?
            ");
            $stmt->execute([$newStatus, $newStatus, $orderId]);
            
            // Add status history entry
            $stmt = $pdo->prepare("
                INSERT INTO order_status_history 
                (order_id, status, payment_id, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$orderId, $newStatus, $payment->id]);
            
            $pdo->commit();
            
            logWebhookEvent("Status updated", [
                'order_id' => $orderId,
                'old_status' => $order['status'],
                'new_status' => $newStatus,
                'mollie_status' => $payment->status
            ]);
            
            return $newStatus;
        } catch (Exception $e) {
            $pdo->rollBack();
            throw $e;
        }
    }
    
    return $order['status'];
}

/**
 * Map Mollie payment status to our system status
 */
function mapMollieStatus($mollieStatus) {
    $statusMap = [
        'paid' => 'paid',
        'pending' => 'pending',
        'open' => 'pending',
        'failed' => 'failed',
        'canceled' => 'cancelled',
        'expired' => 'expired',
        'refunded' => 'refunded',
        'charged_back' => 'chargeback'
    ];
    
    return $statusMap[$mollieStatus] ?? 'unknown';
}

/**
 * Send order confirmation email
 */
function sendOrderConfirmationEmail($orderId, $pdo) {
    // Get order details
    $stmt = $pdo->prepare("
        SELECT o.*, 
               c.email as customer_email,
               c.first_name as customer_first_name,
               c.last_name as customer_last_name
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        WHERE o.order_id = ?
    ");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        logWebhookEvent("Order not found for email", ['order_id' => $orderId]);
        return false;
    }
    
    // Check if email already sent
    if ($order['email_sent'] == 1) {
        logWebhookEvent("Email already sent", ['order_id' => $orderId]);
        return true;
    }
    
    // Get customer email - check all possible sources
    $email = $order['customer_email'] ?? null;
    
    // If no email in order table directly, try to parse from shipping_address JSON
    if (!$email && isset($order['shipping_address'])) {
        $addressData = json_decode($order['shipping_address'], true);
        $email = $addressData['email'] ?? null;
    }
    
    if (!$email) {
        logWebhookEvent("No customer email found", ['order_id' => $orderId]);
        return false;
    }
    
    // Get customer name
    $customerName = $order['customer_first_name'] ?? '';
    if (empty($customerName) && isset($order['shipping_address'])) {
        $addressData = json_decode($order['shipping_address'], true);
        $customerName = $addressData['name'] ?? 'Geachte klant';
    }
    
    // Get order items
    $stmt = $pdo->prepare("
        SELECT oi.*, p.image_url, p.sku
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = ?
    ");
    $stmt->execute([$orderId]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Prepare email template
    $orderDate = date('d-m-Y H:i', strtotime($order['created_at']));
    
    // Build items HTML
    $itemsHtml = '';
    foreach ($items as $item) {
        $price = number_format($item['price'], 2, ',', '.');
        $totalPrice = number_format($item['total_price'], 2, ',', '.');
        $imageUrl = $item['image_url'] ? 'https://www.jacquelinetjassens.com/' . $item['image_url'] : '';
        
        $itemsHtml .= "
        <tr>
            <td style='padding: 10px; border-bottom: 1px solid #eee;'>
                " . ($imageUrl ? "<img src='$imageUrl' width='50' alt=''>" : "") . "
            </td>
            <td style='padding: 10px; border-bottom: 1px solid #eee;'>{$item['product_name']}</td>
            <td style='padding: 10px; border-bottom: 1px solid #eee;'>€{$price}</td>
            <td style='padding: 10px; border-bottom: 1px solid #eee;'>{$item['quantity']}</td>
            <td style='padding: 10px; border-bottom: 1px solid #eee;'>€{$totalPrice}</td>
        </tr>";
    }
    
    // Load email template
    $templateFile = __DIR__ . '/templates/order_confirmation_email.html';
    if (file_exists($templateFile)) {
        $message = file_get_contents($templateFile);
    } else {
        // Fallback template
        $message = '<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Bevestiging bestelling #{{ORDER_ID}}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; }
                .header { background-color: #d5bc5a; padding: 20px; color: white; }
                .content { padding: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th { text-align: left; padding: 10px; background-color: #f2f2f2; }
                .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
                .footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Bedankt voor uw bestelling!</h1>
                </div>
                <div class="content">
                    <p>Beste {{CUSTOMER_NAME}},</p>
                    <p>Uw bestelling is succesvol afgerond en wordt zo spoedig mogelijk verwerkt.</p>
                    
                    <h2>Bestelling #{{ORDER_ID}}</h2>
                    <p>Datum: {{ORDER_DATE}}</p>
                    
                    <table>
                        <thead>
                            <tr>
                                <th></th>
                                <th>Product</th>
                                <th>Prijs</th>
                                <th>Aantal</th>
                                <th>Totaal</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{ORDER_ITEMS}}
                        </tbody>
                    </table>
                    
                    <div class="total">
                        <p><strong>Subtotaal:</strong> €{{SUBTOTAL}}</p>
                        <p><strong>Verzendkosten:</strong> €{{SHIPPING}}</p>
                        <p><strong>BTW (21%):</strong> €{{TAX}}</p>
                        <p><strong>Totaal:</strong> €{{TOTAL}}</p>
                    </div>
                    
                    <p>We sturen u een e-mail zodra uw bestelling is verzonden.</p>
                    <p>Met vriendelijke groet,<br>Jacqueline Tjassens</p>
                </div>
                <div class="footer">
                    <p>© {{YEAR}} Jacqueline Tjassens | <a href="https://www.jacquelinetjassens.com">www.jacquelinetjassens.com</a></p>
                </div>
            </div>
        </body>
        </html>';
    }
    
    // Replace placeholders
    $placeholders = [
        '{{ORDER_ID}}' => $orderId,
        '{{CUSTOMER_NAME}}' => $customerName,
        '{{ORDER_DATE}}' => $orderDate,
        '{{ORDER_ITEMS}}' => $itemsHtml,
        '{{SUBTOTAL}}' => number_format($order['subtotal_amount'] ?? 0, 2, ',', '.'),
        '{{SHIPPING}}' => number_format($order['shipping_amount'] ?? 0, 2, ',', '.'),
        '{{TAX}}' => number_format($order['tax_amount'] ?? 0, 2, ',', '.'),
        '{{TOTAL}}' => number_format($order['total_amount'] ?? 0, 2, ',', '.'),
        '{{YEAR}}' => date('Y')
    ];
    
    $message = str_replace(array_keys($placeholders), array_values($placeholders), $message);
    
    // Use our improved sendEmail function
    $customerEmailSent = sendEmail(
        "Bevestiging bestelling #$orderId",
        $message,
        "Bedankt voor uw bestelling #$orderId. Bekijk uw bestelling op onze website.",
        $email,
        $customerName
    );
    
    // Send notification to owner
    $ownerEmailSent = sendEmail(
        "Nieuwe bestelling #$orderId",
        $message,
        "Nieuwe bestelling #$orderId ontvangen."
    );
    
    if ($customerEmailSent || $ownerEmailSent) {
        // Mark email as sent in database
        $stmt = $pdo->prepare("UPDATE orders SET email_sent = 1 WHERE order_id = ?");
        $stmt->execute([$orderId]);
        
        logWebhookEvent("Confirmation email sent", ['order_id' => $orderId, 'email' => $email]);
        return true;
    } else {
        logWebhookEvent("Failed to send confirmation email", ['order_id' => $orderId, 'email' => $email]);
        return false;
    }
}

/**
 * Process post-payment actions like inventory updates
 */
function processPostPaymentActions($orderId, $status, $pdo) {
    if ($status !== 'paid') {
        return;
    }
    
    try {
        // Update inventory
        $stmt = $pdo->prepare("
            UPDATE products p
            JOIN order_items oi ON p.id = oi.product_id
            SET p.stock = p.stock - oi.quantity,
                p.updated_at = NOW()
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$orderId]);
        
        // Record inventory changes
        $stmt = $pdo->prepare("
            INSERT INTO inventory_log 
            (product_id, quantity_change, reason, reference_id, created_at)
            SELECT 
                oi.product_id, 
                -oi.quantity, 
                'order', 
                ?, 
                NOW()
            FROM order_items oi
            WHERE oi.order_id = ?
        ");
        $stmt->execute([$orderId, $orderId]);
        
        logWebhookEvent("Inventory updated for order", ['order_id' => $orderId]);
    } catch (Exception $e) {
        logWebhookEvent("Inventory update failed", [
            'order_id' => $orderId,
            'error' => $e->getMessage()
        ]);
        // Don't rethrow - this shouldn't block the webhook response
    }
}

// Main execution flow
try {
    // Start by logging the raw webhook
    logWebhookEvent("Webhook received", [
        'post' => $_POST,
        'server' => [
            'remote_addr' => $_SERVER['REMOTE_ADDR'],
            'request_method' => $_SERVER['REQUEST_METHOD'],
            'http_user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]
    ]);
    
    // Verify this is a valid webhook request
    if (!verifyWebhookRequest()) {
        logWebhookEvent("Invalid webhook request", ['post' => $_POST]);
        http_response_code(401);
        exit("Unauthorized");
    }
    
    // Retrieve the payment ID
    $paymentId = filter_input(INPUT_POST, 'id', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    if (!$paymentId) {
        throw new Exception("No payment ID provided");
    }
    
    // Initialize Mollie client
    $mollie = getMollieClient();
    if (!$mollie) {
        throw new Exception("Failed to initialize Mollie client");
    }
    
    // Get the payment from Mollie
    $payment = $mollie->payments->get($paymentId);
    if (!$payment) {
        throw new Exception("Failed to retrieve payment: $paymentId");
    }
    
    $orderId = $payment->metadata->order_id ?? null;
    if (!$orderId) {
        throw new Exception("No order ID found in payment metadata");
    }
    
    logWebhookEvent("Processing payment", [
        'payment_id' => $paymentId,
        'order_id' => $orderId,
        'status' => $payment->status
    ]);
    
    // Process the payment update
    $status = processPaymentUpdate($payment, $pdo);
    
    // If payment is paid, process additional actions
    if ($status === 'paid' && $payment->isPaid()) {
        // Send confirmation email
        sendOrderConfirmationEmail($orderId, $pdo);
        
        // Process inventory updates and other post-payment actions
        processPostPaymentActions($orderId, $status, $pdo);
    }
    
    // Return success response to Mollie
    http_response_code(200);
    echo "OK";
    exit;

} catch (Exception $e) {
    logWebhookEvent("Webhook processing error", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'payment_id' => $paymentId ?? null,
        'order_id' => $orderId ?? null
    ]);
    
    // Return 200 to prevent Mollie from retrying too aggressively
    // We've logged the error and can handle it manually if needed
    http_response_code(200);
    exit("Error logged");
}
