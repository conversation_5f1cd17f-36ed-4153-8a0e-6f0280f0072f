<?php
session_start();
require_once 'config.php';
require_once 'email_helper.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

try {
    // Debug: Log the raw POST data
    error_log("Questionnaire raw POST data: " . print_r($_POST, true));
    
    // The form data is directly in $_POST
    $formData = $_POST;
    
    // Build email content
    $message = "<h2>Nieuwe Vragenlijst Inzending</h2>";
    $message .= "<p><strong>Datum:</strong> " . date('d-m-Y H:i:s') . "</p>";
    
    // Debug: Log all form keys
    error_log("Form keys: " . implode(", ", array_keys($formData)));
    
    // Add personal information if available
    if (!empty($formData['full_name'])) {
        $message .= "<p><strong>Naam:</strong> " . htmlspecialchars($formData['full_name']) . "</p>";
    } else {
        error_log("Missing full_name in form data");
    }
    
    if (!empty($formData['email'])) {
        $message .= "<p><strong>Email:</strong> " . htmlspecialchars($formData['email']) . "</p>";
    } else {
        error_log("Missing email in form data");
    }
    
    if (!empty($formData['phone'])) {
        $message .= "<p><strong>Telefoon:</strong> " . htmlspecialchars($formData['phone']) . "</p>";
    } else {
        error_log("Missing phone in form data");
    }
    
    if (!empty($formData['address'])) {
        $message .= "<p><strong>Adres:</strong> " . htmlspecialchars($formData['address']) . "</p>";
    } else {
        error_log("Missing address in form data");
    }
    
    $message .= "<h3>Antwoorden</h3>";
    
    // Count total answers found
    $totalAnswers = 0;
    
    // Group questions by centrum
    for ($centrum = 1; $centrum <= 7; $centrum++) {
        $message .= "<h4>Centrum {$centrum}</h4><ul>";
        
        $hasAnswers = false;
        $centrumAnswers = 0;
        
        // Debug: Log all keys for this centrum
        $centrumKeys = array_filter(array_keys($formData), function($key) use ($centrum) {
            return strpos($key, "q{$centrum}_") === 0;
        });
        error_log("Centrum {$centrum} keys: " . implode(", ", $centrumKeys));
        
        foreach ($formData as $key => $value) {
            // Match keys like q1_0, q2_3, etc.
            if (preg_match("/^q{$centrum}_(\d+)$/", $key, $matches)) {
                $questionIndex = $matches[1];
                $question = getQuestionText($centrum, $questionIndex);
                $message .= "<li><strong>{$question}</strong>: {$value}</li>";
                $hasAnswers = true;
                $centrumAnswers++;
                $totalAnswers++;
                
                // Debug: Log each answer found
                error_log("Found answer for q{$centrum}_{$questionIndex}: {$value}");
            }
        }
        
        if (!$hasAnswers) {
            $message .= "<li>Geen antwoorden voor dit centrum</li>";
            error_log("No answers found for centrum {$centrum}");
        } else {
            error_log("Found {$centrumAnswers} answers for centrum {$centrum}");
        }
        
        $message .= "</ul>";
    }
    
    // Debug: Log total answers found
    error_log("Total answers found: {$totalAnswers}");
    
    // Debug: Log the constructed message
    error_log("Questionnaire email content: " . $message);

    // Get recipient email from form or use default
    $recipientEmail = !empty($formData['email']) ? $formData['email'] : null;
    $recipientName = !empty($formData['full_name']) ? $formData['full_name'] : null;

    if (sendEmail(
        "Nieuwe Vragenlijst Inzending",
        $message,
        "Nieuwe vragenlijst inzending ontvangen",
        '<EMAIL>',
        'Jacqueline Tjassens'
    )) {
        // Also send a confirmation to the user if we have their email
        if ($recipientEmail) {
            sendEmail(
                "Bevestiging van uw vragenlijst",
                "<h2>Bedankt voor het invullen van de vragenlijst</h2>
                <p>Beste " . htmlspecialchars($recipientName ?? 'klant') . ",</p>
                <p>Wij hebben uw vragenlijst ontvangen en zullen deze zo spoedig mogelijk bekijken.</p>
                <p>Met vriendelijke groet,<br>Jacqueline Tjassens</p>",
                "Bedankt voor het invullen van de vragenlijst",
                $recipientEmail,
                $recipientName
            );
        }
        
        echo json_encode(['success' => true, 'message' => 'Uw vragenlijst is succesvol verzonden. Bedankt voor het invullen!']);
    } else {
        throw new Exception('Failed to send email');
    }
} catch (Exception $e) {
    error_log("Questionnaire submission error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Er is een fout opgetreden bij het verzenden van de vragenlijst. Probeer het later opnieuw.']);
}

function getQuestionText($centrum, $index) {
    $questions = [
        1 => [
            "Heeft u problemen met artrose of osteoporose?",
            "Heeft u last van rugklachten, zoals een hernia, discopathie-aandoeningen, scoliose?",
            "Heeft u regelmatig spasmen, spierkrampen of chronische spierpijn in het algemeen?",
            "Heeft u bloedarmoede, bloedstoornissen of een sterke neiging tot het ontwikkelen van virussen en chronische vermoeidheid",
            "Heeft u huidproblemen, zoals eczeem, psoriasis, acne of iets dergelijks?",
            "Heeft u de neiging om meer te geven dan u ontvangt",
            "Vindt u het moeilijk om van andere mensen te houden of ze te waarderen?",
            "Als u iemand ziet die pijn heeft, probeert u hem dan te helpen?",
            "Bent u onhandig in u sociale relaties, in die zin dat u moeite heeft tactvol te zijn?",
            "Was u een verlegen persoon of bent u dat nog steeds?",
            "Heeft uw gezondheidstoestand de neiging om te verzwakken met de wisseling van seizoenen?",
            "Maken de veranderingen en het onverwachte in uw leven u nerveus?",
            "Heeft u de neiging om anderen niet te laten zien hoe u zich voelt?",
            "Voelt u zich het zwarte schaap van de familie?",
            "Bent u de persoon met wie mensen automatisch komen praten als ze een probleem hebben?",
            "Heeft u de neiging om snel de banden te verbreken in een relatie als er een probleem is met een andere persoon?"
        ],
        2 => [
            "Heeft u gynaecologische problemen? ( voor mannen prostaat, teelbalproblemen).",
            "Vaginale problemen zoals vaginitis, droogheid of een ander probleem op dit gebied?",
            "Voelt u een afwezigheid van seksueel verlangen?",
            "Als u geld leent aan iemand van wie u houdt, heeft u dan de neiging rente in rekening te brengen?",
            "Komt u vaak in een moeilijke financiële situatie of in de schulden, bv voor een vakantie?",
            "Bent u iemand die competitie hoog in het vaandel heeft staan?",
            "Hebben de mensen om u heen de neiging om u de schuld te geven van dit aspect?",
            "Heeft u ooit een relatie verbroken voor uw professionele werk?",
            "Heeft u een lange levensfase meegemaakt waarin u in een buitensporige beperkende relatie zat, waarin u zich ondergewaardeerd voelde?"
        ],
        3 => [
            "Heeft u spijsverteringsproblemen?",
            "Heeft u verslavingsproblemen zoals alcohol, tabak, drugs?",
            // Note: height/weight question is handled separately in the form
            "Heeft u last van boulimie of anorexia?",
            "Vindt u het moeilijk om uzelf in de spiegel te zien?",
            "Heeft u de neiging om mensen met een verslavingsstoornis in uw leven aan te trekken?",
            "Geeft u om gewicht?",
            "Heeft u dwangmatig gedrag om uzelf te kalmeren, zoals een stuk chocola of winkelen?",
            "Vindt u het belangrijk om altijd in de mode te zijn, of het om u kleding, uitdrukkingen , uw persoonlijke stijl of kennissen gaat?"
        ],
        4 => [
            "Heeft u arteriële, veneuze problemen: doorbloedingsstoornissen, oedeem, spataderen?",
            "Heeft u atherosclerose?",
            "Heeft u een hoge bloeddruk?",
            "Heeft u een te hoog cholesterolgehalte?",
            "Heeft u ooit een hartaanval gehad of heeft u ooit een waarschuwingssignaal gekregen?",
            "Heeft u astma of andere luchtwegallergieën of heeft u dat gehad?",
            "Probeert u regelmatig te weten te komen hoe de mensen van wie u houdt zich voelen?",
            "Is u gemoedstoestand gevoelig voor het weer, voor de wisseling van de seizoenen?",
            "Heeft u ooit gehuild vanwege uw werk?",
            "Huilt u vaak en gemakkelijk?",
            "Is het moeilijk voor u om boos te worden op een dierbare, ook al is dat terecht?",
            "Bent u geneigd om snel geïrriteerd en boos te worden?",
            "Als er iets mis is en u voelt zich slecht, heeft u dan de neiging om uzelf te isoleren en afstand te nemen van anderen?"
        ],
        5 => [
            "Heeft u gebitsproblemen of heeft u deze in het verleden gehad?",
            "Heeft u last van schildklierproblemen?",
            "Heeft u last van de cervicale regio?",
            "Heeft u vaak keelpijn?",
            "Heeft u andere keelproblemen gehad?",
            "Had u moeite om instructies op te volgen toen u jong was?",
            "Heeft u in uw huidige leven moeite om de regels, de richtlijnen te volgen?",
            "Vindt u het moeilijk om u concentratie vast te houden als u aan het telefoneren bent?",
            "Heeft u moeite om uzelf verstaanbaar te maken als u met mensen om u heen praat?",
            "Heeft u moeite om uzelf in het openbaar uit te drukken?",
            "Heeft u de neiging om ja te zeggen, zelfs als u andersom denkt, zodat u uzelf niet hoeft te verantwoorden?",
            "Heeft u last van dyslexie, spraak of uitdrukkingsproblemen wanneer u zich ongemakkelijk, gestrest voelt?"
        ],
        6 => [
            "Heeft u last van slapeloosheid?",
            "Heeft u last van hoofdpijn en migraine?",
            "Bent u gestrest over het ouder worden?",
            "Heeft u last van ernstig geheugenverlies?",
            "Heeft u last van staar gehad of heeft u er momenteel last van?",
            "Heeft u last van duizeligheid?",
            "Als u een beperkte spreektijd krijgt, heeft u er dan moeite mee om deze te respecteren?",
            "Heeft u moeite met het beantwoorden van vragenlijsten met meerkeuzevragen?",
            "Heeft u het gevoel dat u geest vaak afdwaalt?",
            "Bent u terughoudend om nieuwe technologie of nieuwe manieren van zakendoen te leren?",
            "Heeft u ooit een significant trauma of misbruik meegemaakt?",
            "Voelt u zich fijn in de natuur, heeft u soms de indruk er één mee te zijn?"
        ],
        7 => [
            "Heeft u een chronische ziekte?",
            "Heeft u een ernstige of ongeneeslijke ziekte waarvan de diagnose is gesteld?",
            "Lijdt u aan kanker?",
            "Bent u in een zeer kritieke gezondheidstoestand met een nogal pessimistische mening van de artsen?",
            "Bent u iemand die gesloten is voor spiritualiteit of religies?",
            "Bent u het type persoon dat altijd naar zijn werk gaat zonder een dag vrij te nemen, zelfs als u zich niet lekker voelt?",
            "Raakt u in paniek als u nadenkt over de zin en het doel van uw leven?",
            "Heeft u de neiging om gezondheidsproblemen en morele problemen aan elkaar te koppelen?",
            "Heeft u het gevoel dat uw dierbaren de neiging hebben om van u weg te lopen en u in de steek laten als u met een probleem wordt geconfronteerd?"
        ],
    ];
    
    if (isset($questions[$centrum][$index])) {
        return $questions[$centrum][$index];
    } else {
        error_log("Question not found for centrum {$centrum}, index {$index}");
        return "Vraag {$centrum}.{$index}";
    }
}
