<?php
/**
 * reCAPTCHA Configuration
 * 
 * Centralized configuration for Google reCAPTCHA v3 keys and settings.
 * Update the keys here and they will be used throughout the application.
 */

// Prevent direct access
if (!defined('RECAPTCHA_CONFIG_LOADED')) {
    define('RECAPTCHA_CONFIG_LOADED', true);
}

/**
 * reCAPTCHA v3 Configuration
 */
class RecaptchaConfig {
    
    /**
     * Site Key (Public Key)
     * Used in frontend JavaScript and HTML
     * This key is visible to users and can be seen in page source
     */
    const SITE_KEY = '6Lf7Te0oAAAAAFSV-wPTrZvERy7_I3lCtnQsin7F';
    
    /**
     * Secret Key (Private Key)
     * Used for server-side verification
     * Keep this key secure and never expose it to frontend
     */
    const SECRET_KEY = '6Lf7Te0oAAAAAMelSFNpj38tZIjVXwZALjg8g2-v';
    
    /**
     * Default minimum score for reCAPTCHA v3
     * Range: 0.0 (likely bot) to 1.0 (likely human)
     * Recommended: 0.5 for most applications
     */
    const DEFAULT_MIN_SCORE = 0.5;
    
    /**
     * reCAPTCHA API URL for verification
     */
    const VERIFY_URL = 'https://www.google.com/recaptcha/api/siteverify';
    
    /**
     * Timeout for reCAPTCHA verification requests (seconds)
     */
    const VERIFY_TIMEOUT = 10;
    
    /**
     * Get the site key for frontend use
     * 
     * @return string Site key
     */
    public static function getSiteKey() {
        return self::SITE_KEY;
    }
    
    /**
     * Get the secret key for backend verification
     * 
     * @return string Secret key
     */
    public static function getSecretKey() {
        return self::SECRET_KEY;
    }
    
    /**
     * Get the default minimum score
     * 
     * @return float Default minimum score
     */
    public static function getDefaultMinScore() {
        return self::DEFAULT_MIN_SCORE;
    }
    
    /**
     * Get the verification URL
     * 
     * @return string Verification URL
     */
    public static function getVerifyUrl() {
        return self::VERIFY_URL;
    }
    
    /**
     * Get the verification timeout
     * 
     * @return int Timeout in seconds
     */
    public static function getVerifyTimeout() {
        return self::VERIFY_TIMEOUT;
    }
    
    /**
     * Generate the reCAPTCHA script tag for HTML
     * 
     * @return string HTML script tag
     */
    public static function getScriptTag() {
        $siteKey = self::getSiteKey();
        return "<script src=\"https://www.google.com/recaptcha/api.js?render={$siteKey}\"></script>";
    }
    
    /**
     * Generate JavaScript code to execute reCAPTCHA
     * 
     * @param string $action Action name (e.g., 'contact', 'newsletter')
     * @param string $callback JavaScript callback function name
     * @return string JavaScript code
     */
    public static function getExecuteScript($action, $callback = null) {
        $siteKey = self::getSiteKey();
        $callbackCode = $callback ? ".then({$callback})" : '';
        
        return "
        grecaptcha.ready(function() {
            grecaptcha.execute('{$siteKey}', {action: '{$action}'}){$callbackCode};
        });";
    }
    
    /**
     * Validate configuration
     * 
     * @return array Array with 'valid' boolean and 'errors' array
     */
    public static function validateConfig() {
        $errors = [];
        
        // Check if keys are set and not empty
        if (empty(self::SITE_KEY)) {
            $errors[] = 'Site key is not configured';
        }
        
        if (empty(self::SECRET_KEY)) {
            $errors[] = 'Secret key is not configured';
        }
        
        // Check if keys look valid (basic format check)
        if (!empty(self::SITE_KEY) && !preg_match('/^6L[a-zA-Z0-9_-]{38}$/', self::SITE_KEY)) {
            $errors[] = 'Site key format appears invalid';
        }
        
        if (!empty(self::SECRET_KEY) && !preg_match('/^6L[a-zA-Z0-9_-]{38}$/', self::SECRET_KEY)) {
            $errors[] = 'Secret key format appears invalid';
        }
        
        // Check score range
        if (self::DEFAULT_MIN_SCORE < 0.0 || self::DEFAULT_MIN_SCORE > 1.0) {
            $errors[] = 'Default minimum score must be between 0.0 and 1.0';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}

/**
 * Helper functions for backward compatibility and ease of use
 */

/**
 * Get reCAPTCHA site key
 * 
 * @return string Site key
 */
function getRecaptchaSiteKey() {
    return RecaptchaConfig::getSiteKey();
}

/**
 * Get reCAPTCHA secret key
 * 
 * @return string Secret key
 */
function getRecaptchaSecretKey() {
    return RecaptchaConfig::getSecretKey();
}

/**
 * Get reCAPTCHA script tag
 * 
 * @return string HTML script tag
 */
function getRecaptchaScriptTag() {
    return RecaptchaConfig::getScriptTag();
}

?>
