<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Add CORS headers if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';
require_once 'db.php';

// Get and validate input
$input = file_get_contents('php://input');
error_log("Received input: " . $input);

$data = json_decode($input, true);
$date = $data['date'] ?? null;
$duration = (int)($data['duration'] ?? 0);

if (!$date || !$duration) {
    error_log("Invalid input - Date: $date, Duration: $duration");
    http_response_code(400);
    echo json_encode([
        'error' => 'Invalid input parameters',
        'date' => $date,
        'duration' => $duration
    ]);
    exit;
}

try {
    // Verify database connection with detailed error logging
    $env = $GLOBALS['_environment'];
    error_log("Current environment: " . $env);
    
    // Get database config for debugging
    $config = $GLOBALS['_pdo_config'][$env];
    error_log("DB Config: host=" . $config['host'] . ", user=" . $config['username'] . ", dbname=" . $config['dbname']);
    
    $pdo = getPDO();
    if (!isset($pdo)) {
        throw new Exception("Database connection not established");
    }

    // Get day of week (0 = Sunday, 6 = Saturday)
    $dayOfWeek = date('w', strtotime($date));
    error_log("Processing request for date: $date (day of week: $dayOfWeek)");
    
    // Check for special date
    $stmt = $pdo->prepare("SELECT * FROM special_dates WHERE date = ?");
    $stmt->execute([$date]);
    $specialDate = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($specialDate) {
        if ($specialDate['type'] === 'blocked') {
            echo json_encode([
                'slots' => [],
                'message' => 'Deze datum is niet beschikbaar'
            ]);
            exit;
        } elseif ($specialDate['type'] === 'custom_hours') {
            // Use special hours instead of regular business hours
            $hours = [
                'start_time' => $specialDate['start_time'],
                'end_time' => $specialDate['end_time']
            ];
        }
    } else {
        // Use regular business hours
        $stmt = $pdo->prepare("SELECT start_time, end_time FROM business_hours WHERE day_of_week = ?");
        $stmt->execute([$dayOfWeek]);
        $hours = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    error_log("Business hours found: " . json_encode($hours));
    
    if (!$hours) {
        error_log("No business hours found for day $dayOfWeek");
        echo json_encode([
            'slots' => [],
            'message' => 'No business hours for selected day'
        ]);
        exit;
    }
    
    // Get existing appointments
    $stmt = $pdo->prepare("
        SELECT appointment_time, 60 as duration  /* Assuming default duration of 60 minutes */
        FROM appointments 
        WHERE appointment_date = ?
        AND status != 'cancelled'
        ORDER BY appointment_time
    ");
    $stmt->execute([$date]);
    $bookedSlots = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Transform booked slots
    $bookedSlots = array_map(function($slot) {
        return [
            'appointment_time' => $slot['appointment_time'],
            'end_time' => date('H:i', strtotime($slot['appointment_time']) + ($slot['duration'] * 60))
        ];
    }, $bookedSlots);
    
    error_log("Booked slots: " . json_encode($bookedSlots));
    
    // Generate available time slots
    $availableSlots = [];
    $startTime = strtotime($hours['start_time']);
    $endTime = strtotime($hours['end_time']);
    $interval = 30 * 60; // 30 minutes in seconds
    
    for ($time = $startTime; $time <= $endTime - ($duration * 60); $time += $interval) {
        $slotStart = date('H:i', $time);
        $slotEnd = date('H:i', $time + ($duration * 60));
        
        $isAvailable = true;
        foreach ($bookedSlots as $booked) {
            if (
                (strtotime($slotStart) >= strtotime($booked['appointment_time']) && 
                 strtotime($slotStart) < strtotime($booked['end_time'])) ||
                (strtotime($slotEnd) > strtotime($booked['appointment_time']) && 
                 strtotime($slotEnd) <= strtotime($booked['end_time'])) ||
                (strtotime($slotStart) <= strtotime($booked['appointment_time']) && 
                 strtotime($slotEnd) >= strtotime($booked['end_time']))
            ) {
                $isAvailable = false;
                break;
            }
        }
        
        if ($isAvailable) {
            $availableSlots[] = $slotStart;
        }
    }
    
    error_log("Available slots: " . json_encode($availableSlots));
    
    echo json_encode([
        'slots' => $availableSlots,
        'debug' => [
            'date' => $date,
            'dayOfWeek' => $dayOfWeek,
            'duration' => $duration,
            'businessHours' => $hours,
            'bookedSlots' => $bookedSlots
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Error in get-available-slots.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error occurred',
        'message' => $e->getMessage()
    ]);
}
