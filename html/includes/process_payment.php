<?php
// Direct error logging
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../payment_processor_errors.log');
error_log('Payment processor started - ' . date('Y-m-d H:i:s'));

// Set error handling
ini_set('display_errors', 0);
error_reporting(E_ALL);
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error: [$errno] $errstr in $errfile on line $errline");
    global $log;
    if (isset($log)) {
        $log->error('PHP Error', [
            'errno' => $errno,
            'errstr' => $errstr,
            'errfile' => $errfile,
            'errline' => $errline
        ]);
    }
    return true;
});

// Register shutdown function to catch fatal errors
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("FATAL ERROR: " . print_r($error, true));
        
        // Send JSON error response if possible
        if (!headers_sent()) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'A fatal error occurred: ' . $error['message']
            ]);
        }
    }
});

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'Logger.php';
$log = Logger::get('payment_processor');
$log->info('Starting payment processor');

try {
    $log->info('Loading includes');
    require_once 'config.php';
    require_once 'db.php';
    
    // Explicitly initialize the database connection
    $pdo = getPDO();
    
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
    
    $log->info('Database connection established');
    
    require_once 'cart_handler.php';
    require_once 'mollie_config.php';
    $log->info('All includes loaded successfully');
} catch (Exception $e) {
    $log->error('Failed loading includes', [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Error loading required files'
    ]);
    exit;
}

// Log request details
$log->info('Request received', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'content_type' => isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '',
    'post_data' => $_POST
]);

// Process payment
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'process_payment') {
    $log->info('Processing payment action detected');
    
    try {
        // Get session ID
        $session_id = session_id();
        $log->info('Fetching cart items for session', ['session_id' => $session_id]);
        
        // Get cart items
        $cart_items = getCartItems();
        $log->info('Cart items fetched successfully', ['count' => count($cart_items)]);
        
        if (empty($cart_items)) {
            $log->error('Empty cart during payment process');
            throw new Exception('Shopping cart is empty');
        }
        
        $log->info('Cart items retrieved', ['count' => count($cart_items)]);
        
        // Get delivery method
        $delivery_method = isset($_POST['delivery_method']) ? trim($_POST['delivery_method']) : 'shipping';
        $log->info('Delivery method detected', ['method' => $delivery_method]);
        
        // Calculate totals
        $subtotal = 0;
        foreach ($cart_items as $item) {
            $subtotal += $item['price'] * $item['quantity'];
        }
        
        // Apply shipping cost only if shipping is selected
        $shipping = ($delivery_method === 'shipping') ? SHIPPING_COST : 0;
        $total = $subtotal + $shipping;
        
        $log->info('Totals calculated', [
            'subtotal' => $subtotal,
            'shipping' => $shipping,
            'total' => $total,
            'delivery_method' => $delivery_method
        ]);
        
        // Generate unique order ID
        $order_id = uniqid('ORDER_');
        $log->info('Order created', [
            'order_id' => $order_id,
            'total_amount' => $total,
            'items_count' => count($cart_items),
            'delivery_method' => $delivery_method
        ]);
        
        // Get customer details
        $customer_name = isset($_POST['name']) ? trim($_POST['name']) : '';
        $customer_email = isset($_POST['email']) ? trim($_POST['email']) : '';
        $customer_address = isset($_POST['address']) ? trim($_POST['address']) : '';
        $customer_postal_code = isset($_POST['postal_code']) ? trim($_POST['postal_code']) : '';
        $customer_city = isset($_POST['city']) ? trim($_POST['city']) : '';
        
        // Validate customer details
        if (empty($customer_name) || empty($customer_email) || empty($customer_address) || 
            empty($customer_postal_code) || empty($customer_city)) {
            $log->error('Missing customer details', [
                'name' => $customer_name,
                'email' => $customer_email,
                'address' => $customer_address,
                'postal_code' => $customer_postal_code,
                'city' => $customer_city
            ]);
            throw new Exception('Please fill in all required fields');
        }
        
        // Save order to database
        $stmt = $pdo->prepare("INSERT INTO orders (
                                user_id, 
                                status, 
                                payment_id, 
                                shipping_address, 
                                total_amount,
                                delivery_method
                              ) VALUES (
                                NULL, 
                                'pending', 
                                ?, 
                                ?, 
                                ?,
                                ?
                              )");

        // Create address JSON
        $address = json_encode([
            'name' => $customer_name,
            'email' => $customer_email,
            'address' => $customer_address,
            'postal_code' => $customer_postal_code,
            'city' => $customer_city
        ]);

        $stmt->execute([
            $order_id, // This will be the payment_id
            $address,
            $total,
            $delivery_method
        ]);

        $order_db_id = $pdo->lastInsertId();
        $log->info('Order saved to database', [
            'order_db_id' => $order_db_id,
            'delivery_method' => $delivery_method
        ]);
        
        // Save order items
        foreach ($cart_items as $item) {
            $stmt = $pdo->prepare("INSERT INTO order_items (
                                    order_id, 
                                    product_id, 
                                    quantity, 
                                    price
                                  ) VALUES (?, ?, ?, ?)");
            $stmt->execute([
                $order_db_id,
                $item['product_id'],
                $item['quantity'],
                $item['price']
            ]);
        }
        $log->info('Order items saved to database');
        
        // Create payment link
        $payment_description = "Bestelling #" . $order_db_id;

        // Encrypt the order ID for the redirect URL
        $encryption_key = ENCRYPTION_KEY;
        $encrypted_id = base64_encode(openssl_encrypt(
            $order_db_id,
            'AES-256-CBC',
            $encryption_key,
            0,
            substr(hash('sha256', $encryption_key), 0, 16)
        ));

        $redirect_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . 
                        "://" . $_SERVER['HTTP_HOST'] . "/payment_return.php?order=" . urlencode($encrypted_id);

        $log->info('Creating payment link', [
            'order_id' => $order_db_id,
            'total' => $total,
            'description' => $payment_description,
            'redirect_url' => $redirect_url
        ]);

        // Create payment with Mollie
        $payment_url = createPaymentLink($order_id, $total, $payment_description, $redirect_url, [
            'order_db_id' => $order_db_id
        ]);

        if (empty($payment_url)) {
            $log->error('Empty payment URL received', [
                'order_id' => $order_db_id
            ]);
            throw new Exception('Failed to create payment URL');
        }

        // Clear the cart after successful order creation
        $log->info('Clearing cart');
        clearCart();
        $log->info('Cart cleared after order creation');

        $log->info('Payment link created successfully', [
            'order_id' => $order_db_id,
            'payment_url' => $payment_url
        ]);

        $log->info('Preparing JSON response');
        $response = [
            'success' => true,
            'redirectUrl' => $payment_url,
            'message' => 'Payment processing initiated'
        ];
        $log->info('Sending JSON response', $response);

        header('Content-Type: application/json');
        echo json_encode($response);
        $log->info('Response sent');
        exit;

    } catch (Exception $e) {
        $log->error('Payment processing error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
        exit;
    }
} else {
    $log->error('Invalid request method or action', [
        'method' => $_SERVER['REQUEST_METHOD'],
        'action' => $_POST['action'] ?? 'not set'
    ]);

    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request'
    ]);
    exit;
}
