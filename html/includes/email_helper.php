<?php
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/mail_config.php';
require_once __DIR__ . '/../vendor/autoload.php';

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

function sendEmail($subject, $body, $altBody = '', $recipientEmail = null, $recipientName = null) {
    // Set default recipient if not provided
    $recipientEmail = $recipientEmail ?: SMTP_USER;
    $recipientName = $recipientName ?: '<PERSON>sen<PERSON>';
    
    // Log the email request with more details
    error_log("Email request initiated: To: {$recipientEmail}, Subject: {$subject}");
    error_log("Email configuration: Host: " . SMTP_HOST . ", Port: " . SMTP_PORT . ", Secure: " . SMTP_SECURE);
    
    try {
        require_once __DIR__ . '/../vendor/autoload.php';
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USER;
        $mail->Password = SMTP_PASS;
        $mail->SMTPSecure = SMTP_SECURE;
        $mail->Port = SMTP_PORT;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = SMTP_TIMEOUT;
        
        // Set debug level to maximum for troubleshooting
        $mail->SMTPDebug = 4; // Temporarily set to maximum debug level
        $mail->Debugoutput = 'error_log';
        
        error_log("PHPMailer initialized with settings: Host={$mail->Host}, Port={$mail->Port}, SMTPSecure={$mail->SMTPSecure}");
        
        $mail->setFrom(SMTP_USER, 'Jacqueline Tjassens');
        $mail->addAddress($recipientEmail, $recipientName);
        $mail->addReplyTo(SMTP_USER, 'Jacqueline Tjassens');
        
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        $mail->AltBody = $altBody ?: strip_tags(str_replace(['<br>', '</p>'], "\n", $body));
        
        error_log("Attempting to send email (first try)...");
        
        // Try with primary settings
        $success = $mail->send();
        if ($success) {
            error_log("Email sent successfully to {$recipientEmail}");
            return true;
        }
        
        error_log("First attempt failed: " . $mail->ErrorInfo);
        
        // Try alternative settings (port 465 with SSL)
        error_log("Trying alternative settings (SSL/465)...");
        $mail = new PHPMailer(true);
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USER;
        $mail->Password = SMTP_PASS;
        $mail->SMTPSecure = 'ssl';
        $mail->Port = 465;
        $mail->CharSet = 'UTF-8';
        $mail->Timeout = SMTP_TIMEOUT;
        $mail->SMTPDebug = 4; // Temporarily set to maximum debug level
        $mail->Debugoutput = 'error_log';
        
        $mail->setFrom(SMTP_USER, 'Jacqueline Tjassens');
        $mail->addAddress($recipientEmail, $recipientName);
        $mail->addReplyTo(SMTP_USER, 'Jacqueline Tjassens');
        
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        $mail->AltBody = $altBody ?: strip_tags(str_replace(['<br>', '</p>'], "\n", $body));
        
        $success = $mail->send();
        error_log("Second attempt (SSL/465): " . ($success ? "success" : "failed - " . $mail->ErrorInfo));
        
        // If still failing, try without encryption on port 25
        if (!$success) {
            error_log("Trying third option (port 25 without encryption)...");
            $mail = new PHPMailer(true);
            $mail->isSMTP();
            $mail->Host = SMTP_HOST;
            $mail->SMTPAuth = true;
            $mail->Username = SMTP_USER;
            $mail->Password = SMTP_PASS;
            $mail->SMTPSecure = '';
            $mail->Port = 25;
            $mail->CharSet = 'UTF-8';
            $mail->Timeout = SMTP_TIMEOUT;
            $mail->SMTPDebug = 4; // Temporarily set to maximum debug level
            $mail->Debugoutput = 'error_log';
            
            $mail->setFrom(SMTP_USER, 'Jacqueline Tjassens');
            $mail->addAddress($recipientEmail, $recipientName);
            $mail->addReplyTo(SMTP_USER, 'Jacqueline Tjassens');
            
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $body;
            $mail->AltBody = $altBody ?: strip_tags(str_replace(['<br>', '</p>'], "\n", $body));
            
            $success = $mail->send();
            error_log("Third attempt (port 25): " . ($success ? "success" : "failed - " . $mail->ErrorInfo));
        }
        
        return $success;
    } catch (Exception $e) {
        error_log("Email error (exception): " . $e->getMessage());
        error_log("Exception trace: " . $e->getTraceAsString());
        return false;
    }
}
