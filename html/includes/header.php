<?php 
require_once 'config.php'; 
require_once 'seo.php';

// Get meta data for current page
$meta_data = $seo_data ?? [];

// Determine the base path dynamically
$base_path = (isset($_SERVER['SCRIPT_NAME']) && (
    strpos($_SERVER['SCRIPT_NAME'], '/behandelingen/') !== false || 
    strpos($_SERVER['SCRIPT_NAME'], '/workshops/') !== false || 
    strpos($_SERVER['SCRIPT_NAME'], '/blog/') !== false ||
    strpos($_SERVER['SCRIPT_NAME'], '/producten/') !== false ||
    strpos($_SERVER['SCRIPT_NAME'], '/admin/') !== false
)) ? '../' : '';
?>
<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <?php echo generateMetaTags($meta_data); ?>
    
    <?php 
    // Add structured data if specified
    if (isset($structured_data_type)) {
        echo generateStructuredData($structured_data_type, $structured_data ?? []);
    }
    ?>

    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo $base_path; ?>assets/css/bootstrap.min.css" as="style">
    <link rel="preload" href="<?php echo $base_path; ?>assets/css/style.css" as="style">
    <link rel="preload" href="<?php echo $base_path; ?>assets/images/home.jpg" as="image" fetchpriority="high">

    <!-- Load Bootstrap CSS first (framework) -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/bootstrap.min.css">
    
    <!-- Load your custom CSS after Bootstrap so it can override Bootstrap styles -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/style.css?v=1.0">

    <!-- Defer non-critical CSS -->
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/all.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/swiper.min.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/font.css" media="print" onload="this.media='all'">
    <link rel="stylesheet" href="<?php echo $base_path; ?>assets/css/cookie-consent.css" media="print" onload="this.media='all'">

    <!-- Optimized Google Fonts loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&display=swap" media="print" onload="this.media='all'">
    <noscript><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&display=swap"></noscript>

    <!-- Favicons -->
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo $base_path; ?>assets/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo $base_path; ?>assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo $base_path; ?>assets/images/favicon-16x16.png">
    <link rel="shortcut icon" href="<?php echo $base_path; ?>assets/images/favicon.ico">
</head>
<body>
    <!-- pre loader start -->
    <div class="pa-preloader">
        <div class="pa-ellipsis">
            <img src="<?php echo $base_path; ?>assets/images/loader.svg" alt="">
        </div>
    </div>
    <!-- pre loader end -->
    <!-- main wrapper start -->
    <div class="pa-main-wrapper">
    <!-- Google Analytics with GDPR consent -->
    <script>
        // Set default consent to denied
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        
        // Initialize with denied consent by default
        gtag('consent', 'default', {
            'analytics_storage': 'denied'
        });
        
        // Load GA script but don't activate until consent
        var gaScript = document.createElement('script');
        gaScript.async = true;
        gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-DWZ71DG9MG';
        document.head.appendChild(gaScript);
        
        // Initialize GA
        gtag('js', new Date());
        gtag('config', 'G-DWZ71DG9MG');
        
        // Check for existing consent
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('analytics_consent') === 'granted') {
                grantAnalyticsConsent();
            }
        });
        
        // Function to grant consent
        function grantAnalyticsConsent() {
            gtag('consent', 'update', {
                'analytics_storage': 'granted'
            });
            localStorage.setItem('analytics_consent', 'granted');
        }
        
        // Function to revoke consent
        function revokeAnalyticsConsent() {
            gtag('consent', 'update', {
                'analytics_storage': 'denied'
            });
            localStorage.setItem('analytics_consent', 'denied');
        }
    </script>
    <!-- End Google Analytics -->
</body>
</html>
