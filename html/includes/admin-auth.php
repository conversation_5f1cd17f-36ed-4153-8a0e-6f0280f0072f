<?php
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db.php';

/**
 * Check if email domain is allowed for admin access
 * 
 * @param string $email Email address to check
 * @return bool True if domain is allowed
 */
function isAllowedAdminDomain($email) {
    $allowedDomains = [
        'tjassens.com',
        'jacquelinetjassens.com'
    ];
    
    $domain = substr(strrchr($email, "@"), 1);
    return in_array(strtolower($domain), $allowedDomains);
}

/**
 * Verify if user has admin access
 * 
 * @return bool True if user has admin access
 */
function hasAdminAccess() {
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    try {
        $pdo = getPDO();
        
        // Get user details
        $stmt = $pdo->prepare("SELECT email FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            return false;
        }
        
        // Check if user has admin role and allowed email domain
        return isAllowedAdminDomain($user['email']);
        
    } catch (PDOException $e) {
        error_log("Admin auth error: " . $e->getMessage());
        return false;
    }
}

// If not authenticated, redirect to login
if (!hasAdminAccess()) {
    // Store the requested URL for redirect after login
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    
    // Clear any existing admin sessions
    unset($_SESSION['is_admin']);
    
    // Redirect to login page
    header('Location: /login.php?error=unauthorized');
    exit;
}

// Set admin session flag
$_SESSION['is_admin'] = true;
