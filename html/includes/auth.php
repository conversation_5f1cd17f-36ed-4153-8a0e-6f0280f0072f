<?php
/**
 * User Authentication System
 * 
 * This file provides functions for user login, logout, registration,
 * and password reset functionality.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'db.php';

/**
 * Authenticates a user with username and password
 *
 * @param string $username The username
 * @param string $password The password (plaintext)
 * @return bool True if authentication successful, false otherwise
 */
function loginUser($username, $password) {
    try {
        error_log("=== Login Attempt ===");
        error_log("Username: " . $username);
        
        $pdo = getPDO();
        
        if (!$pdo) {
            error_log("Database connection failed in loginUser");
            return false;
        }
        
        $stmt = $pdo->prepare("SELECT id, username, password FROM users WHERE username = :username");
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['username'];
            $_SESSION['last_activity'] = time();
            
            // Regenerate session ID to prevent session fixation
            session_regenerate_id(true);
            
            return true;
        }
        
        return false;
        
    } catch (PDOException $e) {
        error_log("Login error: " . $e->getMessage());
        return false;
    }
}

/**
 * Logs out the current user and destroys the session
 *
 * @return void
 */
function logoutUser() {
    // Unset all session variables
    $_SESSION = array();
    
    // Destroy the session cookie
    if (isset($_COOKIE[session_name()])) {
        setcookie(session_name(), '', time() - 3600, '/', '', true, true);
    }
    
    // Destroy the session
    session_destroy();
}

/**
 * Check if email domain is restricted (admin domains)
 * 
 * @param string $email Email address to check
 * @return bool True if domain is restricted
 */
function isRestrictedDomain($email) {
    $restrictedDomains = [
        'tjassens.com',
        'jacquelinetjassens.com'
    ];
    
    $domain = substr(strrchr($email, "@"), 1);
    return in_array(strtolower($domain), $restrictedDomains);
}

/**
 * Registers a new user
 *
 * @param string $username The desired username
 * @param string $email The user's email address
 * @param string $password The desired password (plaintext)
 * @return array Associative array with 'success' boolean and 'message' string
 */
function registerUser($username, $email, $password) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            return ['success' => false, 'message' => 'Database connection failed'];
        }
        
        // Validate input
        if (empty($username) || empty($email) || empty($password)) {
            return ['success' => false, 'message' => 'All fields are required'];
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['success' => false, 'message' => 'Invalid email format'];
        }
        
        // Block registration with restricted domains
        if (isRestrictedDomain($email)) {
            return [
                'success' => false, 
                'message' => 'Registratie met dit e-maildomein is niet toegestaan'
            ];
        }
        
        if (strlen($password) < 8) {
            return ['success' => false, 'message' => 'Password must be at least 8 characters'];
        }
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Check if username or email already exists
        $checkStmt = $pdo->prepare("SELECT id FROM users WHERE username = :username OR email = :email");
        $checkStmt->bindParam(':username', $username, PDO::PARAM_STR);
        $checkStmt->bindParam(':email', $email, PDO::PARAM_STR);
        $checkStmt->execute();
        
        if ($checkStmt->rowCount() > 0) {
            $pdo->rollBack();
            return ['success' => false, 'message' => 'Username or email already exists'];
        }
        
        // Insert new user
        $insertStmt = $pdo->prepare("
            INSERT INTO users (username, email, password, created_at) 
            VALUES (:username, :email, :password, NOW())
        ");
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Bind parameters
        $insertStmt->bindParam(':username', $username, PDO::PARAM_STR);
        $insertStmt->bindParam(':email', $email, PDO::PARAM_STR);
        $insertStmt->bindParam(':password', $hashed_password, PDO::PARAM_STR);
        
        $insertStmt->execute();
        $userId = $pdo->lastInsertId();
        
        // Commit transaction
        $pdo->commit();
        
        // Set session variables
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_name'] = $username;
        $_SESSION['last_activity'] = time();
        
        // Regenerate session ID to prevent session fixation
        session_regenerate_id(true);
        
        return ['success' => true, 'message' => 'Registration successful'];
        
    } catch (PDOException $e) {
        if ($pdo && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        // Check for duplicate entry
        if ($e->getCode() == 23000) {
            if (strpos($e->getMessage(), 'username') !== false) {
                return ['success' => false, 'message' => 'This username already exists'];
            }
            if (strpos($e->getMessage(), 'email') !== false) {
                return ['success' => false, 'message' => 'This email is already registered'];
            }
        }
        
        error_log("Registration error: " . $e->getMessage());
        return ['success' => false, 'message' => 'An error occurred during registration'];
    }
}

/**
 * Initiates a password reset process for a user
 *
 * @param string $email The email address of the user
 * @return array Associative array with 'success' boolean and 'message' string
 */
function requestPasswordReset($email) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            return ['success' => false, 'message' => 'Database connection failed'];
        }
        
        // Validate email
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return ['success' => false, 'message' => 'Invalid email format'];
        }
        
        // Check if email exists
        $stmt = $pdo->prepare("SELECT id FROM users WHERE email = :email");
        $stmt->bindParam(':email', $email, PDO::PARAM_STR);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            // Don't reveal if email exists or not for security
            return ['success' => true, 'message' => 'If your email is registered, you will receive reset instructions'];
        }
        
        // Generate a secure token
        $token = bin2hex(random_bytes(32));
        $expiry = date('Y-m-d H:i:s', time() + 3600); // 1 hour expiry
        
        // Store token in database
        $resetStmt = $pdo->prepare("
            INSERT INTO password_resets (email, token, expires_at)
            VALUES (:email, :token, :expires_at)
            ON DUPLICATE KEY UPDATE token = :token, expires_at = :expires_at
        ");
        
        $resetStmt->bindParam(':email', $email, PDO::PARAM_STR);
        $resetStmt->bindParam(':token', $token, PDO::PARAM_STR);
        $resetStmt->bindParam(':expires_at', $expiry, PDO::PARAM_STR);
        $resetStmt->execute();
        
        // In a real application, send email with reset link
        // For example: $resetLink = "https://yourdomain.com/reset-password.php?token=$token";
        
        return ['success' => true, 'message' => 'If your email is registered, you will receive reset instructions'];
        
    } catch (Exception $e) {
        error_log("Password reset request error: " . $e->getMessage());
        return ['success' => false, 'message' => 'An error occurred processing your request'];
    }
}

/**
 * Checks if a user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Gets the current user's ID
 *
 * @return int|null User ID if logged in, null otherwise
 */
function getCurrentUserId() {
    return isLoggedIn() ? $_SESSION['user_id'] : null;
}

/**
 * Checks for session timeout and refreshes if active
 *
 * @param int $timeout Timeout period in seconds (default 30 minutes)
 * @return bool True if session is valid, false if timed out
 */
function checkSessionTimeout($timeout = 1800) {
    if (!isLoggedIn()) {
        return false;
    }
    
    $current_time = time();
    
    // Check if session has timed out
    if (isset($_SESSION['last_activity']) && ($current_time - $_SESSION['last_activity']) > $timeout) {
        logoutUser();
        return false;
    }
    
    // Refresh last activity time
    $_SESSION['last_activity'] = $current_time;
    return true;
}
?>
