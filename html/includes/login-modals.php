<?php require_once 'config.php'; ?>
<!-- login start -->
<div class="pa-login-model">
    <div class="modal fade" id="loginModel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
                <div class="modal-body">
                    <h1 class="pa-login-title">Login</h1>
                    <?php if (isset($_SESSION['login_error'])): ?>
                        <div class="alert alert-danger">
                            <?php 
                            echo $_SESSION['login_error'];
                            unset($_SESSION['login_error']);
                            ?>
                        </div>
                    <?php endif; ?>
                    <form method="post" action="includes/login_process.php">
                        <div class="pa-login-form">
                            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
                            <input type="text" name="username" placeholder="Gebruikersnaam" required 
                                value="<?php echo isset($_SESSION['form_data']['username']) ? htmlspecialchars($_SESSION['form_data']['username']) : ''; ?>">
                            <input type="password" name="password" placeholder="Wachtwoord" required>
                            <div class="pa-remember">
                                <label>Onthoud me
                                    <input type="checkbox" name="remember">
                                    <span class="s_checkbox"></span>
                                </label>
                                <a href="javascript:;" class="pa-forgot-password" data-bs-toggle="modal"
                                    data-bs-target="#forgotModal" data-bs-dismiss="modal" aria-label="Close">Wachtwoord vergeten?</a>
                            </div>
                            <div class="pa-login-btn">
                                <button type="submit" class="pa-btn">Login</button>
                                <p>Nog geen account? <a href="javascript:;" data-bs-toggle="modal"
                                        data-bs-target="#signUpModal" data-bs-dismiss="modal">Aanmelden</a></p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- login end -->
<!-- signup start -->
<div class="pa-login-model pa-signup-modal">
    <div class="modal fade" id="signUpModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="modal-body">
                    <h1 class="pa-login-title">Aanmelden</h1>
                    <?php if (isset($_SESSION['signup_error'])): ?>
                        <div class="alert alert-danger">
                            <?php 
                            echo $_SESSION['signup_error'];
                            unset($_SESSION['signup_error']);
                            ?>
                        </div>
                    <?php endif; ?>
                    <form method="post" action="includes/signup_process.php">
                        <div class="pa-login-form">
                            <input type="text" name="username" placeholder="Gebruikersnaam" required>
                            <input type="email" name="email" placeholder="Email" required>
                            <input type="password" name="password" placeholder="Wachtwoord" required>
                            <input type="password" name="confirm_password" placeholder="Bevestig wachtwoord" required>
                            <div class="pa-login-btn">
                                <button type="submit" class="pa-btn">Aanmelden</button>
                                <p>Heb je al een account? <a href="javascript:;" data-bs-toggle="modal"
                                        data-bs-target="#loginModel" data-bs-dismiss="modal"
                                        aria-label="Close">Login</a></p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- signup end -->
<!-- forgot start -->
<div class="pa-login-model pa-forgot-modal">
    <div class="modal fade" id="forgotModal">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <button type="button" class=" btn-close" data-bs-dismiss="modal" aria-label="Close">
                </button>
                <div class="modal-body">
                    <h1 class="pa-login-title">Wachtwoord vergeten</h1>
                    <form method="post" action="forgot_password.php">
                        <div class="pa-login-form">
                            <input type="email" name="email" placeholder="Email" required>
                            <div class="pa-login-btn">
                                <button type="submit" class="pa-btn">Vergeten</button>
                                <p>Heb je  geen account? <a href="javascript:;" data-bs-toggle="modal"
                                        data-bs-target="#signUpModal" data-bs-dismiss="modal" aria-label="Close">Aanmelden</a></p>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- forgot end -->
