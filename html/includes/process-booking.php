<?php
session_start();
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/mail_config.php';
require_once __DIR__ . '/email_helper.php';
require_once __DIR__ . '/../vendor/autoload.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Function to redirect with message
function redirectWithMessage($type, $message) {
    $_SESSION["{$type}_message"] = $message;
    header('Location: ../afspraak-maken.php');
    exit;
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    logError('Invalid request method', ['method' => $_SERVER['REQUEST_METHOD']]);
    redirectWithMessage('error', 'Ongeldige aanvraag methode.');
}

// Initialize validation errors array
$errors = [];

// Required fields
$requiredFields = ['service_type', 'client_name', 'client_email', 'appointment_date', 'appointment_time'];
foreach ($requiredFields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = "Het veld '" . str_replace('_', ' ', $field) . "' is verplicht.";
    }
}

// Email validation
if (!empty($_POST['client_email']) && !filter_var($_POST['client_email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Ongeldig e-mailadres.";
}

// Date validation
if (!empty($_POST['appointment_date'])) {
    $selectedDate = new DateTime($_POST['appointment_date']);
    $today = new DateTime();
    $today->setTime(0, 0, 0);
    
    if ($selectedDate < $today) {
        $errors[] = "De gekozen datum mag niet in het verleden liggen.";
    }
}

// If validation errors exist, redirect back with errors
if (!empty($errors)) {
    $_SESSION['appointment_errors'] = $errors;
    $_SESSION['form_data'] = $_POST; // Save form data for repopulation
    logError('Validation errors', ['errors' => $errors, 'data' => $_POST]);
    header('Location: ../afspraak-maken.php');
    exit;
}

try {
    // Sanitize input
    $service_type = filter_input(INPUT_POST, 'service_type', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $service_id = filter_input(INPUT_POST, 'behandeling_id', FILTER_SANITIZE_NUMBER_INT) ?: 
                 filter_input(INPUT_POST, 'workshop_id', FILTER_SANITIZE_NUMBER_INT) ?:
                 filter_input(INPUT_POST, 'welkomstconsult_id', FILTER_SANITIZE_NUMBER_INT);
    $client_name = filter_input(INPUT_POST, 'client_name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $client_email = filter_input(INPUT_POST, 'client_email', FILTER_SANITIZE_EMAIL);
    $client_phone = filter_input(INPUT_POST, 'client_phone', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $appointment_date = filter_input(INPUT_POST, 'appointment_date', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $appointment_time = filter_input(INPUT_POST, 'appointment_time', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
    $notes = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

    // Ensure database connection is established
    $pdo = getPDO();
    if (!isset($pdo)) {
        throw new Exception("Database connection not established");
    }

    // Check for duplicate appointments
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM appointments 
        WHERE appointment_date = :date 
        AND appointment_time = :time 
        AND status != 'cancelled'
    ");
    $stmt->execute([
        ':date' => $appointment_date,
        ':time' => $appointment_time
    ]);
    
    if ($stmt->fetchColumn() > 0) {
        redirectWithMessage('error', 'Deze tijd is helaas al geboekt. Kies een andere tijd.');
    }

    // Begin transaction
    $pdo->beginTransaction();

    // Insert appointment
    $stmt = $pdo->prepare("
        INSERT INTO appointments 
        (service_type, service_id, client_name, client_email, client_phone, 
         appointment_date, appointment_time, notes, status, created_at)
        VALUES 
        (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
    ");

    $success = $stmt->execute([
        $service_type,
        $service_id,
        $client_name,
        $client_email,
        $client_phone,
        $appointment_date,
        $appointment_time,
        $notes
    ]);

    if (!$success) {
        throw new Exception("Er is een fout opgetreden bij het opslaan van de afspraak.");
    }

    // After successful booking insertion
    $appointmentId = $pdo->lastInsertId();
    error_log("New appointment created with ID: " . $appointmentId);

    // Encrypt the appointment ID for the redirect URL
    $encryption_key = ENCRYPTION_KEY;
    $encrypted_id = base64_encode(openssl_encrypt(
        $appointmentId,
        'AES-256-CBC',
        $encryption_key,
        0,
        substr(hash('sha256', $encryption_key), 0, 16)
    ));
    
    // Commit transaction
    $pdo->commit();
    
    // Store success message in session
    $_SESSION['booking_status'] = 'success';
    $_SESSION['booking_message'] = 'Uw afspraak is succesvol ingepland. U ontvangt spoedig een bevestiging per email.';
    
    // Check if this is an AJAX request
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'Uw afspraak is succesvol geboekt! U ontvangt spoedig een bevestiging per email.',
            'redirectUrl' => 'appointment_confirmation.php?id=' . urlencode($encrypted_id)
        ]);
        exit;
    }
    
    // Redirect with encrypted ID for regular form submission
    header('Location: ../appointment_confirmation.php?id=' . urlencode($encrypted_id));
    exit;

} catch (PDOException $e) {
    // Handle database-specific errors
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    logError('Database error', [
        'error' => $e->getMessage(),
        'data' => json_encode($_POST) // Fixed array to string conversion
    ]);
    
    redirectWithMessage('error', 'Er is een fout opgetreden bij het maken van de afspraak. Probeer het later opnieuw.');
} catch (Exception $e) {
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // Store error message in session
    $_SESSION['booking_status'] = 'error';
    $_SESSION['booking_message'] = 'Er is een fout opgetreden: ' . $e->getMessage();
    
    // Redirect with error
    header('Location: ../afspraak-maken.php?status=error#booking-confirmation');
    exit;
}

// If we somehow get here, redirect to appointment page
header('Location: ../afspraak-maken.php');
exit;

// Helper function to get service name by ID (kept for reference if needed elsewhere)
function getServiceNameById($serviceType, $serviceId) {
    if (empty($serviceType) || empty($serviceId)) {
        return '';
    }
    
    // Convert to integers for comparison
    $serviceId = (int)$serviceId;
    
    if ($serviceType === 'behandeling') {
        // List of treatments
        $behandelingen = [
            1 => ['name' => 'Abhyanga massage', 'price' => 95.00],
            2 => ['name' => 'Kruidenstempelmassage 60 min', 'price' => 75.00],
            3 => ['name' => 'Kruidenstempelmassage 90 min', 'price' => 100.00],
            4 => ['name' => 'Ontspanningsmassage 30 min', 'price' => 45.00],
            5 => ['name' => 'Ontspanningsmassage 60 min', 'price' => 60.00],
            6 => ['name' => 'Ontspanningsmassage 90 min', 'price' => 90.00],
            7 => ['name' => 'Stemvorktherapie', 'price' => 55.00],
            8 => ["name" => "Tienermassage", "price" => 45.00],
            9 => ["name" => "Go4Balance Consult", "price" => 45.00]
        ];
        
        return isset($behandelingen[$serviceId]) ? $behandelingen[$serviceId]['name'] : '';
        
    } else if ($serviceType === 'workshop') {
        // List of workshops
        $workshops = [
            10 => ['name' => 'Mild in het wild Compassiewandeling', 'price' => 45.00],
            11 => ['name' => 'Workshop Uit je hoofd in je lijf', 'price' => 39.95]
        ];
        
        return isset($workshops[$serviceId]) ? $workshops[$serviceId]['name'] : '';
    } else if ($serviceType === 'welkomstconsult') {
        // List of welkomstconsult options
        $welkomstconsult = [
            12 => ['name' => 'Shiro Abhyanga (Welkomstconsult)', 'price' => 18.00],
            13 => ['name' => 'Pad Abhyanga (Welkomstconsult)', 'price' => 18.00]
        ];
        
        return isset($welkomstconsult[$serviceId]) ? $welkomstconsult[$serviceId]['name'] : '';
    }
    
    return '';
}
