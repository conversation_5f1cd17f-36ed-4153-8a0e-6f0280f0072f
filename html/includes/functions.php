<?php
/**
 * User and Order management functions
 * 
 * This file contains functions for retrieving user data and sending order confirmations.
 */
require_once 'db.php';

/**
 * Retrieves user data for a specific user ID
 *
 * @param int $userId The ID of the user to retrieve
 * @return array|false User data array or false on failure
 */
function getUserData($userId) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            error_log("Database connection failed in getUserData");
            return false;
        }
        
        $stmt = $pdo->prepare("SELECT username, email, created_at FROM users WHERE id = :userId");
        $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $stmt->execute();
        
        $userData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$userData) {
            error_log("No user found for ID: " . $userId);
            return false;
        }
        
        return $userData;
    } catch (PDOException $e) {
        error_log("Error getting user data: " . $e->getMessage());
        return false;
    }
}

/**
 * Sends an order confirmation email to the customer
 *
 * @param int $orderId The ID of the order to send confirmation for
 * @return bool True on success, false on failure
 */
function sendOrderConfirmationEmail($orderId) {
    try {
        $pdo = getPDO();
        
        if (!$pdo) {
            error_log("Database connection failed in sendOrderConfirmationEmail");
            return false;
        }
        
        // Get order details with customer email
        $stmt = $pdo->prepare("
            SELECT o.*, u.email as customer_email, oi.quantity, oi.price, p.name as product_name 
            FROM orders o 
            JOIN users u ON o.user_id = u.id
            JOIN order_items oi ON o.order_id = oi.order_id 
            JOIN products p ON oi.product_id = p.id 
            WHERE o.order_id = :orderId
        ");
        $stmt->bindParam(':orderId', $orderId, PDO::PARAM_INT);
        $stmt->execute();
        $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($orderItems)) {
            error_log("No order items found for order ID: " . $orderId);
            return false;
        }
        
        // Build email content
        $to = $orderItems[0]['customer_email'];
        $subject = "Bevestiging van uw bestelling - " . $orderId;
        
        $message = "<html><body>";
        $message .= "<h2>Bedankt voor uw bestelling</h2>";
        $message .= "<p>Uw bestelnummer: " . htmlspecialchars($orderId) . "</p>";
        $message .= "<h3>Bestelde producten:</h3><ul>";
        
        $totalAmount = 0;
        foreach ($orderItems as $item) {
            $itemTotal = $item['quantity'] * $item['price'];
            $totalAmount += $itemTotal;
            
            $message .= "<li>" . htmlspecialchars($item['product_name']) . " - " . 
                        htmlspecialchars($item['quantity']) . "x €" . 
                        number_format($item['price'], 2) . "</li>";
        }
        
        $message .= "</ul>";
        $message .= "<p>Totaalbedrag: €" . number_format($totalAmount, 2) . "</p>";
        $message .= "</body></html>";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        return mail($to, $subject, $message, implode("\r\n", $headers));
    } catch (PDOException $e) {
        error_log("Error sending order confirmation: " . $e->getMessage());
        return false;
    }
}