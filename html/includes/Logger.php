<?php
class Logger {
    private static $instance = null;
    private $logFile;
    private $logDir;

    private function __construct($name) {
        $baseDir = realpath(__DIR__ . '/..');
        $this->logDir = $baseDir . '/logs';
        $this->logFile = $this->logDir . '/' . $name . '.log';
        
        $this->initializeLog();
    }

    public static function get($name = 'app') {
        if (self::$instance === null) {
            self::$instance = new self($name);
        }
        return self::$instance;
    }

    private function initializeLog() {
        if (!file_exists($this->logDir)) {
            mkdir($this->logDir, 0755, true);
        }
        
        if (!file_exists($this->logFile)) {
            touch($this->logFile);
            chmod($this->logFile, 0644);
        }
    }

    public function info($message, $data = []) {
        $this->write('INFO', $message, $data);
    }

    public function error($message, $data = []) {
        $this->write('ERROR', $message, $data);
    }

    public function debug($message, $data = []) {
        $this->write('DEBUG', $message, $data);
    }

    private function write($level, $message, $data) {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp][$level] $message";
        
        if (!empty($data)) {
            $logEntry .= "\nData: " . json_encode($data, JSON_PRETTY_PRINT);
        }
        
        $logEntry .= "\n";
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
}