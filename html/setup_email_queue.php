<?php
/**
 * Setup Email Queue Directories
 * 
 * This script creates the necessary directories for the email queue system.
 */

// Define the queue directories
$queueDirs = [
    __DIR__ . '/queue',
    __DIR__ . '/queue/emails',
    __DIR__ . '/queue/emails/failed',
    __DIR__ . '/queue/emails/processed'
];

// Create each directory if it doesn't exist
foreach ($queueDirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "Created directory: {$dir}\n";
        } else {
            echo "Failed to create directory: {$dir}\n";
        }
    } else {
        echo "Directory already exists: {$dir}\n";
    }
}

// Create .htaccess file to protect the queue directory
$htaccess = __DIR__ . '/queue/.htaccess';
$htaccessContent = "Order deny,allow\nDeny from all\n";

if (file_put_contents($htaccess, $htaccessContent)) {
    echo "Created .htaccess file to protect queue directory.\n";
} else {
    echo "Failed to create .htaccess file.\n";
}

echo "Setup complete.\n";