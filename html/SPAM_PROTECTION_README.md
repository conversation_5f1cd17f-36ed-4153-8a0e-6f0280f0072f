# Spam Protection Implementation

Dit document beschrijft de geïmplementeerde spam-beschermingsmaatregelen voor de formulieren op de website.

## Overzicht van Beschermingslagen

### 1. Google reCAPTCHA v3
- **Locatie**: Be<PERSON> formulieren (contact en nieuwsbrief)
- **Functie**: Analyseert gebruikersgedrag en geeft een score (0.0-1.0)
- **Drempelwaarde**: 0.5 (aan<PERSON><PERSON><PERSON> in `includes/spam_protection.php`)
- **Site Key**: `6Lf7Te0oAAAAAFSV-wPTrZvERy7_I3lCtnQsin7F`

### 2. Honeypot Velden
- **Locatie**: Beide formulieren
- **Veldnaam**: `website`
- **Functie**: Onzichtbaar veld dat bots vaak invullen
- **CSS Class**: `.honeypot-field` voor complete onzichtbaarheid

### 3. Rate Limiting
- **Contactformulier**: Max 3 submissions per 5 minuten per IP
- **Nieuwsbrief**: Max 2 submissions per 5 minuten per IP
- **Opslag**: Tijdelijke bestanden in system temp directory

### 4. Server-side Validatie
- Input sanitization
- Email validatie
- Verplichte velden controle

## Bestanden Overzicht

### Core Bestanden
- `includes/recaptcha_config.php` - **NIEUWE** Centrale reCAPTCHA configuratie
- `includes/spam_protection.php` - Hoofdfuncties voor spam-bescherming
- `ajaxmail.php` - Contact formulier verwerking
- `subscribe.php` - Nieuwsbrief formulier verwerking

### Frontend Bestanden
- `assets/js/custom.js` - JavaScript voor reCAPTCHA integratie
- `assets/css/style.css` - CSS voor honeypot velden
- `contact.php` - Contact formulier HTML
- `includes/footer.php` - Nieuwsbrief formulier HTML

### Test Bestanden
- `test_spam_protection.php` - Test script voor alle beschermingslagen
- `validate_recaptcha_config.php` - **NIEUW** Validatie en configuratie overzicht

## Configuratie

### reCAPTCHA Keys Beheren (NIEUW!)
Alle reCAPTCHA keys worden nu centraal beheerd in `includes/recaptcha_config.php`:

```php
// Site Key (Public) - gebruikt in frontend
const SITE_KEY = '6Lf7Te0oAAAAAFSV-wPTrZvERy7_I3lCtnQsin7F';

// Secret Key (Private) - gebruikt in backend
const SECRET_KEY = '6Lf7Te0oAAAAAMelSFNpj38tZIjVXwZALjg8g2-v';

// Standaard minimum score
const DEFAULT_MIN_SCORE = 0.5;
```

### Keys Bijwerken
1. Bewerk alleen `includes/recaptcha_config.php`
2. Alle andere bestanden gebruiken automatisch de nieuwe keys
3. Valideer configuratie met `validate_recaptcha_config.php`

### Rate Limiting Instellingen
```php
// Contactformulier
'max_submissions' => 3,
'time_window' => 300, // 5 minuten

// Nieuwsbrief
'max_submissions' => 2,
'time_window' => 300, // 5 minuten
```

## Gebruik van Spam Protection Helper

### Basis Gebruik
```php
require_once 'includes/spam_protection.php';

$client_ip = $_SERVER['REMOTE_ADDR'];
$spam_check = checkSpamProtection($_POST, $client_ip, 'form_type', [
    'max_submissions' => 3,
    'time_window' => 300,
    'min_score' => 0.5,
    'expected_action' => 'contact'
]);

if (!$spam_check['allowed']) {
    echo json_encode(['success' => false, 'message' => $spam_check['error_message']]);
    exit;
}
```

### Individuele Functies
```php
// Honeypot check
$is_spam = checkHoneypot($_POST, 'website');

// Rate limiting
$rate_info = checkRateLimit($ip, 'form_type', 3, 300);

// reCAPTCHA verificatie
$recaptcha_result = verifyRecaptcha($token, $ip, 0.5, 'contact');

// Submission registreren
recordSubmission($ip, 'form_type', $existing_submissions);
```

## Testing

### Automatische Tests
```bash
# Via browser
http://yoursite.com/test_spam_protection.php

# Via command line
php test_spam_protection.php
```

### Handmatige Tests
1. **Honeypot Test**: Vul het onzichtbare 'website' veld in via browser developer tools
2. **Rate Limiting Test**: Verstuur meerdere formulieren snel achter elkaar
3. **reCAPTCHA Test**: Blokkeer JavaScript om reCAPTCHA te voorkomen

## Monitoring en Logging

### Log Locaties
- PHP error logs bevatten spam detection events
- Rate limiting events worden gelogd met IP adressen
- reCAPTCHA failures worden gedetailleerd gelogd

### Log Voorbeelden
```
Spam detected: honeypot field filled from IP: ************* for form: contact
Rate limit exceeded for IP: ************* for form: newsletter
reCAPTCHA score too low: 0.3 for IP: ************* for form: contact
```

## Onderhoud

### Rate Limit Bestanden Opruimen
Rate limit bestanden worden automatisch opgeruimd, maar voor extra zekerheid:
```bash
# Verwijder oude rate limit bestanden (ouder dan 1 dag)
find /tmp -name "*_rate_limit_*" -mtime +1 -delete
```

### reCAPTCHA Score Aanpassen
Als er te veel false positives zijn, verlaag de score:
```php
// In spam_protection.php of bij functie aanroep
$min_score = 0.3; // Minder streng
$min_score = 0.7; // Strenger
```

## Troubleshooting

### Veel False Positives
- Verlaag reCAPTCHA score drempel
- Verhoog rate limiting limieten
- Controleer honeypot CSS styling

### Spam Komt Nog Door
- Verhoog reCAPTCHA score drempel
- Verlaag rate limiting limieten
- Voeg extra validatie toe

### reCAPTCHA Werkt Niet
- Controleer site key en secret key
- Verificeer domain configuratie in Google Console
- Test netwerk connectiviteit naar Google servers

## Security Overwegingen

1. **Secret Keys**: Bewaar reCAPTCHA secret key veilig
2. **Rate Limiting**: Bestanden in temp directory zijn relatief veilig
3. **Logging**: Vermijd het loggen van gevoelige gebruikersdata
4. **Updates**: Houd reCAPTCHA libraries up-to-date

## Prestatie Impact

- **reCAPTCHA**: ~100-200ms extra per request
- **Rate Limiting**: ~1-5ms extra per request
- **Honeypot**: Geen merkbare impact
- **Totaal**: Minimale impact op gebruikerservaring
