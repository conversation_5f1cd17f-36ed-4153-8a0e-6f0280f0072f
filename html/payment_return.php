<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/mollie_config.php';
require_once 'includes/mail_config.php'; 
require_once 'includes/email_helper.php';

// Set error handling to catch all errors
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Initialize database connection
try {
    $pdo = getPDO();
    if (!$pdo) {
        throw new Exception("Failed to connect to database");
    }
} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());
    // We'll continue and handle this error in the main try-catch block
}

// Define a global variable to store error messages
$processingErrors = [];

/**
 * Log detailed payment return information
 */
function logPaymentReturn($message, $data = []) {
    $logFile = __DIR__ . '/logs/payment_returns.log';
    $logDir = dirname($logFile);
    
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    $sessionId = session_id();
    
    $logEntry = [
        'timestamp' => $timestamp,
        'ip' => $clientIp,
        'session_id' => $sessionId,
        'message' => $message,
        'data' => $data
    ];
    
    file_put_contents(
        $logFile, 
        json_encode($logEntry, JSON_PRETTY_PRINT) . "\n---\n", 
        FILE_APPEND
    );
}

/**
 * Add this function to help with debugging
 */
function logDetailedError($e, $context = []) {
    global $processingErrors;
    
    // Add to our global errors array
    $processingErrors[] = [
        'message' => $e->getMessage(),
        'context' => $context
    ];
    
    logPaymentReturn("Detailed error", [
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString(),
        'context' => $context
    ]);
}

// Set a custom error handler
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    logPaymentReturn("PHP Error", [
        'errno' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ]);
    return false; // Let PHP handle the error as well
});

// Set exception handler
set_exception_handler(function($e) {
    logDetailedError($e, ['handler' => 'global_exception_handler']);
});

// Initialize variables that will be used in the template
$order = null;
$status = 'unknown';
$items = [];
$payment = null;

/**
 * Get order details with secure validation
 */
function getOrderDetails($orderId, $pdo) {
    // Log the order ID we're trying to fetch
    logPaymentReturn("Attempting to get order details", ['order_id' => $orderId]);
    
    // Basic validation
    if (empty($orderId)) {
        throw new Exception("Empty order ID");
    }
    
    // First try: direct query by ID (most common case)
    if (is_numeric($orderId)) {
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($order) {
            logPaymentReturn("Found order by ID", ['id' => $orderId]);
            return $order;
        }
    }
    
    // Second try: by order_id field
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($order) {
        logPaymentReturn("Found order by order_id", ['order_id' => $orderId]);
        return $order;
    }
    
    // Third try: if order_id starts with "ORDER_", try without that prefix
    if (strpos($orderId, 'ORDER_') === 0) {
        $cleanOrderId = substr($orderId, 6); // Remove "ORDER_" prefix
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE order_id = ?");
        $stmt->execute([$cleanOrderId]);
        $order = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($order) {
            logPaymentReturn("Found order using cleaned ID", [
                'original_id' => $orderId,
                'cleaned_id' => $cleanOrderId
            ]);
            return $order;
        }
    }
    
    // If we still haven't found the order, log and throw exception
    logPaymentReturn("Order not found after all attempts", ['order_id' => $orderId]);
    throw new Exception("Order not found");
}

/**
 * Verify payment status with Mollie API
 */
function verifyPaymentWithMollie($paymentId, $mollie) {
    if (empty($paymentId)) {
        return null;
    }
    
    try {
        return $mollie->payments->get($paymentId);
    } catch (Exception $e) {
        logPaymentReturn("Mollie API error", ['error' => $e->getMessage(), 'payment_id' => $paymentId]);
        return null;
    }
}

/**
 * Send order confirmation email
 */
function sendOrderConfirmationEmail($order, $pdo) {
    // Get customer email from order or session
    $email = null;
    
    // Try to get email from order data
    if (isset($order['customer_email']) && !empty($order['customer_email'])) {
        $email = $order['customer_email'];
    } 
    // Try to get from shipping_address JSON if exists
    else if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
        $addressData = json_decode($order['shipping_address'], true);
        if (isset($addressData['email']) && !empty($addressData['email'])) {
            $email = $addressData['email'];
        }
    }
    // Try to get from session
    else if (isset($_SESSION['customer']['email']) && !empty($_SESSION['customer']['email'])) {
        $email = $_SESSION['customer']['email'];
    }
    
    if (!$email) {
        logPaymentReturn("No customer email found", ['order_id' => $order['id']]);
        return false;
    }
    
    // Get customer name
    $customerName = null;
    if (isset($order['customer_name']) && !empty($order['customer_name'])) {
        $customerName = $order['customer_name'];
    } else if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
        $addressData = json_decode($order['shipping_address'], true);
        if (isset($addressData['name']) && !empty($addressData['name'])) {
            $customerName = $addressData['name'];
        }
    } else {
        $customerName = "Geachte klant";
    }
    
    // Format total amount
    $totalAmount = number_format($order['total_amount'], 2, ',', '.');
    
    // Get order items
    $orderItems = [];
    try {
        $stmt = $pdo->prepare("SELECT oi.*, p.name as product_name FROM order_items oi LEFT JOIN products p ON oi.product_id = p.id WHERE oi.order_id = ?");
        $stmt->execute([$order['id']]);
        $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        logPaymentReturn("Error getting order items", ['error' => $e->getMessage()]);
    }
    
    // Log the email attempt with detailed information
    logPaymentReturn("Attempting to send order confirmation email", [
        'order_id' => $order['id'],
        'customer_email' => $email,
        'customer_name' => $customerName,
        'total_amount' => $totalAmount,
        'items_count' => count($orderItems)
    ]);
    
    // Build item list for email
    $itemsList = "";
    foreach ($orderItems as $item) {
        $itemPrice = number_format($item['price'], 2, ',', '.');
        $itemTotal = number_format($item['price'] * $item['quantity'], 2, ',', '.');
        $itemsList .= "<tr>
            <td style='padding: 10px; border-bottom: 1px solid #eee;'>{$item['product_name']}</td>
            <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: center;'>{$item['quantity']}</td>
            <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: right;'>€{$itemPrice}</td>
            <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: right;'>€{$itemTotal}</td>
        </tr>";
    }

    // Calculate subtotal, shipping, and VAT
    $deliveryMethod = $order['delivery_method'] ?? 'shipping';
    $shipping = ($deliveryMethod === 'shipping') ? 4.95 : 0; // Only apply shipping cost if shipping method
    $subtotal = $order['total_amount'] - ($deliveryMethod === 'shipping' ? $shipping : 0);
    $vat = $subtotal * 0.21; // 21% VAT

    // Format for display
    $formattedSubtotal = number_format($subtotal, 2, ',', '.');
    $formattedShipping = number_format($shipping, 2, ',', '.');
    $formattedVat = number_format($vat, 2, ',', '.');

    // Shipping row HTML - only show if shipping method is selected
    $shippingRowHtml = ($deliveryMethod === 'shipping') 
        ? "<tr><td>Verzendkosten:</td><td style='text-align: right;'>€{$formattedShipping}</td></tr>" 
        : "";

    // Customer email template
    $customerMessage = "<!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Bevestiging bestelling #{$order['id']}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; }
            .header { background-color: #d5bc5a; padding: 20px; color: white; }
            .content { padding: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th { text-align: left; padding: 10px; background-color: #f2f2f2; }
            .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
            .footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Bedankt voor uw bestelling!</h1>
            </div>
            <div class='content'>
                <p>Beste {$customerName},</p>
                <p>Uw bestelling is succesvol ontvangen en wordt verwerkt.</p>
                <p><strong>Bestelnummer:</strong> #{$order['id']}</p>
                
                <table>
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th style='text-align: center;'>Aantal</th>
                            <th style='text-align: right;'>Prijs</th>
                            <th style='text-align: right;'>Totaal</th>
                        </tr>
                    </thead>
                    <tbody>
                        {$itemsList}
                    </tbody>
                </table>
                
                <div class='total'>
                    <table style='width: 100%;'>
                        <tr><td>Subtotaal:</td><td style='text-align: right;'>€{$formattedSubtotal}</td></tr>
                        {$shippingRowHtml}
                        <tr><td>BTW (21%):</td><td style='text-align: right;'>€{$formattedVat}</td></tr>
                        <tr style='font-weight: bold;'><td>Totaalbedrag:</td><td style='text-align: right;'>€{$totalAmount}</td></tr>
                    </table>
                </div>
                
                <p>Heeft u vragen over uw bestelling? Neem dan contact met ons <NAME_EMAIL>.</p>
            </div>
            <div class='footer'>
                <p>© " . date('Y') . " Jacqueline Tjassens. Alle rechten voorbehouden.</p>
            </div>
        </div>
    </body>
    </html>";
    
    // Get address data for owner email
    $addressHtml = '';
    if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
        $addressData = json_decode($order['shipping_address'], true);
        $addressHtml = "
            <p><strong>Naam:</strong> " . htmlspecialchars($addressData['name'] ?? $customerName) . "</p>
            <p><strong>Adres:</strong> " . htmlspecialchars($addressData['address'] ?? '') . "</p>
            <p><strong>Postcode:</strong> " . htmlspecialchars($addressData['postal_code'] ?? '') . "</p>
            <p><strong>Plaats:</strong> " . htmlspecialchars($addressData['city'] ?? '') . "</p>
            <p><strong>Bezorgmethode:</strong> " . htmlspecialchars($deliveryMethod === 'pickup' ? 'Ophalen in Zoetermeer' : 'Verzenden') . "</p>";
    } else {
        $addressHtml = "<p>Geen adresgegevens beschikbaar</p>";
    }
    
    // Owner email template
    $ownerMessage = "<!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Nieuwe bestelling #{$order['id']}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; }
            .header { background-color: #d5bc5a; padding: 20px; color: white; }
            .content { padding: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th { text-align: left; padding: 10px; background-color: #f2f2f2; }
            .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
            .address { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Nieuwe bestelling ontvangen!</h1>
            </div>
            <div class='content'>
                <p>Er is een nieuwe bestelling geplaatst:</p>
                <p><strong>Bestelnummer:</strong> #{$order['id']}</p>
                <p><strong>Klant:</strong> {$customerName}</p>
                <p><strong>Email:</strong> {$email}</p>

                <!-- Added address information -->
                <div class='address'>
                    <h4>Verzendgegevens</h4>
                    {$addressHtml}
                </div>
                
                <table>
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th style='text-align: center;'>Aantal</th>
                            <th style='text-align: right;'>Prijs</th>
                            <th style='text-align: right;'>Totaal</th>
                        </tr>
                    </thead>
                    <tbody>
                        {$itemsList}
                    </tbody>
                </table>
                
                <div class='total'>
                    <table style='width: 100%;'>
                        <tr><td>Subtotaal:</td><td style='text-align: right;'>€{$formattedSubtotal}</td></tr>
                        {$shippingRowHtml}
                        <tr><td>BTW (21%):</td><td style='text-align: right;'>€{$formattedVat}</td></tr>
                        <tr style='font-weight: bold;'><td>Totaalbedrag:</td><td style='text-align: right;'>€{$totalAmount}</td></tr>
                    </table>
                </div>
            </div>
        </div>
    </body>
    </html>";

    // Try sending customer email first
    $customerSubject = "Bevestiging bestelling #{$order['id']} - Jacqueline Tjassens";
    $customerAltBody = "Bedankt voor uw bestelling #{$order['id']}. Totaalbedrag: €{$totalAmount}";
    
    // Log before sending
    logPaymentReturn("Sending customer email", [
        'to' => $email,
        'subject' => $customerSubject
    ]);
    
    // Send customer email with explicit parameters
    $customerEmailSent = sendEmail(
        $customerSubject,
        $customerMessage,
        $customerAltBody,
        $email,
        $customerName
    );
    
    // Log result
    logPaymentReturn("Customer email result", [
        'success' => $customerEmailSent ? 'yes' : 'no',
        'to' => $email
    ]);
    
    // Send to owner with explicit parameters
    $ownerSubject = "Nieuwe bestelling #{$order['id']} - Jacqueline Tjassens";
    $ownerAltBody = "Nieuwe bestelling #{$order['id']}. Totaalbedrag: €{$totalAmount}";
    
    // Log before sending
    logPaymentReturn("Sending owner email", [
        'to' => '<EMAIL>',
        'subject' => $ownerSubject
    ]);
    
    $ownerEmailSent = sendEmail(
        $ownerSubject,
        $ownerMessage,
        $ownerAltBody,
        '<EMAIL>',
        'Jacqueline Tjassens'
    );
    
    // Log result
    logPaymentReturn("Owner email result", [
        'success' => $ownerEmailSent ? 'yes' : 'no',
        'to' => '<EMAIL>'
    ]);
    
    // Log email sending results
    logPaymentReturn("Order confirmation emails summary", [
        'order_id' => $order['id'],
        'customer_email_sent' => $customerEmailSent ? 'yes' : 'no',
        'owner_email_sent' => $ownerEmailSent ? 'yes' : 'no',
        'customer_email' => $email
    ]);
    
    // Return true if at least one email was sent
    return $customerEmailSent || $ownerEmailSent;
}

/**
 * Helper function to send email with PHPMailer
 */
function sendEmailWithPHPMailer($subject, $message, $toEmail, $altBody = '') {
    // Log the email request
    logPaymentReturn("Email request", [
        'to' => $toEmail,
        'subject' => $subject
    ]);
    
    // Use our improved sendEmail function
    return sendEmail(
        $subject,
        $message,
        $altBody,
        $toEmail
    );
}

/**
 * Helper function to check if a column exists in a table
 */
function checkColumnExists($pdo, $table, $column) {
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
        return $stmt && $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Clear shopping cart after successful payment
 */
function clearShoppingCart() {
    unset($_SESSION['cart']);
    unset($_SESSION['pending_order_id']);
}

// Main processing code
try {
    // Get order ID from URL parameter
    $encryptedOrderId = isset($_GET['order']) ? $_GET['order'] : '';
    $orderId = null;
    
    if (!empty($encryptedOrderId)) {
        // Decrypt order ID
        $decryption_key = ENCRYPTION_KEY;
        $orderId = openssl_decrypt(
            base64_decode($encryptedOrderId),
            'AES-256-CBC',
            $decryption_key,
            0,
            substr(hash('sha256', $decryption_key), 0, 16)
        );
        
        if ($orderId === false) {
            throw new Exception("Invalid order ID");
        }
        
        $orderId = (int)$orderId;
    } else if (isset($_SESSION['pending_order_id'])) {
        $orderId = $_SESSION['pending_order_id'];
    }
    
    // Log the payment return attempt
    logPaymentReturn("Payment return initiated", [
        'order_id' => $orderId,
        'session_id' => session_id()
    ]);
    
    // Get order details with better error handling
    try {
        logPaymentReturn("Attempting to get order details", ['order_id' => $orderId]);
        
        // Basic validation
        if (empty($orderId)) {
            throw new Exception("Empty order ID");
        }
        
        // First try: direct query by ID (most common case)
        if (is_numeric($orderId)) {
            $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
            $stmt->execute([$orderId]);
            $order = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($order) {
                logPaymentReturn("Found order by ID", ['id' => $orderId]);
            }
        }
        
        // If order not found and we have a payment ID in the URL, try to find by payment ID
        if (empty($order) && isset($_GET['id'])) {
            $paymentId = $_GET['id'];
            $stmt = $pdo->prepare("SELECT * FROM orders WHERE payment_id = ?");
            $stmt->execute([$paymentId]);
            $order = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($order) {
                logPaymentReturn("Found order by payment ID", ['payment_id' => $paymentId]);
                $orderId = $order['id'];
            }
        }
        
        // If still not found, check session for cart data
        if (empty($order) && isset($_SESSION['cart']) && !empty($_SESSION['cart'])) {
            logPaymentReturn("Order not found, but cart exists in session", [
                'cart_items' => count($_SESSION['cart'])
            ]);
            // We'll handle this case later if needed
        }
        
        // If we still haven't found the order, log and throw exception
        if (empty($order)) {
            logPaymentReturn("Order not found after all attempts", ['order_id' => $orderId]);
            throw new Exception("Order not found");
        }
    } catch (Exception $e) {
        logPaymentReturn("Error getting order details", [
            'error' => $e->getMessage(),
            'order_id' => $orderId
        ]);
        throw $e; // Re-throw to be caught by the main try-catch
    }
    
    // Initialize variables
    $status = $order['status'] ?? 'unknown';
    
    // Initialize Mollie client with better error handling
    try {
        $mollie = getMollieClient();
        if (!$mollie) {
            throw new Exception("Failed to initialize Mollie client");
        }
        logPaymentReturn("Mollie client initialized", ['success' => 'yes']);
    } catch (Exception $e) {
        logDetailedError($e, ['function' => 'getMollieClient']);
        // Continue execution even if Mollie client initialization fails
    }
    
    // Verify payment status with better error handling
    if (isset($mollie) && $mollie && !empty($order['payment_id'])) {
        try {
            // Check if payment_id is in the correct format (should start with tr_)
            if (strpos($order['payment_id'], 'tr_') !== 0) {
                logPaymentReturn("Invalid payment ID format", [
                    'payment_id' => $order['payment_id'],
                    'expected_format' => 'Should start with tr_'
                ]);
                // Skip Mollie verification if payment ID is invalid
            } else {
                $payment = $mollie->payments->get($order['payment_id']);
                
                logPaymentReturn("Payment details from Mollie", [
                    'payment_id' => $order['payment_id'],
                    'mollie_status' => $payment->status,
                    'is_paid' => $payment->isPaid() ? 'yes' : 'no',
                    'is_cancelled' => $payment->isCanceled() ? 'yes' : 'no',
                    'is_expired' => $payment->isExpired() ? 'yes' : 'no',
                    'is_pending' => $payment->isPending() ? 'yes' : 'no'
                ]);
                
                // Determine new status based on Mollie payment status
                $newStatus = $status;
                if ($payment->isPaid()) {
                    $newStatus = 'paid';
                } else if ($payment->isCanceled()) {
                    $newStatus = 'cancelled';
                } else if ($payment->isExpired()) {
                    $newStatus = 'expired';
                } else if ($payment->isPending()) {
                    $newStatus = 'pending';
                } else if ($payment->isFailed()) {
                    $newStatus = 'failed';
                }
                
                // Update order status in database if it has changed
                if ($newStatus !== $status) {
                    try {
                        $stmt = $pdo->prepare("UPDATE orders SET status = ?, updated_at = NOW() WHERE id = ?");
                        $result = $stmt->execute([$newStatus, $order['id']]);
                        
                        logPaymentReturn("Status update attempt", [
                            'order_id' => $order['id'],
                            'old_status' => $status,
                            'new_status' => $newStatus,
                            'update_success' => $result ? 'yes' : 'no',
                            'rows_affected' => $stmt->rowCount()
                        ]);
                        
                        // Update the status in our current order variable
                        $status = $newStatus;
                    } catch (Exception $e) {
                        logDetailedError($e, ['function' => 'updateOrderStatus', 'order_id' => $order['id']]);
                        // Continue execution even if update fails
                    }
                } else {
                    logPaymentReturn("Status unchanged", [
                        'order_id' => $order['id'],
                        'status' => $status
                    ]);
                }
            }
        } catch (Exception $e) {
            logDetailedError($e, ['function' => 'verifyPaymentWithMollie', 'payment_id' => $order['payment_id']]);
            // Continue execution even if Mollie verification fails
        }
    } else {
        logPaymentReturn("Cannot verify payment", [
            'has_mollie_client' => isset($mollie) && $mollie ? 'yes' : 'no',
            'has_payment_id' => !empty($order['payment_id']) ? 'yes' : 'no',
            'payment_id' => $order['payment_id'] ?? 'not set'
        ]);
    }
    
    // Handle different status scenarios
    switch ($status) {
        case 'paid':
        case 'pending':
            // Send confirmation email if not already sent
            if (!isset($order['email_sent']) || $order['email_sent'] != 1) {
                try {
                    // Get order items
                    $stmt = $pdo->prepare("SELECT oi.*, p.name as product_name FROM order_items oi LEFT JOIN products p ON oi.product_id = p.id WHERE oi.order_id = ?");
                    $stmt->execute([$order['id']]);
                    $orderItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    // Get customer email from order or session
                    $email = null;
                    
                    // Try to get email from order data
                    if (isset($order['customer_email']) && !empty($order['customer_email'])) {
                        $email = $order['customer_email'];
                    } 
                    // Try to get from shipping_address JSON if exists
                    else if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
                        $addressData = json_decode($order['shipping_address'], true);
                        if (isset($addressData['email']) && !empty($addressData['email'])) {
                            $email = $addressData['email'];
                        }
                    }
                    // Try to get from session
                    else if (isset($_SESSION['customer']['email']) && !empty($_SESSION['customer']['email'])) {
                        $email = $_SESSION['customer']['email'];
                    }
                    
                    if (!$email) {
                        logPaymentReturn("No customer email found", ['order_id' => $order['id']]);
                    } else {
                        // Get customer name
                        $customerName = null;
                        if (isset($order['customer_name']) && !empty($order['customer_name'])) {
                            $customerName = $order['customer_name'];
                        } else if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
                            $addressData = json_decode($order['shipping_address'], true);
                            if (isset($addressData['name']) && !empty($addressData['name'])) {
                                $customerName = $addressData['name'];
                            }
                        } else {
                            $customerName = "Geachte klant";
                        }
                        
                        // Format total amount
                        $totalAmount = number_format($order['total_amount'], 2, ',', '.');
                        
                        // Build item list for email
                        $itemsList = "";
                        foreach ($orderItems as $item) {
                            $itemPrice = number_format($item['price'], 2, ',', '.');
                            $itemTotal = number_format($item['price'] * $item['quantity'], 2, ',', '.');
                            $itemsList .= "<tr>
                                <td style='padding: 10px; border-bottom: 1px solid #eee;'>{$item['product_name']}</td>
                                <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: center;'>{$item['quantity']}</td>
                                <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: right;'>€{$itemPrice}</td>
                                <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: right;'>€{$itemTotal}</td>
                            </tr>";
                        }

                        // Calculate subtotal, shipping, and VAT
                        $deliveryMethod = $order['delivery_method'] ?? 'shipping';
                        $shipping = ($deliveryMethod === 'shipping') ? 4.95 : 0; // Only apply shipping cost if shipping method
                        $subtotal = $order['total_amount'] - ($deliveryMethod === 'shipping' ? $shipping : 0);
                        $vat = $subtotal * 0.21; // 21% VAT

                        // Format for display
                        $formattedSubtotal = number_format($subtotal, 2, ',', '.');
                        $formattedShipping = number_format($shipping, 2, ',', '.');
                        $formattedVat = number_format($vat, 2, ',', '.');

                        // Shipping row HTML - only show if shipping method is selected
                        $shippingRowHtml = ($deliveryMethod === 'shipping') 
                            ? "<tr><td>Verzendkosten:</td><td style='text-align: right;'>€{$formattedShipping}</td></tr>" 
                            : "";

                        // Determine status message for email
                        $statusMessage = ($status === 'paid') 
                            ? "Uw bestelling is succesvol ontvangen en wordt verwerkt."
                            : "Uw bestelling is ontvangen en wordt verwerkt zodra de betaling is voltooid.";
                        
                        // Customer email template
                        $customerMessage = "<!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset='UTF-8'>
                            <title>Bevestiging bestelling #{$order['id']}</title>
                            <style>
                                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                                .container { max-width: 600px; margin: 0 auto; }
                                .header { background-color: #d5bc5a; padding: 20px; color: white; }
                                .content { padding: 20px; }
                                table { width: 100%; border-collapse: collapse; }
                                th { text-align: left; padding: 10px; background-color: #f2f2f2; }
                                .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
                                .footer { margin-top: 30px; font-size: 12px; color: #777; text-align: center; }
                            </style>
                        </head>
                        <body>
                            <div class='container'>
                                <div class='header'>
                                    <h1>Bedankt voor uw bestelling!</h1>
                                </div>
                                <div class='content'>
                                    <p>Beste {$customerName},</p>
                                    <p>{$statusMessage}</p>
                                    <p><strong>Bestelnummer:</strong> #{$order['id']}</p>
                                    <p><strong>Status:</strong> " . ($status === 'paid' ? 'Betaald' : 'In afwachting van betaling') . "</p>
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th style='text-align: center;'>Aantal</th>
                                                <th style='text-align: right;'>Prijs</th>
                                                <th style='text-align: right;'>Totaal</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {$itemsList}
                                        </tbody>
                                    </table>
                                    <div class='total'>
                                        <table style='width: 100%;'>
                                            <tr><td>Subtotaal:</td><td style='text-align: right;'>€{$formattedSubtotal}</td></tr>
                                            {$shippingRowHtml}
                                            <tr><td>BTW (21%):</td><td style='text-align: right;'>€{$formattedVat}</td></tr>
                                            <tr style='font-weight: bold;'><td>Totaalbedrag:</td><td style='text-align: right;'>€{$totalAmount}</td></tr>
                                        </table>
                                    </div>
                                    <p>Heeft u vragen over uw bestelling? Neem dan contact met ons <NAME_EMAIL>.</p>
                                </div>
                                <div class='footer'>
                                    <p>© " . date('Y') . " Jacqueline Tjassens. Alle rechten voorbehouden.</p>
                                </div>
                            </div>
                        </body>
                        </html>";
                        
                        // Get address data for owner email
                        $addressHtml = '';
                        if (isset($order['shipping_address']) && !empty($order['shipping_address'])) {
                            $addressData = json_decode($order['shipping_address'], true);
                            $addressHtml = "
                                <p><strong>Naam:</strong> " . htmlspecialchars($addressData['name'] ?? $customerName) . "</p>
                                <p><strong>Adres:</strong> " . htmlspecialchars($addressData['address'] ?? '') . "</p>
                                <p><strong>Postcode:</strong> " . htmlspecialchars($addressData['postal_code'] ?? '') . "</p>
                                <p><strong>Plaats:</strong> " . htmlspecialchars($addressData['city'] ?? '') . "</p>
                                <p><strong>Bezorgmethode:</strong> " . htmlspecialchars($deliveryMethod === 'pickup' ? 'Ophalen in Zoetermeer' : 'Verzenden') . "</p>";
                        } else {
                            $addressHtml = "<p>Geen adresgegevens beschikbaar</p>";
                        }
                        
                        // Owner email template
                        $ownerMessage = "<!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset='UTF-8'>
                            <title>Nieuwe bestelling #{$order['id']}</title>
                            <style>
                                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                                .container { max-width: 600px; margin: 0 auto; }
                                .header { background-color: #d5bc5a; padding: 20px; color: white; }
                                .content { padding: 20px; }
                                table { width: 100%; border-collapse: collapse; }
                                th { text-align: left; padding: 10px; background-color: #f2f2f2; }
                                .total { margin-top: 20px; background-color: #f9f9f9; padding: 15px; }
                                .address { margin-top: 20px;margin-bottom:20px; background-color: #f9f9f9; padding: 15px; }
                            </style>
                        </head>
                        <body>
                            <div class='container'>
                                <div class='header'>
                                    <h1>Nieuwe bestelling ontvangen!</h1>
                                </div>
                                <div class='content'>
                                    <p>Er is een nieuwe bestelling geplaatst:</p>
                                    <p><strong>Bestelnummer:</strong> #{$order['id']}</p>
                                    <p><strong>Klant:</strong> {$customerName}</p>
                                    <p><strong>Email:</strong> {$email}</p>

                                    <!-- Added address information -->
                                    <div class='address'>
                                        <h4>Verzendgegevens</h4>
                                        {$addressHtml}
                                    </div>
                                    
                                    <table>
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th style='text-align: center;'>Aantal</th>
                                                <th style='text-align: right;'>Prijs</th>
                                                <th style='text-align: right;'>Totaal</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {$itemsList}
                                        </tbody>
                                    </table>
                                    <div class='total'>
                                        <table style='width: 100%;'>
                                            <tr><td>Subtotaal:</td><td style='text-align: right;'>€{$formattedSubtotal}</td></tr>
                                            {$shippingRowHtml}
                                            <tr><td>BTW (21%):</td><td style='text-align: right;'>€{$formattedVat}</td></tr>
                                            <tr style='font-weight: bold;'><td>Totaalbedrag:</td><td style='text-align: right;'>€{$totalAmount}</td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </body>
                        </html>";

                        // Try sending customer email first
                        $customerSubject = "Bevestiging bestelling #{$order['id']} - Jacqueline Tjassens";
                        $customerAltBody = "Bedankt voor uw bestelling #{$order['id']}. Totaalbedrag: €{$totalAmount}";
                        
                        // Log before sending
                        logPaymentReturn("Sending customer email", [
                            'to' => $email,
                            'subject' => $customerSubject
                        ]);
                        
                        // Send customer email with explicit parameters
                        $customerEmailSent = sendEmail(
                            $customerSubject,
                            $customerMessage,
                            $customerAltBody,
                            $email,
                            $customerName
                        );
                        
                        // Log result
                        logPaymentReturn("Customer email result", [
                            'success' => $customerEmailSent ? 'yes' : 'no',
                            'to' => $email
                        ]);
                        
                        // Send to owner with explicit parameters
                        $ownerSubject = "Nieuwe bestelling #{$order['id']} - Jacqueline Tjassens";
                        $ownerAltBody = "Nieuwe bestelling #{$order['id']}. Totaalbedrag: €{$totalAmount}";
                        
                        // Log before sending
                        logPaymentReturn("Sending owner email", [
                            'to' => '<EMAIL>',
                            'subject' => $ownerSubject
                        ]);
                        
                        $ownerEmailSent = sendEmail(
                            $ownerSubject,
                            $ownerMessage,
                            $ownerAltBody,
                            '<EMAIL>',
                            'Jacqueline Tjassens'
                        );
                        
                        // Log result
                        logPaymentReturn("Owner email result", [
                            'success' => $ownerEmailSent ? 'yes' : 'no',
                            'to' => '<EMAIL>'
                        ]);
                        
                        // Log email sending results
                        logPaymentReturn("Order confirmation emails summary", [
                            'order_id' => $order['id'],
                            'customer_email_sent' => $customerEmailSent ? 'yes' : 'no',
                            'owner_email_sent' => $ownerEmailSent ? 'yes' : 'no',
                            'customer_email' => $email
                        ]);
                        
                        // Mark email as sent if at least one was successful
                        if ($customerEmailSent || $ownerEmailSent) {
                            $stmt = $pdo->prepare("UPDATE orders SET email_sent = 1 WHERE id = ?");
                            $stmt->execute([$order['id']]);
                            logPaymentReturn("Confirmation emails sent and marked in database", [
                                'order_id' => $order['id'],
                                'status' => $status
                            ]);
                        }
                    }
                } catch (Exception $e) {
                    logDetailedError($e, [
                        'function' => 'sendOrderConfirmationEmail', 
                        'order_id' => $order['id'],
                        'status' => $status
                    ]);
                    // Continue execution even if email sending fails
                }
            } else {
                logPaymentReturn("Email already sent for this order", [
                    'order_id' => $order['id'],
                    'status' => $status
                ]);
            }
            
            // Only clear shopping cart and set success message for paid orders
            if ($status === 'paid') {
                // Clear shopping cart
                clearShoppingCart();
                
                $_SESSION['success_message'] = "Uw betaling is succesvol verwerkt. Bedankt voor uw bestelling!";
                $_SESSION['completed_order_id'] = $order['id'];
            } else {
                $_SESSION['info_message'] = "Uw bestelling is ontvangen en wordt verwerkt zodra de betaling is voltooid.";
            }
            break;
            
        case 'failed':
            $_SESSION['error_message'] = "Uw betaling is niet gelukt. U kunt het opnieuw proberen of een andere betaalmethode kiezen.";
            break;
            
        case 'cancelled':
            $_SESSION['info_message'] = "Uw betaling is geannuleerd. U kunt het later opnieuw proberen.";
            break;
            
        default:
            $_SESSION['error_message'] = "Er is een probleem opgetreden met uw betaling. Neem contact op met onze klantenservice.";
    }
    
    // Get order items for display
    if ($order) {
        try {
            // First check if the image_url column exists in the products table
            $checkColumnSql = "SHOW COLUMNS FROM products LIKE 'image_url'";
            $checkColumnStmt = $pdo->query($checkColumnSql);
            $imageUrlColumnExists = $checkColumnStmt && $checkColumnStmt->rowCount() > 0;
            
            // Build the SQL query based on whether the column exists
            if ($imageUrlColumnExists) {
                $sql = "
                    SELECT oi.*, p.name as product_name, p.image_url 
                    FROM order_items oi
                    LEFT JOIN products p ON oi.product_id = p.id
                    WHERE oi.order_id = ?
                ";
            } else {
                // Use a simpler query without the image_url column
                $sql = "
                    SELECT oi.*, p.name as product_name
                    FROM order_items oi
                    LEFT JOIN products p ON oi.product_id = p.id
                    WHERE oi.order_id = ?
                ";
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$order['id']]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            logPaymentReturn("Order items retrieved", [
                'order_id' => $order['id'],
                'item_count' => count($items),
                'image_url_column_exists' => $imageUrlColumnExists ? 'yes' : 'no'
            ]);
        } catch (Exception $e) {
            logDetailedError($e, ['function' => 'getOrderItems', 'order_id' => $order['id']]);
            // Continue execution even if getting items fails
        }
    }
    
} catch (Exception $e) {
    logPaymentReturn("Payment return error", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'order_id' => $orderId ?? null
    ]);
    
    $_SESSION['error_message'] = "Er is een fout opgetreden bij het verwerken van uw betaling. Probeer het later opnieuw of neem contact op met onze klantenservice.";
}

// Set page title for header
$page_title = "Betaling Status";

// Include header and menu
include 'includes/header.php';
include 'includes/menu.php';

// Add breadcrumb
require_once 'includes/breadcrumb.php';
renderBreadcrumb($page_title,'shop-bg', []);
?>

<div class="container my-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">Betaling Status</h2>
                    
                    <?php if (isset($_SESSION['success_message'])): ?>
                        <div class="alert alert-success">
                            <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_SESSION['info_message'])): ?>
                        <div class="alert alert-info">
                            <?php echo $_SESSION['info_message']; unset($_SESSION['info_message']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_SESSION['error_message'])): ?>
                        <div class="alert alert-danger">
                            <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($processingErrors)): ?>
                        <div class="alert alert-warning">
                            <p>Er zijn enkele problemen opgetreden tijdens het verwerken van uw betaling:</p>
                            <ul>
                                <?php foreach ($processingErrors as $error): ?>
                                    <li><?php echo htmlspecialchars($error['message']); ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <p>Neem contact op met onze klantenservice als het probleem aanhoudt.</p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">Terug naar homepage</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Only show order details if we have a valid order
if (isset($order) && is_array($order) && !empty($order)): 
    // Format amounts
    $subtotal = number_format($order['subtotal_amount'] ?? 0, 2, ',', '.');
    $shipping = number_format($order['shipping_amount'] ?? 0, 2, ',', '.');
    $tax = number_format($order['tax_amount'] ?? 0, 2, ',', '.');
    $total = number_format($order['total_amount'] ?? 0, 2, ',', '.');
    $orderDate = isset($order['created_at']) ? date('d-m-Y H:i', strtotime($order['created_at'])) : 'Onbekend';
?>

<div class="container my-5">
    <div class="row">
        <div class="col-md-10 mx-auto">
            <div class="card">
                <div class="card-header bg-light">
                    <h3>Bestelling</h3>
                    <p class="mb-0">Datum: <?php echo $orderDate; ?></p>
                </div>
                <div class="card-body">
                    <h4 class="mb-3">Bestelde producten</h4>
                    
                    <?php if (!empty($items)): ?>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th style="width: 80px"></th>
                                    <th>Product</th>
                                    <th>Prijs</th>
                                    <th>Aantal</th>
                                    <th class="text-end">Totaal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($items as $item): 
                                    $itemPrice = number_format($item['price'], 2, ',', '.');
                                    $itemTotal = number_format($item['price'] * $item['quantity'], 2, ',', '.');
                                    $imageUrl = isset($item['image_url']) && $item['image_url'] ? 'https://www.jacquelinetjassens.com/' . $item['image_url'] : '';
                                ?>
                                <tr>
                                    <td>
                                        <?php if ($imageUrl): ?>
                                            <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="" class="img-thumbnail" style="max-width: 60px;">
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($item['product_name'] ?? 'Onbekend product'); ?></td>
                                    <td>€<?php echo $itemPrice; ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td class="text-end">€<?php echo $itemTotal; ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-info">
                        Geen productgegevens beschikbaar.
                    </div>
                    <?php endif; ?>
                    
                    <div class="row mt-4">
                        <div class="col-md-6 ms-auto">
                            <table class="table">
                                <tr>
                                    <td>Subtotaal:</td>
                                    <td class="text-end">€<?php echo number_format(($order['delivery_method'] === 'shipping') ? $order['total_amount'] - 4.95 : $order['total_amount'], 2, ',', '.'); ?></td>
                                </tr>
                                <?php if ($order['delivery_method'] !== 'pickup'): ?>
                                <tr>
                                    <td>Verzendkosten:</td>
                                    <td class="text-end">€4,95</td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td>BTW (21%):</td>
                                    <td class="text-end">€<?php 
                                        $subtotal = ($order['delivery_method'] === 'shipping') ? $order['total_amount'] - 4.95 : $order['total_amount'];
                                        echo number_format($subtotal * 0.21, 2, ',', '.'); 
                                    ?></td>
                                </tr>
                                <tr class="fw-bold">
                                    <td>Totaal:</td>
                                    <td class="text-end">€<?php echo $total; ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <?php 
                    // Display shipping address if available
                    $shipping = json_decode($order['shipping_address'] ?? '{}', true);
                    if (!empty($shipping)): 
                    ?>
                    <div class="mt-4">
                        <h4>Verzendadres</h4>
                        <address>
                            <?php echo htmlspecialchars($shipping['name'] ?? ''); ?><br>
                            <?php echo htmlspecialchars($shipping['address'] ?? ''); ?><br>
                            <?php echo htmlspecialchars($shipping['postal_code'] ?? ''); ?> 
                            <?php echo htmlspecialchars($shipping['city'] ?? ''); ?>
                        </address>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php
// Include footer
include 'includes/footer.php';
?>
