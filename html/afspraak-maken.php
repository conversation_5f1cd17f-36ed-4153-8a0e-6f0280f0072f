<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/breadcrumb.php';

// Set CSRF token for form security
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$page_title = "Afspraak maken";

$behandelingen = [
    ["id" => 1, "name" => "Abhyanga massage", "duration" => 90, "price" => 95.00],
    ["id" => 2, "name" => "Kruidenstempelmassage 60 min", "duration" => 60, "price" => 75.00],
    ["id" => 3, "name" => "Kruidenstempelmassage 90 min", "duration" => 90, "price" => 100.00],
    ["id" => 4, "name" => "Ontspanningsmassage 30 min", "duration" => 30, "price" => 45.00],
    ["id" => 5, "name" => "Ontspanningsmassage 60 min", "duration" => 60, "price" => 60.00],
    ["id" => 6, "name" => "Ontspanningsmassage 90 min", "duration" => 90, "price" => 90.00],
    ["id" => 7, "name" => "Stemvorktherapie", "duration" => 60, "price" => 55.00],
    ["id" => 8, "name" => "Tienermassage", "duration" => 60, "price" => 45.00],
    ["id" => 9, "name" => "Go4Balance Consult", "duration" => 60, "price" => 45.00],
    ["id" => 10, "name" => "Indian Foot Massage", "duration" => 50, "price" => 55.00]
];

$workshops = [
    ["id" => 10, "name" => "Mild in het wild", "duration" => 120, "price" => 45.00],
    ["id" => 11, "name" => "Uit je hoofd in je lijf", "duration" => 120, "price" => 39.95]
];

$welkomstconsult = [
    ["id" => 12, "name" => "Shiro Abhyanga (Welkomstconsult)", "duration" => 30, "price" => 18.00],
    ["id" => 13, "name" => "Pad Abhyanga (Welkomstconsult)", "duration" => 30, "price" => 18.00]
];

// Calculate date ranges for the booking form
$earliest_date = date('Y-m-d', strtotime('+1 day'));
$furthest_date = date('Y-m-d', strtotime('+3 months'));

include 'includes/header.php';
include 'includes/menu.php';

renderBreadcrumb(
    $page_title,
    'booking-bg',
    []
); 
?>

<!-- Add this right after the opening of the main content div, before the booking form -->
<?php if (isset($_SESSION['booking_status']) && isset($_SESSION['booking_message'])): ?>
    <div id="booking-confirmation" class="alert alert-<?php echo $_SESSION['booking_status'] === 'success' ? 'success' : 'danger'; ?> mb-4">
        <?php 
        echo htmlspecialchars($_SESSION['booking_message']); 
        // Clear the message after displaying
        unset($_SESSION['booking_status']);
        unset($_SESSION['booking_message']);
        ?>
    </div>
    
    <?php if (isset($_SESSION['booking_status']) && $_SESSION['booking_status'] === 'success'): ?>
    <script>
        // Scroll to confirmation message
        document.addEventListener('DOMContentLoaded', function() {
            const confirmation = document.getElementById('booking-confirmation');
            if (confirmation) {
                confirmation.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    </script>
    <?php endif; ?>
<?php endif; ?>

<!-- booking wizard start -->
<div class="pa-booking spacer-top spacer-bottom">
    <div class="container">
        <div class="row">
            <div class="col-md-8">
                <!-- Wizard Progress -->
                <div class="wizard-progress mb-4">
                    <div class="step active" data-step="1">
                        <div class="step-icon">1</div>
                        <div class="step-label">Dienst</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-icon">2</div>
                        <div class="step-label">Datum & Tijd</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-icon">3</div>
                        <div class="step-label">Gegevens</div>
                    </div>
                    <div class="step" data-step="4">
                        <div class="step-icon">4</div>
                        <div class="step-label">Betaling</div>
                    </div>
                    <div class="progress-bar"></div>
                </div>

                <!-- Booking Form -->
                <form id="bookingForm" method="post" action="includes/process_appointment_payment.php" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <input type="hidden" name="action" value="process_appointment_payment">
                    
                    <div class="wizard-container">
                        <!-- Step 1: Service Selection -->
                        <div id="step1" class="wizard-step active">
                            <h4 class="mb-4">Kies een behandeling of workshop</h4>
                            <div class="form-group mb-4">
                                <label class="form-label" for="serviceType">Type dienst <span class="text-danger">*</span></label>
                                <select name="service_type" id="serviceType" class="form-select" required>
                                    <option value="">Selecteer type</option>
                                    <option value="behandeling">Behandeling</option>
                                    <option value="workshop">Workshop</option>
                                    <option value="welkomstconsult">Welkomstconsult</option>
                                </select>
                                <div class="invalid-feedback">Selecteer een type dienst</div>
                            </div>

                            <div class="form-group mb-4" id="behandelingenSelect" style="display:none;">
                                <label class="form-label" for="behandeling_id">Behandeling <span class="text-danger">*</span></label>
                                <select name="behandeling_id" id="behandeling_id" class="form-select">
                                    <option value="">Selecteer behandeling</option>
                                    <?php foreach($behandelingen as $behandeling): ?>
                                    <option value="<?php echo htmlspecialchars($behandeling['id']); ?>" 
                                            data-duration="<?php echo htmlspecialchars($behandeling['duration']); ?>"
                                            data-price="<?php echo htmlspecialchars(number_format($behandeling['price'], 2, ',', '.')); ?>">
                                        <?php echo htmlspecialchars($behandeling['name']); ?> (€<?php echo htmlspecialchars(number_format($behandeling['price'], 2, ',', '.')); ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Selecteer een behandeling</div>
                            </div>

                            <div class="form-group mb-4" id="workshopsSelect" style="display:none;">
                                <label class="form-label" for="workshop_id">Workshop <span class="text-danger">*</span></label>
                                <select name="workshop_id" id="workshop_id" class="form-select">
                                    <option value="">Selecteer workshop</option>
                                    <?php foreach($workshops as $workshop): ?>
                                    <option value="<?php echo htmlspecialchars($workshop['id']); ?>"
                                            data-duration="<?php echo htmlspecialchars($workshop['duration']); ?>"
                                            data-price="<?php echo htmlspecialchars(number_format($workshop['price'], 2, ',', '.')); ?>">
                                        <?php echo htmlspecialchars($workshop['name']); ?> (€<?php echo htmlspecialchars(number_format($workshop['price'], 2, ',', '.')); ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Selecteer een workshop</div>
                            </div>

                            <div class="form-group mb-4" id="welkomstconsultSelect" style="display:none;">
                                <label class="form-label" for="welkomstconsult_id">Welkomstconsult <span class="text-danger">*</span></label>
                                <select name="welkomstconsult_id" id="welkomstconsult_id" class="form-select">
                                    <option value="">Selecteer welkomstconsult</option>
                                    <?php foreach($welkomstconsult as $consult): ?>
                                    <option value="<?php echo htmlspecialchars($consult['id']); ?>"
                                            data-duration="<?php echo htmlspecialchars($consult['duration']); ?>"
                                            data-price="<?php echo htmlspecialchars(number_format($consult['price'], 2, ',', '.')); ?>">
                                        <?php echo htmlspecialchars($consult['name']); ?> (€<?php echo htmlspecialchars(number_format($consult['price'], 2, ',', '.')); ?>)
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">Selecteer een welkomstconsult</div>
                            </div>

                            <div class="wizard-buttons">
                                <div></div>
                                <button type="button" class="pa-btn next-step" data-step="1" id="step1Next">Volgende</button>
                            </div>
                        </div>

                        <!-- Step 2: Date and Time Selection -->
                        <div id="step2" class="wizard-step">
                            <h4 class="mb-4">Kies datum en tijd</h4>
                            <div class="form-group mb-4">
                                <label class="form-label" for="appointmentDate">Datum <span class="text-danger">*</span></label>
                                <input type="date" name="appointment_date" id="appointmentDate" class="form-control" 
                                       min="<?php echo $earliest_date; ?>" 
                                       max="<?php echo $furthest_date; ?>" required>
                                <div class="invalid-feedback">Selecteer een geldige datum</div>
                                <small class="form-text text-muted">Afspraken zijn mogelijk van <?php echo date('d-m-Y', strtotime($earliest_date)); ?> tot <?php echo date('d-m-Y', strtotime($furthest_date)); ?></small>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="form-label" for="appointmentTime">Tijd <span class="text-danger">*</span></label>
                                <select name="appointment_time" id="appointmentTime" class="form-select" required>
                                    <option value="">Selecteer eerst een datum</option>
                                </select>
                                <div class="invalid-feedback">Selecteer een tijd</div>
                                <small class="form-text text-muted">Beschikbare tijden worden getoond na het selecteren van een datum</small>
                            </div>

                            <div class="wizard-buttons">
                                <button type="button" class="pa-btn prev-step" data-step="2">Vorige</button>
                                <button type="button" class="pa-btn next-step" data-step="2" id="step2Next">Volgende</button>
                            </div>
                        </div>

                        <!-- Step 3: Personal Information -->
                        <div id="step3" class="wizard-step">
                            <h4 class="mb-4">Vul uw gegevens in</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="client_name">Naam <span class="text-danger">*</span></label>
                                        <input type="text" name="client_name" id="client_name" class="form-control" required>
                                        <div class="invalid-feedback">Vul uw naam in</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group mb-3">
                                        <label class="form-label" for="client_email">Email <span class="text-danger">*</span></label>
                                        <input type="email" name="client_email" id="client_email" class="form-control" required>
                                        <div class="invalid-feedback">Vul een geldig e-mailadres in</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-3">
                                <label class="form-label" for="client_phone">Telefoon <span class="text-danger">*</span></label>
                                <input type="tel" name="client_phone" id="client_phone" class="form-control" 
                                       pattern="^(0|\+31|0031)[1-9][0-9]{8}$" required>
                                <div class="invalid-feedback">Vul een geldig telefoonnummer in (bijv. 0612345678 of +31612345678)</div>
                            </div>
                            
                            <div class="form-group mb-4">
                                <label class="form-label" for="notes">Opmerkingen</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3" maxlength="500"></textarea>
                                <small class="form-text text-muted">Maximaal 500 tekens</small>
                                <div class="text-end mt-1">
                                    <span id="charCount">0</span>/500
                                </div>
                            </div>
                            
                            <div class="form-group mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" required>
                                    <label class="form-check-label" for="privacyConsent">
                                        Ik ga akkoord met de <a href="privacy-policy.php" target="_blank">privacyverklaring</a> <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">U moet akkoord gaan met de privacyverklaring</div>
                                </div>
                            </div>

                            <div class="wizard-buttons">
                                <button type="button" class="pa-btn prev-step" data-step="3">Vorige</button>
                                <button type="button" class="pa-btn next-step" data-step="3" id="step3Next">Volgende</button>
                            </div>
                        </div>

                        <!-- Step 4: Payment Options -->
                        <div id="step4" class="wizard-step">
                            <h4 class="mb-4">Betaling</h4>
                            
                            <div class="payment-method-selection mb-4">
                                <h5>Kies uw betaalmethode</h5>
                                <div class="form-group">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="paymentOnline" value="online" checked>
                                        <label class="form-check-label" for="paymentOnline">
                                            <strong>Online betalen</strong>
                                            <small class="d-block text-muted">Betaal direct online via iDEAL, creditcard of andere betaalmethoden</small>
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="radio" name="payment_method" id="paymentAtAppointment" value="at_appointment">
                                        <label class="form-check-label" for="paymentAtAppointment">
                                            <strong>Betalen bij afspraak</strong>
                                            <small class="d-block text-muted">Betaal contant of per pin tijdens uw bezoek</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="payment-summary mb-4">
                                <h5>Overzicht</h5>
                                <div class="card p-3">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Dienst:</span>
                                        <span id="service-name-display">-</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Datum:</span>
                                        <span id="appointment-date-display">-</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Tijd:</span>
                                        <span id="appointment-time-display">-</span>
                                    </div>
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Prijs:</span>
                                        <span id="service-price-display">-</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between">
                                        <strong>Totaal</strong>
                                        <strong id="total-price-display">-</strong>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group mb-4" id="onlinePaymentConsent">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="paymentConsent" name="payment_consent">
                                    <label class="form-check-label" for="paymentConsent">
                                        Ik ga akkoord met de betaling en de <a href="algemene-voorwaarden.php" target="_blank">algemene voorwaarden</a> <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">U moet akkoord gaan met de betaling</div>
                                </div>
                            </div>

                            <div class="form-group mb-4" id="appointmentPaymentConsent" style="display:none;">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="appointmentConsent" name="appointment_consent">
                                    <label class="form-check-label" for="appointmentConsent">
                                        Ik ga akkoord met de <a href="algemene-voorwaarden.php" target="_blank">algemene voorwaarden</a> en begrijp dat betaling plaatsvindt tijdens mijn bezoek <span class="text-danger">*</span>
                                    </label>
                                    <div class="invalid-feedback">U moet akkoord gaan met de voorwaarden</div>
                                </div>
                            </div>

                            <div class="wizard-buttons">
                                <button type="button" class="pa-btn prev-step" data-step="4">Vorige</button>
                                <button type="submit" class="pa-btn submit-booking">
                                    <span id="submitButtonText">Betalen</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-md-4">
                <div class="sticky-cards-container">
                    <div class="booking-summary card">
                        <div class="card-header">
                            <h3>Uw afspraak</h3>
                        </div>
                        <div class="card-body" id="bookingSummary">
                            <div class="alert alert-info">Maak uw selectie om de samenvatting te zien.</div>
                        </div>
                    </div>
                    
                    <div class="information-card">
                        <div class="card-header">
                            <h3>Informatie</h3>
                        </div>
                        <div class="card-body">
                            <p>Na het maken van de afspraak ontvangt u een bevestiging per e-mail. U kunt uw afspraak tot 24 uur van tevoren kosteloos annuleren.</p>
                            <p>Heeft u vragen? Neem dan contact met ons op:</p>
                            <p><i class="fa fa-phone"></i> <a href="tel:+31612345678">06-41412241</a></p>
                            <p><i class="fa fa-envelope"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
