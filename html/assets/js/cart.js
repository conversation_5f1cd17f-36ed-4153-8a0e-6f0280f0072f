/**
 * E-commerce Shop JavaScript
 * 
 * Handles product filtering, cart operations, and UI interactions
 * 
 * @version 1.2
 */

// Utility functions
const shopUtils = {
    /**
     * Create and show a notification
     * @param {string} message - Message to display
     * @param {string} type - Notification type ('success', 'error', 'info')
     * @param {number} duration - Duration in ms
     */
    showNotification: function(message, type = 'success', duration = 3000) {
      const notification = document.createElement('div');
      notification.className = `cart-notification ${type}`;
      notification.textContent = message;
      
      // Set styles based on notification type
      const bgColor = type === 'error' ? '#e74c3c' : (type === 'info' ? '#3498db' : '#d5bc5a');
      
      Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '15px 25px',
        backgroundColor: bgColor,
        color: 'white',
        borderRadius: '4px',
        zIndex: '1000',
        boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
        opacity: '0',
        transition: 'opacity 0.3s ease'
      });
      
      document.body.appendChild(notification);
      
      // Fade in
      setTimeout(() => {
        notification.style.opacity = '1';
      }, 10);
      
      // Fade out and remove
      setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
      }, duration);
    },
    
    /**
     * Update cart count in all cart indicators
     * @param {number} count - New cart count
     */
    updateCartCount: function(count) {
      const cartCountElements = document.querySelectorAll('.cart-count');
      cartCountElements.forEach(element => {
        element.textContent = count;
        element.style.display = count > 0 ? 'block' : 'none';
      });
    },
    
    /**
     * Update cart totals on the cart page
     * @param {Object} totals - Object containing total values
     */
    updateCartTotals: function(totals) {
      if (!totals) return;
      
      const elements = {
        subtotal: document.querySelector('[data-subtotal]'),
        shipping: document.querySelector('[data-shipping]'),
        btw: document.querySelector('[data-btw]'),
        total: document.querySelector('[data-total]')
      };
      
      for (const [key, element] of Object.entries(elements)) {
        if (element && totals[key]) {
          element.textContent = '€' + totals[key];
        }
      }
    },
    
    /**
     * Make API request to cart handler
     * @param {string} action - Action to perform
     * @param {Object} data - Additional data to send
     * @returns {Promise} - Fetch promise
     */
    cartRequest: function(action, data = {}) {
      const formData = new FormData();
      formData.append('action', action);
      
      // Add all data properties to FormData
      Object.entries(data).forEach(([key, value]) => {
        formData.append(key, value);
      });
      
      // Use absolute path to cart_handler.php instead of relative path
      return fetch('/includes/cart_handler.php', {
        method: 'POST',
        body: formData
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
      });
    }
  };
  
  // Category filter functionality
  function initCategoryFilter() {
    // Get the category from URL
    const urlParams = new URLSearchParams(window.location.search);
    const category = urlParams.get("category");
    
    if (!category) return;
    
    // Filter products by category
    const productContainers = document.querySelectorAll(".col-sm-6");
    let hasMatchingProducts = false;
    
    productContainers.forEach(container => {
      const productCategory = container.getAttribute("data-category");
      if (productCategory === category) {
        container.style.display = "block";
        hasMatchingProducts = true;
      } else {
        container.style.display = "none";
      }
    });
    
    // If no products match, show all products
    if (!hasMatchingProducts) {
      productContainers.forEach(container => {
        container.style.display = "block";
      });
      
      // Show notification that no products match
      shopUtils.showNotification('No products found in this category', 'info');
    }
    
    // Highlight active category and add remove button
    const categoryLinks = document.querySelectorAll(".pa-shop-category ul li a");
    categoryLinks.forEach(link => {
      const linkCategory = new URL(link.href).searchParams.get("category");
      
      if (linkCategory === category) {
        // Add active class
        link.classList.add("active");
        
        // Add clear filter button if not already present
        if (!link.parentNode.querySelector(".clear-filter")) {
          const clearButton = document.createElement("button");
          clearButton.innerHTML = "&#10006;"; // ✖ symbol
          clearButton.classList.add("clear-filter");
          clearButton.setAttribute('title', 'Clear filter');
          clearButton.setAttribute('aria-label', 'Clear category filter');
          
          Object.assign(clearButton.style, {
            background: "transparent",
            border: "none",
            color: "red",
            fontSize: "16px",
            cursor: "pointer",
            marginLeft: "5px",
            transition: "transform 0.2s ease"
          });
          
          // Add hover effect
          clearButton.addEventListener('mouseover', () => {
            clearButton.style.transform = 'scale(1.2)';
          });
          
          clearButton.addEventListener('mouseout', () => {
            clearButton.style.transform = 'scale(1)';
          });
          
          // Clear filter on click
          clearButton.addEventListener("click", function(event) {
            event.preventDefault();
            event.stopPropagation();
            window.location.href = window.location.pathname;
          });
          
          // Insert button after count span or append to link
          const countSpan = link.querySelector("span");
          if (countSpan) {
            countSpan.insertAdjacentElement("afterend", clearButton);
          } else {
            link.appendChild(clearButton);
          }
        }
      }
    });
  }
  
  // Add to cart functionality
  function initAddToCart() {
    document.querySelectorAll('.add-to-cart').forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        
        const productId = this.getAttribute('data-product-id');
        
        if (!productId) {
          console.error('No product ID found');
          shopUtils.showNotification('Product ID not found', 'error');
          return;
        }
        
        // Store original button content
        const originalContent = this.innerHTML;
        const originalDisabledState = this.disabled;
        
        // Add loading state
        this.disabled = true;
        this.innerHTML = '<span class="spinner"></span> Adding...';
        
        shopUtils.cartRequest('add', { product_id: productId })
          .then(data => {
            if (data.success) {
              // Update cart count
              shopUtils.updateCartCount(data.count);
              
              // Show success message
              shopUtils.showNotification('Product toegevoegd aan winkelwagen');
            } else {
              console.error('Failed to add product:', data.message);
              shopUtils.showNotification(data.message || 'Product toevoegen mislukt', 'error');
            }
          })
          .catch(error => {
            console.error('Error:', error);
            shopUtils.showNotification('Product toevoegen mislukt', 'error');
          })
          .finally(() => {
            // Restore button to original state
            this.disabled = originalDisabledState;
            this.innerHTML = originalContent;
          });
      });
    });
  }
  
  // Remove from cart functionality
  function initRemoveFromCart() {
    document.querySelectorAll('.remove-item').forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        
        const productId = this.getAttribute('data-id');
        const cartRow = this.closest('tr');
        
        if (!productId) {
          console.error('No product ID found');
          return;
        }
        
        // Add loading state
        this.disabled = true;
        const originalHTML = this.innerHTML;
        this.innerHTML = '<span class="spinner"></span>';
        
        shopUtils.cartRequest('remove', { product_id: productId })
          .then(data => {
            if (data.success) {
              // Fade out and remove the row
              cartRow.style.transition = 'opacity 0.3s ease';
              cartRow.style.opacity = '0';
              
              setTimeout(() => {
                cartRow.remove();
                
                // Update cart count
                shopUtils.updateCartCount(data.count);
                
                // Update totals
                shopUtils.updateCartTotals(data.totals);
                
                // If cart is empty, refresh the page
                if (data.count === 0) {
                  window.location.reload();
                  return;
                }
                
                // Show success notification
                shopUtils.showNotification('Product removed from cart');
              }, 300);
            } else {
              console.error('Failed to remove product:', data.message);
              shopUtils.showNotification(data.message || 'Failed to remove product', 'error');
              
              // Restore button state
              this.disabled = false;
              this.innerHTML = originalHTML;
            }
          })
          .catch(error => {
            console.error('Error:', error);
            shopUtils.showNotification('Error removing product from cart', 'error');
            
            // Restore button state
            this.disabled = false;
            this.innerHTML = originalHTML;
          });
      });
    });
  }
  
  // Quantity update functionality
  function initQuantityControls() {
    document.querySelectorAll('.pa-cart-quantity').forEach(container => {
      const input = container.querySelector('input');
      const addBtn = container.querySelector('.pa-add');
      const subBtn = container.querySelector('.pa-sub');
      
      if (!input || !addBtn || !subBtn) return;
      
      // Store initial value
      input.defaultValue = input.value;
      
      // Initialize subtract button state
      const updateSubButtonState = () => {
        const currentValue = parseInt(input.value);
        subBtn.disabled = currentValue <= 1;
        subBtn.style.opacity = currentValue <= 1 ? '0.5' : '1';
      };
      
      updateSubButtonState();
      
      // Function to update quantity on server
      const updateQuantity = debounce((newQuantity) => {
        const productId = container.closest('tr').querySelector('.remove-item').getAttribute('data-id');
        
        if (!productId) {
          console.error('No product ID found');
          return;
        }
        
        // Show loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'quantity-loading';
        loadingIndicator.innerHTML = '<div class="spinner-small"></div>';
        container.appendChild(loadingIndicator);
        
        shopUtils.cartRequest('update_quantity', {
          product_id: productId,
          quantity: newQuantity
        })
          .then(data => {
            if (data.success) {
              // Update item total
              const totalCell = container.closest('tr').querySelector('td:nth-last-child(2)');
              if (totalCell) {
                totalCell.textContent = '€' + data.itemTotal;
              }
              
              // Update cart totals
              shopUtils.updateCartTotals(data.totals);
              
              // Update cart count
              shopUtils.updateCartCount(data.count);
              
              // Update input value
              input.value = newQuantity;
              input.defaultValue = newQuantity;
              
              // Update subtract button state
              updateSubButtonState();
            } else {
              console.error('Failed to update quantity:', data.message);
              shopUtils.showNotification(data.message || 'Failed to update quantity', 'error');
              
              // Revert the input value
              input.value = input.defaultValue;
            }
          })
          .catch(error => {
            console.error('Error:', error);
            shopUtils.showNotification('Error updating quantity', 'error');
            
            // Revert the input value
            input.value = input.defaultValue;
          })
          .finally(() => {
            // Remove loading indicator
            container.removeChild(loadingIndicator);
          });
      }, 500);
      
      // Handle plus button
      addBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const newValue = parseInt(input.value) + 1;
        input.value = newValue;
        updateQuantity(newValue);
      });
      
      // Handle minus button
      subBtn.addEventListener('click', (e) => {
        e.preventDefault();
        const currentValue = parseInt(input.value);
        if (currentValue > 1) {
          const newValue = currentValue - 1;
          input.value = newValue;
          updateQuantity(newValue);
        }
      });
      
      // Handle manual input
      input.addEventListener('change', () => {
        let newValue = parseInt(input.value);
        if (isNaN(newValue) || newValue < 1) {
          newValue = 1;
        }
        input.value = newValue;
        updateQuantity(newValue);
      });
    });
  }
  
  // Debounce function to limit how often a function can be called
  function debounce(func, wait) {
    let timeout;
    return function(...args) {
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), wait);
    };
  }
  
  // Initialize all functionality when DOM is loaded
  document.addEventListener("DOMContentLoaded", function() {
    // Add CSS for spinners
    const style = document.createElement('style');
    style.textContent = `
      .spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
        margin-right: 5px;
        vertical-align: middle;
      }
      
      .spinner-small {
        width: 12px;
        height: 12px;
        border-width: 1px;
      }
      
      .quantity-loading {
        position: absolute;
        top: 50%;
        right: -20px;
        transform: translateY(-50%);
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .pa-cart-quantity {
        position: relative;
      }
    `;
    document.head.appendChild(style);
    
    // Initialize all functionalities
    initCategoryFilter();
    initAddToCart();
    initRemoveFromCart();
    initQuantityControls();
  });
