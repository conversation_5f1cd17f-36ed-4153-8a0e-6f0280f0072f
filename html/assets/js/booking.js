/**
 * Booking System JavaScript
 * Handles the booking wizard functionality, form validation, and dynamic updates
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const serviceTypeSelect = document.getElementById('serviceType');
    const behandelingenSelect = document.getElementById('behandelingenSelect');
    const workshopsSelect = document.getElementById('workshopsSelect');
    const bookingSummary = document.getElementById('bookingSummary');
    const bookingForm = document.getElementById('bookingForm');
    
    // Only proceed with booking functionality if we're on a booking page
    if (!serviceTypeSelect || !bookingSummary) {
        return; // Exit if we're not on the booking page
    }
    
    // Parse URL parameters for pre-selection
    const urlParams = new URLSearchParams(window.location.search);
    const serviceType = urlParams.get('service_type');
    const behandelingId = urlParams.get('behandeling_id');
    const workshopId = urlParams.get('workshop_id');

    // Initialize form validation state
    let formValidated = false;

    /**
     * Initialize the booking form
     */
    function initBookingForm() {
        // Set minimum date to tomorrow
        const appointmentDate = document.getElementById('appointmentDate');
        if (appointmentDate) {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            appointmentDate.min = tomorrow.toISOString().split('T')[0];
        }

        // Apply URL parameters if present
        if (serviceType) {
            serviceTypeSelect.value = serviceType;
            serviceTypeSelect.dispatchEvent(new Event('change'));
            
            // Small delay to ensure the dropdowns are visible before selection
            setTimeout(function() {
                if (serviceType === 'behandeling' && behandelingId) {
                    const behandelingSelect = document.querySelector('select[name="behandeling_id"]');
                    if (behandelingSelect) {
                        behandelingSelect.value = behandelingId;
                        behandelingSelect.dispatchEvent(new Event('change'));
                    }
                } else if (serviceType === 'workshop' && workshopId) {
                    const workshopSelect = document.querySelector('select[name="workshop_id"]');
                    if (workshopSelect) {
                        workshopSelect.value = workshopId;
                        workshopSelect.dispatchEvent(new Event('change'));
                    }
                }
                updateSummary();
            }, 100);
        }

        // Initialize wizard to first step
        showStep(1);
        
        // Set up event listeners
        setupEventListeners();
    }

    /**
     * Set up all event listeners for the booking form
     */
    function setupEventListeners() {
        // Service type selection handler
        serviceTypeSelect.addEventListener('change', function() {
            // Hide both service selectors first
            behandelingenSelect.style.display = 'none';
            workshopsSelect.style.display = 'none';
            
            // Show the relevant selector based on selection
            if (this.value === 'behandeling') {
                behandelingenSelect.style.display = 'block';
                document.querySelector('select[name="behandeling_id"]').setAttribute('required', 'required');
                document.querySelector('select[name="workshop_id"]').removeAttribute('required');
                document.querySelector('select[name="workshop_id"]').value = '';
            } else if (this.value === 'workshop') {
                workshopsSelect.style.display = 'block';
                document.querySelector('select[name="workshop_id"]').setAttribute('required', 'required');
                document.querySelector('select[name="behandeling_id"]').removeAttribute('required');
                document.querySelector('select[name="behandeling_id"]').value = '';
            } else {
                // If no service type is selected, remove required attributes
                document.querySelector('select[name="behandeling_id"]').removeAttribute('required');
                document.querySelector('select[name="workshop_id"]').removeAttribute('required');
            }
            
            updateSummary();
            updatePaymentOverview(); // Update payment overview when service type changes
        });

        // Service selection handlers
        const behandelingSelect = document.querySelector('select[name="behandeling_id"]');
        const workshopSelect = document.querySelector('select[name="workshop_id"]');
        const welkomstconsultSelect = document.querySelector('select[name="welkomstconsult_id"]');
        
        if (behandelingSelect) {
            behandelingSelect.addEventListener('change', function() {
                updateSummary();
                updateTimeSlots();
                updatePaymentOverview(); // Update payment overview when behandeling changes
            });
        }

        if (workshopSelect) {
            workshopSelect.addEventListener('change', function() {
                updateSummary();
                updateTimeSlots();
                updatePaymentOverview(); // Update payment overview when workshop changes
            });
        }

        if (welkomstconsultSelect) {
            welkomstconsultSelect.addEventListener('change', function() {
                updateSummary();
                updateTimeSlots();
                updatePaymentOverview(); // Update payment overview when welkomstconsult changes
            });
        }

        // Date and time handlers
        const appointmentDate = document.getElementById('appointmentDate');
        const appointmentTime = document.getElementById('appointmentTime');
        
        if (appointmentDate) {
            appointmentDate.addEventListener('change', function() {
                updateTimeSlots();
                updateSummary();
                updatePaymentOverview(); // Update payment overview when date changes
            });
        }
        
        if (appointmentTime) {
            appointmentTime.addEventListener('change', function() {
                updateSummary();
                updatePaymentOverview(); // Update payment overview when time changes
            });
        }

        // Navigation event listeners
        document.querySelectorAll('.next-step').forEach(button => {
            button.addEventListener('click', function() {
                const currentStep = parseInt(this.getAttribute('data-step'));
                if (validateStep(currentStep)) {
                    showStep(currentStep + 1);
                    
                    // Always update payment overview when moving to next step
                    updatePaymentOverview();
                }
            });
        });

        document.querySelectorAll('.prev-step').forEach(button => {
            button.addEventListener('click', function() {
                const currentStep = parseInt(this.getAttribute('data-step'));
                showStep(currentStep - 1);
            });
        });

        // Payment method change handler
        const paymentOnline = document.getElementById('paymentOnline');
        const paymentAtAppointment = document.getElementById('paymentAtAppointment');
        const onlinePaymentConsent = document.getElementById('onlinePaymentConsent');
        const appointmentPaymentConsent = document.getElementById('appointmentPaymentConsent');
        const submitButtonText = document.getElementById('submitButtonText');
        
        if (paymentOnline && paymentAtAppointment) {
            paymentOnline.addEventListener('change', function() {
                if (this.checked) {
                    onlinePaymentConsent.style.display = 'block';
                    appointmentPaymentConsent.style.display = 'none';
                    submitButtonText.textContent = 'Betalen';
                    
                    // Clear appointment consent
                    document.getElementById('appointmentConsent').checked = false;
                }
            });
            
            paymentAtAppointment.addEventListener('change', function() {
                if (this.checked) {
                    onlinePaymentConsent.style.display = 'none';
                    appointmentPaymentConsent.style.display = 'block';
                    submitButtonText.textContent = 'Afspraak bevestigen';
                    
                    // Clear payment consent
                    document.getElementById('paymentConsent').checked = false;
                }
            });
        }

        // Form submission
        if (bookingForm) {
            bookingForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!validateAllSteps()) {
                    return false;
                }
                
                // Check payment method and validate appropriate consent
                const paymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
                
                if (paymentMethod === 'online') {
                    const paymentConsent = document.getElementById('paymentConsent');
                    if (!paymentConsent.checked) {
                        paymentConsent.classList.add('is-invalid');
                        return false;
                    }
                } else if (paymentMethod === 'at_appointment') {
                    const appointmentConsent = document.getElementById('appointmentConsent');
                    if (!appointmentConsent.checked) {
                        appointmentConsent.classList.add('is-invalid');
                        return false;
                    }
                }
                
                // Disable submit button to prevent double submissions
                const submitButton = document.querySelector('.submit-booking');
                if (submitButton) {
                    submitButton.disabled = true;
                    if (paymentMethod === 'online') {
                        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Verwerken...';
                    } else {
                        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Bevestigen...';
                    }
                }
                
                // Determine form action based on payment method
                let formAction;
                if (paymentMethod === 'online') {
                    formAction = 'includes/process_appointment_payment.php';
                } else {
                    formAction = 'includes/process-booking.php';
                }
                
                console.log('Payment method:', paymentMethod, 'Form action:', formAction);
                
                // Submit form via AJAX
                const formData = new FormData(bookingForm);
                
                fetch(formAction, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Response:', data);
                    if (data.success) {
                        if (paymentMethod === 'online' && data.redirectUrl) {
                            // Redirect to payment provider
                            window.location.href = data.redirectUrl;
                        } else {
                            // Show success message and redirect to confirmation
                            alert(data.message || 'Uw afspraak is succesvol geboekt!');
                            if (data.redirectUrl) {
                                window.location.href = data.redirectUrl;
                            } else {
                                window.location.href = 'index.php';
                            }
                        }
                    } else {
                        // Show error message
                        alert('Er is een fout opgetreden: ' + data.message);
                        if (submitButton) {
                            submitButton.disabled = false;
                            submitButton.innerHTML = paymentMethod === 'online' ? 'Betalen' : 'Afspraak bevestigen';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Er is een fout opgetreden. Probeer het later opnieuw.');
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.innerHTML = paymentMethod === 'online' ? 'Betalen' : 'Afspraak bevestigen';
                    }
                });
                
                return false;
            });
        }

        // Input validation event listeners
        document.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('input', function() {
                if (formValidated) {
                    validateInput(this);
                }
            });
            
            input.addEventListener('blur', function() {
                validateInput(this);
            });
        });
        
        // Radio button selection for services
        document.querySelectorAll('input[name="behandeling_id"], input[name="workshop_id"]').forEach(radio => {
            radio.addEventListener('change', function() {
                updateSummary();
                updateTimeSlots();
                updatePaymentOverview(); // Update payment overview when radio selection changes
                console.log('Radio changed:', this.dataset.name, this.dataset.price);
            });
        });
    }

    /**
     * Show a specific step in the wizard
     * @param {number} stepNumber - The step number to show
     */
    function showStep(stepNumber) {
        console.log('Showing step:', stepNumber);
        
        // Hide all steps
        document.querySelectorAll('.wizard-step').forEach(step => {
            step.classList.remove('active');
        });
        
        // Show current step
        const currentStep = document.getElementById(`step${stepNumber}`);
        if (currentStep) {
            currentStep.classList.add('active');
            
            // If this is the payment step, update the payment overview
            if (stepNumber === 4) {
                console.log('Payment step detected, updating payment overview');
                updatePaymentOverview();
            }
        }
        
        // Update progress indicators
        document.querySelectorAll('.wizard-progress .step').forEach((step, index) => {
            if (index + 1 < stepNumber) {
                step.classList.add('completed');
                step.classList.remove('active');
            } else if (index + 1 === stepNumber) {
                step.classList.add('active');
                step.classList.remove('completed');
            } else {
                step.classList.remove('active', 'completed');
            }
        });

        // Scroll to top of form for better UX
        if (currentStep) {
            currentStep.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    /**
     * Validate a single form input
     * @param {HTMLElement} input - The input element to validate
     * @returns {boolean} - Whether the input is valid
     */
    function validateInput(input) {
        if (!input.hasAttribute('required') && !input.value) {
            input.classList.remove('is-invalid');
            return true;
        }
        
        // Special handling for checkboxes
        if (input.type === 'checkbox' && input.hasAttribute('required')) {
            if (!input.checked) {
                input.classList.add('is-invalid');
                return false;
            } else {
                input.classList.remove('is-invalid');
                return true;
            }
        }
        
        if (input.hasAttribute('required') && !input.value) {
            input.classList.add('is-invalid');
            return false;
        }
        
        // Email validation
        if (input.type === 'email' && input.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(input.value)) {
                input.classList.add('is-invalid');
                return false;
            }
        }
        
        // Phone validation
        if (input.type === 'tel' && input.value) {
            // Dutch phone number format (mobile and landline)
            const phoneRegex = /^(0|\+31|0031)[1-9][0-9]{8}$/;
            if (!phoneRegex.test(input.value.replace(/\s/g, ''))) {
                input.classList.add('is-invalid');
                return false;
            }
        }
        
        // Date validation
        if (input.type === 'date' && input.value) {
            const selectedDate = new Date(input.value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            if (selectedDate <= today) {
                input.classList.add('is-invalid');
                return false;
            }
        }
        
        input.classList.remove('is-invalid');
        return true;
    }

    /**
     * Validate all inputs in a specific step
     * @param {number} stepNumber - The step number to validate
     * @returns {boolean} - Whether all inputs in the step are valid
     */
    function validateStep(stepNumber) {
        const currentStep = document.getElementById(`step${stepNumber}`);
        if (!currentStep) return true;
        
        const inputs = currentStep.querySelectorAll('input, select, textarea');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!validateInput(input)) {
                isValid = false;
            }
        });
        
        // Special validation for step 1
        if (stepNumber === 1) {
            if (serviceTypeSelect.value === 'behandeling') {
                const behandelingSelect = document.querySelector('select[name="behandeling_id"]');
                if (!behandelingSelect.value) {
                    behandelingSelect.classList.add('is-invalid');
                    isValid = false;
                }
            } else if (serviceTypeSelect.value === 'workshop') {
                const workshopSelect = document.querySelector('select[name="workshop_id"]');
                if (!workshopSelect.value) {
                    workshopSelect.classList.add('is-invalid');
                    isValid = false;
                }
            } else if (serviceTypeSelect.value === 'welkomstconsult') {
                const welkomstconsultSelect = document.querySelector('select[name="welkomstconsult_id"]');
                if (!welkomstconsultSelect.value) {
                    welkomstconsultSelect.classList.add('is-invalid');
                    isValid = false;
                }
            } else {
                serviceTypeSelect.classList.add('is-invalid');
                isValid = false;
            }
        }
        
        formValidated = true;
        return isValid;
    }

    /**
     * Validate all steps in the form
     * @returns {boolean} - Whether all steps are valid
     */
    function validateAllSteps() {
        let isValid = true;
        const totalSteps = document.querySelectorAll('.wizard-step').length;
        
        for (let i = 1; i <= totalSteps; i++) {
            if (!validateStep(i)) {
                isValid = false;
                showStep(i);
                break;
            }
        }
        
        return isValid;
    }

    /**
     * Fetch available time slots from the server
     */
    async function fetchAvailableTimeSlots(date, duration) {
        console.log('Fetching slots for date:', date, 'duration:', duration);
        
        try {
            const response = await fetch('includes/get-available-slots.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ date, duration })
            });
            
            const data = await response.json();
            console.log('Server response:', data);
            
            if (!response.ok) {
                throw new Error(data.message || 'Server error occurred');
            }
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data.slots || [];
        } catch (error) {
            console.error('Fetch error:', error);
            throw error;
        }
    }

    /**
     * Update available time slots based on selected date and service
     */
    async function updateTimeSlots() {
        const dateInput = document.getElementById('appointmentDate');
        const timeSelect = document.getElementById('appointmentTime');
        const serviceTypeSelect = document.getElementById('serviceType');
        
        if (!dateInput || !timeSelect || !dateInput.value) {
            console.log('Missing required elements or date value');
            return;
        }

        // Get selected service duration
        let duration = 0;
        
        if (serviceTypeSelect.value === 'behandeling') {
            const behandelingSelect = document.querySelector('select[name="behandeling_id"]');
            if (behandelingSelect && behandelingSelect.selectedIndex > 0) {
                duration = parseInt(behandelingSelect.options[behandelingSelect.selectedIndex].dataset.duration) || 0;
            }
            
            // For radio buttons
            const selectedRadio = document.querySelector('input[name="behandeling_id"]:checked');
            if (selectedRadio) {
                duration = parseInt(selectedRadio.dataset.duration) || 0;
            }
        } else if (serviceTypeSelect.value === 'workshop') {
            const workshopSelect = document.querySelector('select[name="workshop_id"]');
            if (workshopSelect && workshopSelect.selectedIndex > 0) {
                duration = parseInt(workshopSelect.options[workshopSelect.selectedIndex].dataset.duration) || 0;
            }
            
            // For radio buttons
            const selectedRadio = document.querySelector('input[name="workshop_id"]:checked');
            if (selectedRadio) {
                duration = parseInt(selectedRadio.dataset.duration) || 0;
            }
        } else if (serviceTypeSelect.value === 'welkomstconsult') {
            const welkomstconsultSelect = document.querySelector('select[name="welkomstconsult_id"]');
            if (welkomstconsultSelect && welkomstconsultSelect.selectedIndex > 0) {
                duration = parseInt(welkomstconsultSelect.options[welkomstconsultSelect.selectedIndex].dataset.duration) || 0;
            }
            
            // For radio buttons
            const selectedRadio = document.querySelector('input[name="welkomstconsult_id"]:checked');
            if (selectedRadio) {
                duration = parseInt(selectedRadio.dataset.duration) || 0;
            }
        }

        console.log('Selected duration:', duration);
        
        if (!duration) {
            timeSelect.innerHTML = '<option value="">Selecteer eerst een dienst</option>';
            timeSelect.disabled = true;
            return;
        }

        // Show loading state
        timeSelect.innerHTML = '<option value="">Laden...</option>';
        timeSelect.disabled = true;

        try {
            const slots = await fetchAvailableTimeSlots(dateInput.value, duration);
            console.log('Available slots:', slots);
            
            timeSelect.innerHTML = '<option value="">Selecteer tijd</option>';
            
            if (!slots.length) {
                timeSelect.innerHTML = '<option value="">Geen beschikbare tijden</option>';
                timeSelect.disabled = true;
                return;
            }

            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot;
                option.textContent = slot;
                timeSelect.appendChild(option);
            });
            
            timeSelect.disabled = false;
        } catch (error) {
            console.error('Error updating time slots:', error);
            timeSelect.innerHTML = '<option value="">Error: ' + error.message + '</option>';
            timeSelect.disabled = true;
        }
    }

    /**
     * Update the booking summary based on current selectionbij s
     */
    function updateSummary() {
        const serviceType = serviceTypeSelect.value;
        const summaryDiv = document.getElementById('bookingSummary');
        
        if (!serviceType) {
            summaryDiv.innerHTML = '<div class="alert alert-info">Maak uw selectie om de samenvatting te zien.</div>';
            return;
        }
        
        let selectedService = null;
        let serviceName = '';
        let servicePrice = '';
        let serviceDuration = '';
        
        if (serviceType === 'behandeling') {
            selectedService = document.querySelector('select[name="behandeling_id"]');
        } else if (serviceType === 'workshop') {
            selectedService = document.querySelector('select[name="workshop_id"]');
        } else if (serviceType === 'welkomstconsult') {
            selectedService = document.querySelector('select[name="welkomstconsult_id"]');
        }
        
        if (selectedService && selectedService.value) {
            const selectedOption = selectedService.options[selectedService.selectedIndex];
            serviceName = selectedOption.text.split(' (€')[0]; // Remove price from display name
            servicePrice = selectedOption.getAttribute('data-price');
            serviceDuration = selectedOption.getAttribute('data-duration');
            
            let summaryHTML = `
                <div class="booking-item">
                    <h5>${serviceName}</h5>
                    <p><strong>Duur:</strong> ${serviceDuration} minuten</p>
                    <p><strong>Prijs:</strong> €${servicePrice}</p>
                </div>
            `;
            
            // Add appointment details if available
            const appointmentDate = document.getElementById('appointmentDate').value;
            const appointmentTime = document.getElementById('appointmentTime').value;
            
            if (appointmentDate) {
                const dateObj = new Date(appointmentDate);
                const formattedDate = dateObj.toLocaleDateString('nl-NL', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
                summaryHTML += `<p><strong>Datum:</strong> ${formattedDate}</p>`;
            }
            
            if (appointmentTime) {
                summaryHTML += `<p><strong>Tijd:</strong> ${appointmentTime}</p>`;
            }
            
            summaryHTML += `<hr><div class="total"><strong>Totaal: €${servicePrice}</strong></div>`;
            summaryDiv.innerHTML = summaryHTML;
        } else {
            summaryDiv.innerHTML = '<div class="alert alert-info">Selecteer een dienst om de samenvatting te zien.</div>';
        }
    }

    /**
     * Update the payment overview with selected service details
     */
    function updatePaymentOverview() {
        console.log('updatePaymentOverview called');
        
        // Get all the display elements
        const serviceNameDisplay = document.getElementById('service-name-display');
        const appointmentDateDisplay = document.getElementById('appointment-date-display');
        const appointmentTimeDisplay = document.getElementById('appointment-time-display');
        const servicePriceDisplay = document.getElementById('service-price-display');
        const totalPriceDisplay = document.getElementById('total-price-display');
        
        // Debug which elements were found
        console.log('Display elements found:', {
            serviceNameDisplay: !!serviceNameDisplay,
            appointmentDateDisplay: !!appointmentDateDisplay,
            appointmentTimeDisplay: !!appointmentTimeDisplay,
            servicePriceDisplay: !!servicePriceDisplay,
            totalPriceDisplay: !!totalPriceDisplay
        });
        
        // Check if required elements exist
        if (!serviceNameDisplay || !servicePriceDisplay || !totalPriceDisplay) {
            console.log('Some required payment overview elements not found');
            return;
        }
        
        // Get selected service details
        let serviceName = '';
        let servicePrice = 0;
        
        console.log('Service type:', serviceTypeSelect.value);
        
        if (serviceTypeSelect.value === 'behandeling') {
            // Try to get from radio buttons first
            const selectedRadio = document.querySelector('input[name="behandeling_id"]:checked');
            console.log('Selected behandeling radio:', selectedRadio);
            
            if (selectedRadio) {
                serviceName = selectedRadio.dataset.name || selectedRadio.getAttribute('data-name') || '';
                servicePrice = parseFloat(selectedRadio.dataset.price || selectedRadio.getAttribute('data-price') || 0);
                console.log('Radio data:', {
                    name: serviceName,
                    price: servicePrice,
                    nameAttr: selectedRadio.getAttribute('data-name'),
                    priceAttr: selectedRadio.getAttribute('data-price')
                });
            } else {
                // Try dropdown
                const behandelingSelect = document.querySelector('select[name="behandeling_id"]');
                console.log('Behandeling select:', behandelingSelect);
                
                if (behandelingSelect && behandelingSelect.value) {
                    const selectedOption = behandelingSelect.options[behandelingSelect.selectedIndex];
                    serviceName = selectedOption.text.split(' (€')[0];
                    servicePrice = parseFloat(selectedOption.dataset.price || selectedOption.getAttribute('data-price') || 0);
                    console.log('Select data:', {
                        name: serviceName,
                        price: servicePrice,
                        text: selectedOption.text,
                        priceAttr: selectedOption.getAttribute('data-price')
                    });
                }
            }
        } else if (serviceTypeSelect.value === 'workshop') {
            // Similar logic for workshops
            const selectedRadio = document.querySelector('input[name="workshop_id"]:checked');
            console.log('Selected workshop radio:', selectedRadio);
            
            if (selectedRadio) {
                serviceName = selectedRadio.dataset.name || selectedRadio.getAttribute('data-name') || '';
                servicePrice = parseFloat(selectedRadio.dataset.price || selectedRadio.getAttribute('data-price') || 0);
            } else {
                const workshopSelect = document.querySelector('select[name="workshop_id"]');
                console.log('Workshop select:', workshopSelect);
                
                if (workshopSelect && workshopSelect.value) {
                    const selectedOption = workshopSelect.options[workshopSelect.selectedIndex];
                    serviceName = selectedOption.text.split(' (€')[0];
                    servicePrice = parseFloat(selectedOption.dataset.price || selectedOption.getAttribute('data-price') || 0);
                }
            }
        } else if (serviceTypeSelect.value === 'welkomstconsult') {
            // Similar logic for welkomstconsult
            const selectedRadio = document.querySelector('input[name="welkomstconsult_id"]:checked');
            console.log('Selected welkomstconsult radio:', selectedRadio);
            
            if (selectedRadio) {
                serviceName = selectedRadio.dataset.name || selectedRadio.getAttribute('data-name') || '';
                servicePrice = parseFloat(selectedRadio.dataset.price || selectedRadio.getAttribute('data-price') || 0);
            } else {
                const welkomstconsultSelect = document.querySelector('select[name="welkomstconsult_id"]');
                console.log('Welkomstconsult select:', welkomstconsultSelect);
                
                if (welkomstconsultSelect && welkomstconsultSelect.value) {
                    const selectedOption = welkomstconsultSelect.options[welkomstconsultSelect.selectedIndex];
                    serviceName = selectedOption.text.split(' (€')[0];
                    servicePrice = parseFloat(selectedOption.dataset.price || selectedOption.getAttribute('data-price') || 0);
                    console.log('Welkomstconsult select data:', {
                        name: serviceName,
                        price: servicePrice,
                        text: selectedOption.text,
                        priceAttr: selectedOption.getAttribute('data-price')
                    });
                }
            }
        }
        
        // Get appointment date and time
        const dateInput = document.getElementById('appointmentDate');
        const timeInput = document.getElementById('appointmentTime');
        const date = dateInput ? dateInput.value : '';
        const time = timeInput ? timeInput.value : '';
        
        console.log('Final data for payment overview:', {
            serviceName,
            servicePrice,
            date,
            time
        });
        
        // Update payment overview
        serviceNameDisplay.textContent = serviceName || '-';
        servicePriceDisplay.textContent = servicePrice ? '€' + servicePrice.toFixed(2).replace('.', ',') : '-';
        totalPriceDisplay.textContent = servicePrice ? '€' + servicePrice.toFixed(2).replace('.', ',') : '-';
        
        // Update date and time if those elements exist
        if (appointmentDateDisplay) {
            appointmentDateDisplay.textContent = date ? formatDate(date) : '-';
        }
        
        if (appointmentTimeDisplay) {
            appointmentTimeDisplay.textContent = time || '-';
        }
        
        console.log('Payment overview updated');
    }

    /**
     * Format a date string to Dutch locale format
     * @param {string} dateString - Date string in YYYY-MM-DD format
     * @returns {string} - Formatted date string
     */
    function formatDate(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        return date.toLocaleDateString('nl-NL', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    // Initialize the booking form
    initBookingForm();
});

// Character counter for remarks field
document.addEventListener('DOMContentLoaded', function() {
    const notesTextarea = document.getElementById('notes');
    const charCount = document.getElementById('charCount');
    
    if (notesTextarea && charCount) {
        // Initial count
        charCount.textContent = notesTextarea.value.length;
        
        // Update count on input
        notesTextarea.addEventListener('input', function() {
            const count = this.value.length;
            charCount.textContent = count;
            
            // Optional: Add visual feedback when approaching limit
            if (count >= 450) {
                charCount.classList.add('text-warning');
            } else if (count >= 500) {
                charCount.classList.add('text-danger');
            } else {
                charCount.classList.remove('text-warning', 'text-danger');
            }
        });
    }
});

// Service type change handler
const serviceTypeElement = document.getElementById('serviceType');
if (serviceTypeElement) {
    serviceTypeElement.addEventListener('change', function() {
        const serviceType = this.value;
        const behandelingenSelect = document.getElementById('behandelingenSelect');
        const workshopsSelect = document.getElementById('workshopsSelect');
        const welkomstconsultSelect = document.getElementById('welkomstconsultSelect');
        
        // Hide all service selects
        if (behandelingenSelect) behandelingenSelect.style.display = 'none';
        if (workshopsSelect) workshopsSelect.style.display = 'none';
        if (welkomstconsultSelect) welkomstconsultSelect.style.display = 'none';
        
        // Clear all selections
        const behandelingSelect = document.getElementById('behandeling_id');
        const workshopSelect = document.getElementById('workshop_id');
        const welkomstconsultSelectElement = document.getElementById('welkomstconsult_id');
        
        if (behandelingSelect) behandelingSelect.value = '';
        if (workshopSelect) workshopSelect.value = '';
        if (welkomstconsultSelectElement) welkomstconsultSelectElement.value = '';
        
        // Show relevant select and set required attributes
        if (serviceType === 'behandeling' && behandelingenSelect) {
            behandelingenSelect.style.display = 'block';
            if (behandelingSelect) {
                behandelingSelect.required = true;
                if (workshopSelect) workshopSelect.required = false;
                if (welkomstconsultSelectElement) welkomstconsultSelectElement.required = false;
            }
        } else if (serviceType === 'workshop' && workshopsSelect) {
            workshopsSelect.style.display = 'block';
            if (workshopSelect) {
                workshopSelect.required = true;
                if (behandelingSelect) behandelingSelect.required = false;
                if (welkomstconsultSelectElement) welkomstconsultSelectElement.required = false;
            }
        } else if (serviceType === 'welkomstconsult' && welkomstconsultSelect) {
            welkomstconsultSelect.style.display = 'block';
            if (welkomstconsultSelectElement) {
                welkomstconsultSelectElement.required = true;
                if (behandelingSelect) behandelingSelect.required = false;
                if (workshopSelect) workshopSelect.required = false;
            }
        }
        
        updateBookingSummary();
    });
}

// Service selection change handlers
const behandelingSelectElement = document.getElementById('behandeling_id');
const workshopSelectElement = document.getElementById('workshop_id');
const welkomstconsultSelectElement = document.getElementById('welkomstconsult_id');

if (behandelingSelectElement) {
    behandelingSelectElement.addEventListener('change', updateBookingSummary);
}
if (workshopSelectElement) {
    workshopSelectElement.addEventListener('change', updateBookingSummary);
}
if (welkomstconsultSelectElement) {
    welkomstconsultSelectElement.addEventListener('change', updateBookingSummary);
}

function updateBookingSummary() {
    const serviceType = document.getElementById('serviceType').value;
    let selectedService = null;
    let serviceName = '';
    let servicePrice = '';
    
    if (serviceType === 'behandeling') {
        selectedService = document.getElementById('behandeling_id');
    } else if (serviceType === 'workshop') {
        selectedService = document.getElementById('workshop_id');
    } else if (serviceType === 'welkomstconsult') {
        selectedService = document.getElementById('welkomstconsult_id');
    }
    
    if (selectedService && selectedService.value) {
        const selectedOption = selectedService.options[selectedService.selectedIndex];
        serviceName = selectedOption.text;
        servicePrice = selectedOption.getAttribute('data-price');
        
        // Update summary displays
        document.getElementById('service-name-display').textContent = serviceName;
        document.getElementById('service-price-display').textContent = '€' + servicePrice;
        document.getElementById('total-price-display').textContent = '€' + servicePrice;
    }
}
