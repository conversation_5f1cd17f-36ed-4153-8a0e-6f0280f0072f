document.addEventListener('DOMContentLoaded', function() {
    // Check if consent was already given
    if (!localStorage.getItem('cookie_consent_shown')) {
        showCookieConsent();
    }
    
    // Add event listener for privacy policy page
    const privacySettingsBtn = document.getElementById('privacy-settings-btn');
    if (privacySettingsBtn) {
        privacySettingsBtn.addEventListener('click', showCookieSettings);
    }
});

function showCookieConsent() {
    const consentBanner = document.createElement('div');
    consentBanner.className = 'cookie-consent';
    consentBanner.innerHTML = `
        <p>Deze website gebruikt cookies om uw ervaring te verbeteren. Door op "Accepteren" te klikken, gaat u akkoord met het gebruik van alle cookies. U kunt ook kiezen welke cookies u wilt accepteren door op "Instellingen" te klikken.</p>
        <div class="cookie-consent-buttons">
            <button class="cookie-consent-settings">Instellingen</button>
            <button class="cookie-consent-reject">Weigeren</button>
            <button class="cookie-consent-accept">Accepteren</button>
        </div>
    `;
    
    document.body.appendChild(consentBanner);
    
    // Add event listeners
    consentBanner.querySelector('.cookie-consent-accept').addEventListener('click', function() {
        acceptAllCookies();
        hideCookieConsent();
    });
    
    consentBanner.querySelector('.cookie-consent-reject').addEventListener('click', function() {
        rejectAllCookies();
        hideCookieConsent();
    });
    
    consentBanner.querySelector('.cookie-consent-settings').addEventListener('click', function() {
        showCookieSettings();
    });
    
    localStorage.setItem('cookie_consent_shown', 'true');
}

function hideCookieConsent() {
    const consentBanner = document.querySelector('.cookie-consent');
    if (consentBanner) {
        consentBanner.remove();
    }
}

function showCookieSettings() {
    // Create modal if it doesn't exist
    let modal = document.querySelector('.cookie-settings-modal');
    
    if (!modal) {
        modal = document.createElement('div');
        modal.className = 'cookie-settings-modal';
        modal.innerHTML = `
            <div class="cookie-settings-content">
                <h3>Cookie Instellingen</h3>
                <p>Kies hieronder welke cookies u wilt accepteren:</p>
                
                <div class="cookie-option">
                    <input type="checkbox" id="necessary_cookies" checked disabled>
                    <label for="necessary_cookies">Noodzakelijke cookies (altijd aan)</label>
                    <p>Deze cookies zijn essentieel voor het functioneren van de website.</p>
                </div>
                
                <div class="cookie-option">
                    <input type="checkbox" id="analytics_cookies" ${localStorage.getItem('analytics_consent') === 'granted' ? 'checked' : ''}>
                    <label for="analytics_cookies">Analytische cookies</label>
                    <p>Deze cookies helpen ons te begrijpen hoe bezoekers onze website gebruiken.</p>
                </div>
                
                <div class="cookie-settings-footer">
                    <button id="save-cookie-settings">Opslaan</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Add event listener for save button
        modal.querySelector('#save-cookie-settings').addEventListener('click', function() {
            saveCookieSettings();
            modal.style.display = 'none';
            hideCookieConsent();
        });
        
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        });
    }
    
    modal.style.display = 'flex';
}

function saveCookieSettings() {
    const analyticsCookies = document.getElementById('analytics_cookies').checked;
    
    if (analyticsCookies) {
        grantAnalyticsConsent();
    } else {
        revokeAnalyticsConsent();
    }
}

function acceptAllCookies() {
    grantAnalyticsConsent();
}

function rejectAllCookies() {
    revokeAnalyticsConsent();
}