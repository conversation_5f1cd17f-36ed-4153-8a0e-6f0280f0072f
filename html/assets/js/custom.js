/**
 * Ayurveda Theme JavaScript
 * 
 * Handles UI components, animations, and form submissions
 * 
 * @version 2.0
 * <AUTHOR> by [<PERSON><PERSON><PERSON><PERSON><PERSON>]
 */

(function ($) {
    "use strict";
    
    // Main object containing all functionality
    var Ayurveda = {
        // Properties
        initialised: false,
        version: 2.0,
        
        /**
         * Optimize banner image loading for better LCP
         */
        optimizeBannerLoading: function() {
            // Load banner image after critical content
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const banner = document.querySelector('.pa-banner');
                    if (banner) {
                        banner.classList.add('loaded');
                    }
                }, 100); // Small delay to prioritize other critical content
            });
        },
        
        /**
         * Initialize all functionality
         */
        init: function () {
            // Prevent multiple initializations
            if (this.initialised) {
                return;
            }
            this.initialised = true;
            
            // Cache window object
            this.$window = $(window);
            this.$document = $(document);
            this.$body = $('body');
            
            // Call individual functionality modules
            this.setupEventListeners();
            this.setupLoaders();
            this.setupSliders();
            this.setupCounters();
            this.setupNavigation();
            this.setupScrollToTop();
            this.optimizeBannerLoading(); // Add the new optimization
        },
        
        /**
         * Set up general event listeners
         */
        setupEventListeners: function() {
            // Resize handler with debounce
            var resizeTimer;
            this.$window.on('resize', function() {
                clearTimeout(resizeTimer);
                resizeTimer = setTimeout(function() {
                    // Refresh any needed components on resize
                }, 250);
            });
            
            // Document ready
            this.$document.ready(function() {
                // Initialize any components that need the DOM to be ready
            });
        },
        
        /**
         * Set up page loaders and preloaders
         */
        setupLoaders: function() {
            // Page preloader
            this.$window.on("load", function() {
                $(".pa-ellipsis").fadeOut();
                $(".pa-preloader").delay(200).fadeOut("slow");
            });
        },
        
        /**
         * Initialize all sliders
         */
        setupSliders: function() {
            this.initProductSlider();
            this.initRelatedSlider();
            this.initTestimonialSlider();
        },
        
        /**
         * Initialize product slider
         */
        initProductSlider: function() {
            if ($('.pa-trending-product .swiper-container').length) {
                new Swiper('.pa-trending-product .swiper-container', {
                    slidesPerView: 3,
                    loop: true,
                    spaceBetween: 0,
                    speed: 1500,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    breakpoints: {
                        320: {
                            slidesPerView: 1,
                            spaceBetween: 0,
                        },
                        576: {
                            slidesPerView: 1,
                            spaceBetween: 0,
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 0,
                        },
                        992: {
                            slidesPerView: 2,
                            spaceBetween: 0,
                        },
                        1200: {
                            slidesPerView: 3,
                            spaceBetween: 0,
                        }
                    },
                    a11y: {
                        enabled: true,
                        prevSlideMessage: 'Previous slide',
                        nextSlideMessage: 'Next slide',
                    }
                });
            }
        },
        
        /**
         * Initialize related products slider
         */
        initRelatedSlider: function() {
            if ($('.pa-related-product .swiper-container').length) {
                new Swiper('.pa-related-product .swiper-container', {
                    slidesPerView: 2,
                    loop: true,
                    spaceBetween: 30,  // Added default spacing
                    speed: 1500,
                    autoplay: {
                        delay: 3500,
                        disableOnInteraction: false
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    breakpoints: {
                        320: {
                            slidesPerView: 1,
                            spaceBetween: 20,  // Smaller spacing for mobile
                        },
                        576: {
                            slidesPerView: 1,
                            spaceBetween: 20,
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 25,  // Medium spacing for tablets
                        },
                        992: {
                            slidesPerView: 2,
                            spaceBetween: 30,  // Full spacing for desktop
                        }
                    },
                    a11y: {
                        enabled: true
                    }
                });
            }
        },
        
        /**
         * Initialize testimonial slider
         */
        initTestimonialSlider: function() {
            if ($('.pa-tesimonial .swiper-container').length) {
                new Swiper('.pa-tesimonial .swiper-container', {
                    slidesPerView: 1,
                    loop: true,
                    spaceBetween: 0,
                    speed: 1500,
                    autoplay: {
                        delay: 4000,
                        disableOnInteraction: false
                    },
                    navigation: {
                        nextEl: '.swiper-button-next',
                        prevEl: '.swiper-button-prev',
                    },
                    a11y: {
                        enabled: true
                    },
                    effect: 'fade',
                    fadeEffect: {
                        crossFade: true
                    }
                });
            }
        },
        
        /**
         * Initialize counter animations
         */
        setupCounters: function() {
            if ($('.pa-counter-main').length > 0) {
                var counterSection = $('#counter');
                var hasAnimated = false;
                
                // Use Intersection Observer instead of scroll event for better performance
                if ('IntersectionObserver' in window) {
                    var observer = new IntersectionObserver(function(entries) {
                        entries.forEach(function(entry) {
                            if (entry.isIntersecting && !hasAnimated) {
                                Ayurveda.animateCounters();
                                hasAnimated = true;
                                // Stop observing once animation is triggered
                                observer.disconnect();
                            }
                        });
                    }, { threshold: 0.1 });
                    
                    observer.observe(counterSection[0]);
                } else {
                    // Fallback for browsers that don't support Intersection Observer
                    this.$window.on('scroll', function() {
                        if (!hasAnimated) {
                            var oTop = counterSection.offset().top - window.innerHeight;
                            if ($(window).scrollTop() > oTop) {
                                Ayurveda.animateCounters();
                                hasAnimated = true;
                            }
                        }
                    });
                }
            }
        },
        
        /**
         * Animate counter values
         */
        animateCounters: function() {
            $('.counter-value').each(function() {
                var $this = $(this);
                var countTo = $this.attr('data-count');
                
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 3000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(this.countNum);
                    }
                });
            });
        },
        
        /**
         * Set up navigation and menus
         */
        setupNavigation: function() {
            // Mobile menu toggle
            if ($('.pa-toggle-nav').length > 0) {
                $(".pa-toggle-nav").on('click', function(e) {
                    e.stopPropagation();
                    $(".pa-nav-bar").toggleClass("pa-open-menu");
                });
                
                this.$body.on('click', function() {
                    $(".pa-nav-bar").removeClass("pa-open-menu");
                });
                
                $(".pa-menu").on('click', function(e) {
                    e.stopPropagation();
                });
            }
            
            // Submenu toggles
            $(".pa-menu-child").on('click', function(e) {
                e.stopPropagation();
                $(this).find(".pa-submenu").slideToggle(200);
            });
            
            // Secondary menu
            $(".pa-menu-tow-child").on('click', function() {
                $(this).find(".pa-submenu-two").slideToggle(200);
            });
            
            // Secondary menu stop propagation
            $(".pa-submenu-two").on('click', function(e) {
                e.stopPropagation();
            });
            
            // Secondary toggle
            $(".pa-toggle-nav2").on('click', function(e) {
                e.stopPropagation();
                $(".pa-header-two").toggleClass("pa-open-menu");
            });
            
            // Mobile menu icons for small screens
            this.setupMobileMenuIcons();
        },
        
        /**
         * Set up mobile menu icons for small screens
         */
        setupMobileMenuIcons: function() {
            if ($(window).width() <= 767) {
                // If mobile menu icons container exists but is hidden
                if ($('.pa-menu-mobile-icons').length && $('.pa-menu-mobile-icons').css('display') === 'none') {
                    $('.pa-menu-mobile-icons').show();
                }
            }
        },
        
        /**
         * Set up scroll to top functionality
         */
        setupScrollToTop: function() {
            var $scrollButton = $('#scroll');
            
            // Show/hide button based on scroll position
            this.$window.scroll(function() {
                if ($(this).scrollTop() > 100) {
                    $scrollButton.fadeIn();
                } else {
                    $scrollButton.fadeOut();
                }
            });
            
            // Smooth scroll to top
            $scrollButton.on('click', function() {
                $('html, body').animate({
                    scrollTop: 0
                }, 600);
                return false;
            });
        }
    };
    
    // Initialize Ayurveda functionality
    Ayurveda.init();
    
    /**
     * Form validation and submission
     */
    
    /**
     * Check required fields and validation
     * 
     * @param {string|object} formId - Form ID or jQuery object
     * @param {object} targetResp - Response container
     * @returns {number} - 0 if valid, 1 if invalid
     */
    function checkRequire(formId, targetResp) {
        targetResp.html('');
        
        // Validation patterns
        var validationPatterns = {
            email: /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/,
            url: /(http|ftp|https):\/\/[\w-]+(\.[\w-]+)+([\w.,@?^=%&amp;:\/~+#-]*[\w@?^=%&amp;\/~+#-])?/,
            image: /\.(jpe?g|gif|png|PNG|JPE?G)$/,
            mobile: /^[\s()+-]*([0-9][\s()+-]*){6,20}$/,
            facebook: /^(https?:\/\/)?(www\.)?facebook.com\/[a-zA-Z0-9(\.\?)?]/,
            twitter: /^(https?:\/\/)?(www\.)?twitter.com\/[a-zA-Z0-9(\.\?)?]/,
            google_plus: /^(https?:\/\/)?(www\.)?plus.google.com\/[a-zA-Z0-9(\.\?)?]/
        };
        
        var check = 0;
        $('#er_msg').remove();
        
        var target = (typeof formId == 'object') ? $(formId) : $('#' + formId);
        
        // Check each field
        target.find('input, textarea, select').each(function() {
            var $field = $(this);
            var $formGroup = $field.closest('.form-group');
            var $feedback = $formGroup.find('.invalid-feedback');
            
            // Required field check
            if ($field.hasClass('require')) {
                if ($field.val().trim() === '') {
                    check = 1;
                    $field.addClass('error');
                    $formGroup.addClass('form_error');
                    
                    // Show specific error message
                    let fieldName = $field.attr('name') || $field.attr('id');
                    let errorMessage = 'Dit veld is verplicht';
                    
                    // Custom messages for specific fields
                    switch(fieldName) {
                        case 'full_name':
                            errorMessage = 'Vul uw naam in';
                            break;
                        case 'email':
                            errorMessage = 'Vul uw e-mailadres in';
                            break;
                        case 'subject':
                            errorMessage = 'Vul het onderwerp in';
                            break;
                        case 'message':
                            errorMessage = 'Vul uw bericht in';
                            break;
                    }
                    
                    if ($feedback.length) {
                        $feedback.text(errorMessage);
                    } else {
                        $formGroup.append(`<div class="invalid-feedback">${errorMessage}</div>`);
                    }
                } else {
                    $field.removeClass('error');
                    $formGroup.removeClass('form_error');
                    $feedback.text('');
                }
            }
            
            // Validation pattern check
            if ($field.val().trim() !== '') {
                var validType = $field.attr('data-valid');
                
                if (typeof validType !== 'undefined' && validationPatterns[validType]) {
                    if (!validationPatterns[validType].test($field.val().trim())) {
                        $field.addClass('error');
                        $formGroup.addClass('form_error');
                        check = 1;
                        
                        // Show specific validation error message
                        let errorMessage = 'Ongeldig formaat';
                        if (validType === 'email') {
                            errorMessage = 'Vul een geldig e-mailadres in';
                        }
                        
                        if ($feedback.length) {
                            $feedback.text(errorMessage);
                        } else {
                            $formGroup.append(`<div class="invalid-feedback">${errorMessage}</div>`);
                        }
                    } else {
                        $field.removeClass('error');
                        $formGroup.removeClass('form_error');
                        $feedback.text('');
                    }
                }
            }
        });
        
        return check;
    }
    
    /**
     * Handle form submission
     */
    $(".submitForm").on('click', function(e) {
        e.preventDefault();
        
        var _this = $(this);
        var targetForm = _this.closest('form');
        var errorTarget = targetForm.find('.response');
        var hasErrors = false;
        
        // Clear any previous error states
        targetForm.find('.error').removeClass('error');
        targetForm.find('.form_error').removeClass('form_error');
        targetForm.find('.invalid-feedback').text('');
        
        // Validate all required fields
        targetForm.find('input.require, textarea.require, select.require').each(function() {
            var $field = $(this);
            var $formGroup = $field.closest('.form-group');
            var $feedback = $formGroup.find('.invalid-feedback');
            var fieldValue = $field.val().trim();
            var fieldName = $field.attr('name') || $field.attr('id');
            
            // Check empty required fields
            if (fieldValue === '') {
                hasErrors = true;
                $field.addClass('error');
                $formGroup.addClass('form_error');
                
                // Set specific error message
                let errorMessage = 'Dit veld is verplicht';
                switch(fieldName) {
                    case 'full_name':
                        errorMessage = 'Vul uw naam in';
                        break;
                    case 'email':
                        errorMessage = 'Vul uw e-mailadres in';
                        break;
                    case 'subject':
                        errorMessage = 'Vul het onderwerp in';
                        break;
                    case 'message':
                        errorMessage = 'Vul uw bericht in';
                        break;
                }
                
                if ($feedback.length) {
                    $feedback.text(errorMessage);
                } else {
                    $formGroup.append(`<div class="invalid-feedback">${errorMessage}</div>`);
                }
            }
            
            // Additional validation for email
            if (fieldName === 'email' && fieldValue !== '') {
                var emailRegex = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/;
                if (!emailRegex.test(fieldValue)) {
                    hasErrors = true;
                    $field.addClass('error');
                    $formGroup.addClass('form_error');
                    
                    if ($feedback.length) {
                        $feedback.text('Vul een geldig e-mailadres in');
                    } else {
                        $formGroup.append('<div class="invalid-feedback">Vul een geldig e-mailadres in</div>');
                    }
                }
            }
            
            // Additional validation for message length
            if (fieldName === 'message' && fieldValue.length > 500) {
                hasErrors = true;
                $field.addClass('error');
                $formGroup.addClass('form_error');
                
                if ($feedback.length) {
                    $feedback.text('Bericht mag niet langer zijn dan 500 tekens');
                } else {
                    $formGroup.append('<div class="invalid-feedback">Bericht mag niet langer zijn dan 500 tekens</div>');
                }
            }
        });
        
        // If there are errors, stop form submission
        if (hasErrors) {
            return;
        }
        
        // Disable button to prevent double submission
        _this.prop('disabled', true);
        _this.addClass('submitting');
        
        // Add loading indicator
        var originalText = _this.html();
        _this.html('<i class="fa fa-spinner fa-spin"></i> Verzenden...');
        
        // Get form data
        var formData = new FormData(targetForm[0]);
        
        // Submit form via AJAX
        $.ajax({
            method: 'post',
            url: 'ajaxmail.php',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            timeout: 30000
        }).done(function(response) {
            try {
                const result = JSON.parse(response);
                if (result.success) {
                    // Success
                    targetForm.find('input:not([type="hidden"]):not([type="submit"])').val('');
                    targetForm.find('textarea').val('');
                    errorTarget.html('<div class="alert alert-success">' + result.message + '</div>');
                    
                    // Auto-hide success message after 5 seconds
                    setTimeout(function() {
                        errorTarget.find('.alert').fadeOut(function() {
                            $(this).remove();
                        });
                    }, 5000);
                } else {
                    // Error with message
                    errorTarget.html('<div class="alert alert-danger">' + result.message + '</div>');
                }
            } catch (e) {
                // JSON parse error
                errorTarget.html('<div class="alert alert-danger">Er is een onverwachte fout opgetreden. Probeer het later opnieuw.</div>');
            }
        }).fail(function(xhr, status, error) {
            // Handle AJAX failure
            console.error("Form submission error:", status, error);
            errorTarget.html('<div class="alert alert-danger">Server error. Probeer het later opnieuw.</div>');
        }).always(function() {
            // Re-enable button and restore text
            _this.prop('disabled', false);
            _this.removeClass('submitting');
            _this.html(originalText);
        });
    });
    
})(jQuery);

$(document).ready(function() {
    // Initialize forms
    $('form').each(function() {
        initializeForm($(this));
    });

    // Remove error state immediately on input
    $('input.require, textarea.require, select.require').on('input', function() {
        const $field = $(this);
        const $formGroup = $field.closest('.form-group');
        
        // Remove error states
        $field.removeClass('error');
        $formGroup.removeClass('form_error');
        $formGroup.find('.invalid-feedback').remove();
    });

    // Character counter for message field
    const messageField = $('#message');
    const charCount = $('#messageCharCount');
    
    function updateCharCount() {
        if (!messageField.length || !charCount.length) return;
        
        const count = messageField.val().length;
        charCount.text(count);
        
        const charCountContainer = charCount.parent();
        
        if (count >= 450 && count < 500) {
            charCountContainer.removeClass('warning danger').addClass('warning');
        } else if (count >= 500) {
            charCountContainer.removeClass('warning danger').addClass('danger');
        } else {
            charCountContainer.removeClass('warning danger');
        }
    }
    
    // Initial count
    updateCharCount();
    
    // Update count on input
    if (messageField.length) {
        messageField.on('input', updateCharCount);
    }
    
    // Form validation messages
    const errorMessages = {
        full_name: 'Vul uw naam in',
        email: {
            required: 'Vul uw e-mailadres in',
            invalid: 'Vul een geldig e-mailadres in'
        },
        subject: 'Vul het onderwerp in',
        message: {
            required: 'Vul uw bericht in',
            tooLong: 'Bericht mag niet langer zijn dan 500 tekens'
        }
    };
    
    function validateField($field) {
        let isValid = true;
        const value = $field.val().trim();
        const $formGroup = $field.closest('.form-group');
        const $feedback = $formGroup.find('.invalid-feedback');
        const fieldId = $field.attr('id');
        
        // Remove previous error state
        $formGroup.removeClass('error');
        $field.removeClass('error');
        
        // Required field check
        if ($field.hasClass('require') && value === '') {
            $formGroup.addClass('error');
            $field.addClass('error');
            $feedback.text(errorMessages[fieldId]?.required || errorMessages[fieldId]);
            isValid = false;
        }
        
        // Email validation
        else if (fieldId === 'email' && value !== '') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                $formGroup.addClass('error');
                $field.addClass('error');
                $feedback.text(errorMessages.email.invalid);
                isValid = false;
            }
        }
        
        // Message length validation
        else if (fieldId === 'message' && value.length > 500) {
            $formGroup.addClass('error');
            $field.addClass('error');
            $feedback.text(errorMessages.message.tooLong);
            isValid = false;
        }
        
        return isValid;
    }
    
    // Form submission handler
    $('.pa-contact-form form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitButton = $form.find('button[type="submit"]');
        const $responseDiv = $form.find('.response');
        let isValid = true;
        
        // Clear previous response messages
        $responseDiv.empty();
        
        // Validate all fields
        $form.find('input.require, textarea.require').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        if (!isValid) {
            return;
        }
        
        // For reCAPTCHA v3, refresh the token before submission
        if (typeof grecaptcha !== 'undefined' && typeof RECAPTCHA_SITE_KEY !== 'undefined') {
            grecaptcha.ready(function() {
                grecaptcha.execute(RECAPTCHA_SITE_KEY, {action: 'contact'})
                    .then(function(token) {
                        // Update the token
                        document.getElementById('recaptcha_response').value = token;

                        // Disable submit button and show loading state
                        $submitButton.prop('disabled', true)
                                    .addClass('submitting')
                                    .text('Even geduld...');

                        // Submit form via AJAX
                        submitFormWithAjax($form, $submitButton, $responseDiv);
                    });
            });
        } else {
            // Fallback if reCAPTCHA is not available
            $responseDiv.html('<div class="alert alert-danger">reCAPTCHA kon niet worden geladen. Probeer de pagina te vernieuwen.</div>');
        }
    });

    // Function to handle the actual AJAX submission
    function submitFormWithAjax($form, $submitButton, $responseDiv) {
        $.ajax({
            method: 'POST',
            url: 'ajaxmail.php',
            data: new FormData($form[0]),
            cache: false,
            contentType: false,
            processData: false,
            timeout: 30000
        }).done(function(response) {
            try {
                const result = JSON.parse(response);
                if (result.success) {
                    // Clear form
                    $form[0].reset();
                    updateCharCount();
                    
                    $responseDiv.html('<div class="alert alert-success">Uw bericht is succesvol verzonden.</div>');
                    
                    // Auto-hide success message
                    setTimeout(function() {
                        $responseDiv.find('.alert').fadeOut(function() {
                            $(this).remove();
                        });
                    }, 5000);
                } else {
                    $responseDiv.html('<div class="alert alert-danger">' + result.message + '</div>');
                }
            } catch (e) {
                $responseDiv.html('<div class="alert alert-danger">Er is een onverwachte fout opgetreden. Probeer het later opnieuw.</div>');
            }
        }).fail(function() {
            $responseDiv.html('<div class="alert alert-danger">Er is een probleem met de verbinding. Controleer uw internetverbinding en probeer het opnieuw.</div>');
        }).always(function() {
            // Re-enable submit button
            $submitButton.prop('disabled', false)
                        .removeClass('submitting')
                        .text('Verstuur');
        });
    }
    
    // Live validation on blur
    $('.pa-contact-form form input.require, .pa-contact-form form textarea.require').on('blur', function() {
        validateField($(this));
    });
});

function initializeForm(form) {
    form.find('.error').removeClass('error');
    form.find('.form_error').removeClass('form_error');
    form.find('.invalid-feedback').remove();
}

function updateCharCount(textarea, maxLength) {
    // Check if the textarea exists before trying to access its properties
    if (!textarea) return;
    
    const currentLength = textarea.value.length;
    const remaining = maxLength - currentLength;
    
    // Find the counter element
    const counterElement = document.getElementById('char-count');
    if (counterElement) {
        counterElement.textContent = `${remaining} characters remaining`;
    }
}

// When attaching the event listener, check if the element exists first
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contact-details-form');
    if (contactForm) {
        // Only prevent the popup, but keep the validation
        contactForm.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('invalid', (e) => {
                e.preventDefault();
            });
        });
    }
    
    const textarea = document.querySelector('.char-count-textarea');
    if (textarea) {
        const maxLength = textarea.getAttribute('maxlength') || 500; // default to 500 if not specified
        
        // Initial count
        updateCharCount(textarea, maxLength);
        
        // Update on input
        textarea.addEventListener('input', function() {
            updateCharCount(this, maxLength);
        });
    }
});

// Initialize reCAPTCHA for newsletter when available
function initNewsletterRecaptcha() {
    if (typeof grecaptcha !== 'undefined' && document.getElementById('newsletter_recaptcha_response') && typeof RECAPTCHA_SITE_KEY !== 'undefined') {
        grecaptcha.ready(function() {
            grecaptcha.execute(RECAPTCHA_SITE_KEY, {action: 'newsletter'})
                .then(function(token) {
                    document.getElementById('newsletter_recaptcha_response').value = token;
                });
        });
    }
}

// Newsletter subscription
$(document).ready(function() {
    // Initialize newsletter reCAPTCHA on page load
    initNewsletterRecaptcha();
    $('#newsletterForm').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $response = $('#newsletterResponse');
        const $submitButton = $form.find('button[type="submit"]');

        // Basic validation
        const email = $form.find('input[name="email"]').val().trim();
        if (!email || !isValidEmail(email)) {
            $response.html('<div class="alert alert-danger">Vul een geldig e-mailadres in.</div>');
            return;
        }

        // For reCAPTCHA v3, refresh the token before submission
        if (typeof grecaptcha !== 'undefined' && typeof RECAPTCHA_SITE_KEY !== 'undefined') {
            grecaptcha.ready(function() {
                grecaptcha.execute(RECAPTCHA_SITE_KEY, {action: 'newsletter'})
                    .then(function(token) {
                        // Update the token
                        document.getElementById('newsletter_recaptcha_response').value = token;

                        // Disable submit button and show loading state
                        $submitButton.prop('disabled', true).html('Even geduld...');

                        // Submit form via AJAX
                        submitNewsletterWithAjax($form, $submitButton, $response);
                    });
            });
        } else {
            // Fallback if reCAPTCHA is not available
            $response.html('<div class="alert alert-danger">reCAPTCHA kon niet worden geladen. Probeer de pagina te vernieuwen.</div>');
        }
    });

    // Function to handle the actual newsletter AJAX submission
    function submitNewsletterWithAjax($form, $submitButton, $response) {
        $.ajax({
            method: 'POST',
            url: 'subscribe.php',
            data: $form.serialize(),
            timeout: 30000,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            beforeSend: function() {
                $response.html(''); // Clear previous messages
            }
        }).done(function(response) {
            if (response.success) {
                // Clear form
                $form[0].reset();
                $response.html('<div class="alert alert-success">' + response.message + '</div>');
            } else {
                $response.html('<div class="alert alert-danger">' + response.message + '</div>');
            }

            // Auto-hide any message after 5 seconds
            setTimeout(function() {
                $response.find('.alert').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);

        }).fail(function() {
            $response.html('<div class="alert alert-danger">Er is een probleem opgetreden. Probeer het later opnieuw.</div>');

            // Auto-hide error message after 5 seconds
            setTimeout(function() {
                $response.find('.alert').fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);

        }).always(function() {
            // Reset submit button
            $submitButton.prop('disabled', false).html('Schrijf je in');
        });
    }
});

// Make wizard globally accessible
let wizard = {
    currentStep: 1,
    steps: document.querySelectorAll('.wizard-step'),
    progressSteps: document.querySelectorAll('.wizard-progress .step'),

    showStep: function(stepNumber) {
        // Hide all steps
        this.steps.forEach(step => {
            step.classList.remove('active');
        });
        
        // Show current step
        const currentStep = document.getElementById(`step-${stepNumber}`);
        if (currentStep) {
            currentStep.classList.add('active');
            this.currentStep = stepNumber;
        }
        
        // Update progress bar
        this.progressSteps.forEach(step => {
            const stepNum = parseInt(step.dataset.step);
            if (stepNum === stepNumber) {
                step.classList.add('active');
            } else if (stepNum < stepNumber) {
                step.classList.add('completed');
                step.classList.remove('active');
            } else {
                step.classList.remove('active', 'completed');
            }
        });
    },
    
    nextStep: function() {
        if (this.currentStep < this.steps.length) {
            this.showStep(this.currentStep + 1);
        }
    },
    
    previousStep: function() {
        if (this.currentStep > 1) {
            this.showStep(this.currentStep - 1);
        }
    },

    processPayment: function(formData) {
        // Prevent default form submission
        event.preventDefault();
        
        // Show loading state
        this.showStep(3);
        
        // Debug log
        console.log('Processing payment with form data:', Object.fromEntries(formData.entries()));
        
        // Ensure action is set
        if (!formData.get('action')) {
            formData.append('action', 'process_payment');
        }
        
        // Ensure delivery method is set
        if (!formData.get('delivery_method')) {
            const deliveryMethod = document.querySelector('input[name="delivery_method"]:checked').value;
            formData.append('delivery_method', deliveryMethod);
        }
        
        // Use the dedicated payment processor in the includes directory
        const url = '/includes/process_payment.php';
        console.log('Sending payment request to:', url);
        
        fetch(url, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('Error response body:', text);
                    throw new Error(`Server error: ${response.status}`);
                });
            }
            
            return response.text().then(text => {
                console.log('Raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Failed to parse JSON:', e, 'Raw text:', text);
                    throw new Error('Invalid JSON response from server');
                }
            });
        })
        .then(data => {
            console.log('Processed data:', data);
            if (data && data.success && data.redirectUrl) {
                console.log('Redirecting to payment page:', data.redirectUrl);
                // Redirect to Mollie payment page
                window.location.href = data.redirectUrl;
            } else {
                console.error('Invalid response data:', data);
                // Show error and go back to step 2
                alert(data && data.message ? data.message : 'Er is een fout opgetreden bij het verwerken van de betaling.');
                this.showStep(2);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Er is een fout opgetreden bij het verwerken van de betaling. Probeer het later opnieuw.');
            this.showStep(2);
        });

        return false;
    },

    init: function() {
        this.showStep(this.currentStep);

        // Next step buttons
        document.querySelectorAll('.next-step').forEach(button => {
            button.addEventListener('click', () => this.nextStep());
        });

        // Previous step buttons
        document.querySelectorAll('.prev-step').forEach(button => {
            button.addEventListener('click', () => this.previousStep());
        });
    }
};

// Initialize the wizard if we're on the cart page
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.pa-cart')) {
        wizard.init();
    }
});
