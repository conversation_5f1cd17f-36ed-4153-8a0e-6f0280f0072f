
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('questionnaireForm');
    if (!form) return;

    const wizard = {
        currentStep: 1,
        totalSteps: 8,
        
        init: function() {
            // Initialize event listeners
            this.initializeButtons();
            this.initializeValidation();
            this.updateProgressBar();
            
            // Show first step
            this.showStep(1);
        },

        showStep: function(stepNumber) {
            // Hide all steps
            document.querySelectorAll('.wizard-step').forEach(step => {
                step.classList.remove('active');
            });
            
            // Show current step
            const currentStep = document.querySelector(`.wizard-step[data-step="${stepNumber}"]`);
            if (currentStep) {
                currentStep.classList.add('active');
                this.currentStep = stepNumber;
                this.updateProgressBar();
            }
        },

        updateProgressBar: function() {
            document.querySelectorAll('.wizard-progress .step').forEach(step => {
                const stepNum = parseInt(step.dataset.step);
                if (stepNum === this.currentStep) {
                    step.classList.add('active');
                    step.classList.remove('completed');
                } else if (stepNum < this.currentStep) {
                    step.classList.add('completed');
                    step.classList.remove('active');
                } else {
                    step.classList.remove('active', 'completed');
                }
            });
        },

        validateCurrentStep: function() {
            const currentStepElement = document.querySelector(`.wizard-step[data-step="${this.currentStep}"]`);
            let isValid = true;

            // Validate radio buttons for steps 1-7
            if (this.currentStep <= 7) {
                const radioGroups = currentStepElement.querySelectorAll('input[type="radio"]');
                const checkedGroups = new Set();
                
                radioGroups.forEach(radio => {
                    if (radio.checked) {
                        checkedGroups.add(radio.name);
                    }
                });

                const totalGroups = new Set(Array.from(radioGroups).map(radio => radio.name));
                isValid = checkedGroups.size === totalGroups.size;

                if (!isValid) {
                    alert('Beantwoord alle vragen voordat u verder gaat.');
                }
            }

            // Validate personal information fields in step 8
            if (this.currentStep === 8) {
                const requiredFields = currentStepElement.querySelectorAll('input[required]');
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (!isValid) {
                    alert('Vul alle verplichte velden in.');
                }
            }

            return isValid;
        },

        initializeButtons: function() {
            // Next button handlers
            document.querySelectorAll('.next-step').forEach(button => {
                button.addEventListener('click', () => {
                    if (this.validateCurrentStep() && this.currentStep < this.totalSteps) {
                        this.showStep(this.currentStep + 1);
                    }
                });
            });

            // Previous button handlers
            document.querySelectorAll('.prev-step').forEach(button => {
                button.addEventListener('click', () => {
                    if (this.currentStep > 1) {
                        this.showStep(this.currentStep - 1);
                    }
                });
            });
        },

        initializeValidation: function() {
            // Remove validation styling on input
            form.querySelectorAll('input').forEach(input => {
                input.addEventListener('input', function() {
                    this.classList.remove('is-invalid');
                });
            });

            // Form submission handler
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                
                if (!this.validateCurrentStep()) {
                    return;
                }

                const submitButton = form.querySelector('button[type="submit"]');
                
                // Disable submit button and show loading state
                submitButton.disabled = true;
                submitButton.innerHTML = 'Even geduld...';

                // Create FormData object
                const formData = new FormData(form);
                
                // Debug: Log all form data
                console.log('Form data being submitted:');
                for (let [key, value] of formData.entries()) {
                    console.log(`${key}: ${value}`);
                }

                // Send the form data
                fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        // Show success notification instead of alert
                        showNotification(data.message, 'success');
                        
                        // Redirect to homepage after 3 seconds
                        setTimeout(() => {
                            window.location.href = 'index.php';
                        }, 3000);
                    } else {
                        // Show error notification
                        showNotification(data.message || 'Er is een fout opgetreden. Probeer het later opnieuw.', 'error');
                        
                        // Re-enable submit button
                        submitButton.disabled = false;
                        submitButton.innerHTML = 'Verzenden';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Er is een fout opgetreden. Probeer het later opnieuw.', 'error');
                    
                    // Re-enable submit button
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Verzenden';
                });
            });
        }
    };

    // Initialize the wizard
    wizard.init();
});

/**
 * Show a notification message
 * @param {string} message - The message to display
 * @param {string} type - The type of notification ('success', 'error', 'info')
 */
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '15px 25px',
        backgroundColor: type === 'error' ? '#e74c3c' : (type === 'info' ? '#3498db' : '#d5bc5a'),
        color: 'white',
        borderRadius: '4px',
        zIndex: '1000',
        boxShadow: '0 2px 5px rgba(0,0,0,0.2)',
        opacity: '0',
        transition: 'opacity 0.3s ease'
    });
    
    // Add to document
    document.body.appendChild(notification);
    
    // Fade in
    setTimeout(() => {
        notification.style.opacity = '1';
    }, 10);
    
    // Fade out and remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => notification.remove(), 300);
    }, 2700); // Slightly less than redirect time
}

