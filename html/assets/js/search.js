document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.pa-widget.pa-search input');
    const searchButton = document.querySelector('.pa-widget.pa-search .pa-btn');
    
    // Check page type more precisely with regex test
    const isListingPage = /\/(shop|behandelingen|blog|workshops)\.php$/.test(window.location.pathname);
    
    if (isListingPage) {
        // Listing page functionality
        initListingPage(searchInput, searchButton);
    } else {
        // Detail page functionality
        initDetailPage(searchInput, searchButton);
    }
});

function initListingPage(searchInput, searchButton) {
    // Process URL search parameter on load
    const urlParams = new URLSearchParams(window.location.search);
    const searchParam = urlParams.get('search');
    
    if (searchParam && searchInput) {
        searchInput.value = searchParam;
        performSearch(searchParam);
    }
    
    // Add event listeners
    if (searchInput) {
        // Debounce search input for better performance
        const debounce = (fn, delay) => {
            let timeoutId;
            return (...args) => {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => fn(...args), delay);
            };
        };
        
        searchInput.addEventListener('input', debounce(e => {
            performSearch(e.target.value);
            // Update URL with search parameter for bookmarking/sharing
            updateSearchParam(e.target.value);
        }, 300));
    }
    
    if (searchButton) {
        searchButton.addEventListener('click', () => {
            if (searchInput) performSearch(searchInput.value);
        });
    }
    
    // Initialize category counts
    updateCategoryCounts();
}

function initDetailPage(searchInput, searchButton) {
    if (!searchInput || !searchButton) return;
    
    const redirectToShopSearch = () => {
        const searchTerm = searchInput.value.trim();
        if (searchTerm) {
            window.location.href = '../shop.php?search=' + encodeURIComponent(searchTerm);
        }
    };
    
    searchButton.addEventListener('click', redirectToShopSearch);
    searchInput.addEventListener('keypress', e => {
        if (e.key === 'Enter') redirectToShopSearch();
    });
}

function updateSearchParam(searchTerm) {
    const url = new URL(window.location);
    if (searchTerm && searchTerm.trim()) {
        url.searchParams.set('search', searchTerm);
    } else {
        url.searchParams.delete('search');
    }
    window.history.replaceState({}, '', url);
}

function performSearch(searchTerm) {
    searchTerm = (searchTerm || '').toLowerCase().trim();
    const itemContainers = document.querySelectorAll('.col-sm-6[data-category], .col-md-6 .pa-blog-box');
    let visibleItems = 0;
    
    // Define selectors for different content types
    const titleSelectors = [
        '.pa-product-content h4 a',      // shop items
        '.pa-blog-title h2 a',           // blog posts
        '.pa-treatment-title h2',        // treatments
        '.pa-workshop-title h2'          // workshops
    ];
    
    itemContainers.forEach(container => {
        let titleElement = null;
        
        // Find the first matching title element
        for (const selector of titleSelectors) {
            titleElement = container.querySelector(selector);
            if (titleElement) break;
        }
        
        if (!titleElement) return;
        
        const itemTitle = titleElement.textContent || '';
        const isMatch = searchTerm === '' || itemTitle.toLowerCase().includes(searchTerm);
        
        // For blog posts, we need to find the parent column
        const displayElement = container.classList.contains('pa-blog-box') 
            ? container.closest('.col-md-6') 
            : container;
            
        displayElement.style.display = isMatch ? '' : 'none';
        if (isMatch) visibleItems++;
    });
    
    showNoResults(visibleItems === 0);
    updateCategoryCounts();
}

function updateCategoryCounts() {
    const categoryLinks = document.querySelectorAll('.pa-shop-category ul li a');
    
    categoryLinks.forEach(link => {
        const href = link.getAttribute('href');
        const categoryMatch = href && href.match(/category=([^&]+)/);
        if (!categoryMatch) return;
        
        const category = categoryMatch[1];
        const countSpan = link.querySelector('span');
        if (!countSpan) return;
        
        // Count visible items for this category
        const visibleCount = Array.from(
            document.querySelectorAll(`.col-sm-6[data-category="${category}"]`)
        ).filter(item => item.style.display !== 'none').length;
        
        // Update the count
        countSpan.textContent = visibleCount;
    });
}

function showNoResults(show) {
    const itemsContainer = document.querySelector('.row');
    if (!itemsContainer) return;
    
    let noResultsDiv = document.querySelector('.no-results-message');
    
    if (show) {
        if (!noResultsDiv) {
            noResultsDiv = document.createElement('div');
            noResultsDiv.className = 'no-results-message';
            noResultsDiv.style.cssText = 'text-align: center; padding: 20px; width: 100%;';
            noResultsDiv.innerHTML = '<h3>Geen resultaten gevonden</h3>';
            itemsContainer.appendChild(noResultsDiv);
        } else {
            noResultsDiv.style.display = '';
        }
    } else if (noResultsDiv) {
        noResultsDiv.style.display = 'none';
    }
}
