.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    color: #fff;
    padding: 15px;
    z-index: 9999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.cookie-consent p {
    margin: 0;
    padding: 10px 0;
    flex: 1;
}

.cookie-consent-buttons {
    display: flex;
    gap: 10px;
}

.cookie-consent button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.cookie-consent-accept {
    background: #4CAF50;
    color: white;
}

.cookie-consent-reject {
    background: #f44336;
    color: white;
}

.cookie-consent-settings {
    background: #2196F3;
    color: white;
}

.cookie-settings-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    justify-content: center;
    align-items: center;
}

.cookie-settings-content {
    background: white;
    padding: 20px;
    border-radius: 5px;
    max-width: 500px;
    width: 100%;
    color: #333;
}

.cookie-settings-content h3 {
    margin-top: 0;
}

.cookie-settings-footer {
    margin-top: 20px;
    text-align: right;
}