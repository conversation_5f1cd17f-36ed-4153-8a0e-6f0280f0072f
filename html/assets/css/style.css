/* Base styles */
body {
    font-family: 'Hind', sans-serif;
    color: #242424;
    font-size: 16px;
    font-weight: 300;
    line-height: 26px;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Hind', sans-serif;
    color: #111111;
}

a {
    color: #242424;
    transition: 0.3s;
    text-decoration: none;
}

a:hover {
    text-decoration: none;
    color: #d5bc5a;
}

ul {
    margin: 0;
    padding: 0;
}

li {
    list-style: none;
}

/* Spacing helpers */
.spacer-top {
    padding-top: 80px;
}

.spacer-bottom {
    padding-bottom: 80px;
}

/* Headings */
.pa-heading {
    text-align: center;
    margin-bottom: 25px;
}

.pa-heading h1 {
    margin-bottom: 15px;
    text-transform: uppercase;
    font-size: 32px;
    font-weight: 800;
}

.pa-heading h5 {
    margin-bottom: 20px;
    text-transform: capitalize;
    font-size: 20px;
    color: #d5bc5a;
}

.pa-heading img {
    opacity: 0.4;
}

/* Buttons */
.pa-btn {
    background: #d5bc5a;
    color: #fff;
    padding: 0 20px;
    min-width: 160px;
    height: 50px;
    line-height: 50px; /* Match the height */
    border: 2px solid #d5bc5a;
    border-radius: 50px;
    text-transform: capitalize;
    outline: none;
    text-align: center;
    cursor: pointer;
    transition: 0.3s;
    display: inline-block;
    text-decoration: none; /* Ensure no underline */
    vertical-align: middle; /* Ensure vertical alignment */
}

.pa-btn:hover {
    outline: none;
    background: #fff;
    color: #d5bc5a;
    text-decoration: none; /* Ensure no underline on hover */
}

.pa-btn:focus, .modal-header .close {
    outline: none;
}

/* Remove any conflicting styles */
.wizard-buttons .pa-btn {
    line-height: 50px; /* Ensure consistent line height */
    padding: 0 20px; /* Override any other padding */
    display: inline-block;
    vertical-align: middle;
}

/* Form elements */
input {
    width: 100%;
    height: 50px;
    padding: 0 20px;
    border: 1px solid rgba(130, 180, 64, 0.48);
    outline: none;
    border-radius: 50px;
}

/* Form validation styling */
.form-control.error {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #dc3545;
}

.form-group {
    margin-bottom: 1rem;
}

/* Textarea specific styling */
.pa-cmnt-form textarea,
.pa-contact-form textarea {
    width: 100%;
    height: 200px;
    padding: 10px 20px;
    border: 1px solid rgba(130, 180, 64, 0.48);
    outline: none;
    border-radius: 10px;
    resize: none;
}

/* Error state for textarea */
.pa-cmnt-form textarea.error,
.pa-contact-form textarea.error {
    border-color: #dc3545 !important;
}

/* Ensure error messages align with rounded fields */
.form-group:has(input[type="text"].error) .invalid-feedback,
.form-group:has(input[type="email"].error) .invalid-feedback,
.form-group:has(input[type="tel"].error) .invalid-feedback {
    padding-left: 25px; /* Slightly more padding to align with rounded input */
}

/* Ensure error messages align with textarea */
.form-group:has(textarea.error) .invalid-feedback {
    padding-left: 25px;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Header styles */
.pa-top-header {
    height: 50px;
    display: flex;
    align-items: center;
    padding: 0 150px;
}

.pa-top-header p {
    margin-bottom: 0;
    font-size: 14px;
}

.pa-header-call {
    text-align: right;
}

.pa-header-call span {
    font-size: 12px;
    color: #6caaa8;
    font-weight: 600;
}

.pa-main-header {
    display: flex;
    align-items: center;
    padding: 0 150px;
    z-index: 1;
    position: absolute;
    left: 0;
    right: 0;
    top: 30px;
}

/* Navigation */
.pa-menu > ul > li,
.pa-head-icon > ul > li {
    display: inline-block;
    margin-right: 20px;
    position: relative;
}

.pa-head-icon > ul > li {
    margin-right: 10px;
}

.pa-menu ul li:last-child,
.pa-head-icon ul li:last-child {
    margin-right: 0;
}

.pa-menu > ul > li > a {
    color: #d5bc5a;
    padding: 25px 0 26px;
    text-decoration: none;
    font-size: 17px;
    font-weight: 500;
}

.pa-about-menu > ul > li > a {
    color: #ffffff;
}

.pa-menu ul li a {
    text-transform: capitalize;
    text-decoration: none;
    font-size: 17px;
    font-weight: 250;
}

.pa-menu ul li a:hover {
    color: white;
}

.pa-nav-bar {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-top: 9px;
}

/* User and cart icons - base styles */
li.pa-user-login,
li.pa-cart-icon {
    height: 40px;
    width: 40px;
    background-color: #d5bc5a;
    border-radius: 50%;
    text-align: center;
    line-height: 37px;
    margin-right: 15px;
    transition: background-color 0.3s ease;
    display: inline-block !important; /* Force display */
}

li.pa-user-login.logged-in {
    background-color: #6caaa8;
}

li.pa-user-login svg,
li.pa-cart-icon svg {
    fill: #ffffff !important;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    transition: fill 0.3s ease;
}

/* Hover styles */
li.pa-user-login:hover,
li.pa-cart-icon:hover,
li.pa-user-login.logged-in:hover {
    background-color: #000;
}

/* Remove the problematic hover styles */
li.pa-user-login:hover,
li.pa-cart-icon:hover,
li.pa-user-login.logged-in:hover {
    right: initial;
    top: initial;
    height: 40px;
    line-height: 37px;
    min-width: initial;
    padding: initial;
    background-color: #000;
    color: initial;
    border: none;
    font-size: initial;
    border-radius: 50%;
}

/* Dropdown styles */
li.pa-user-login .dropdown-toggle {
    display: block;
    height: 100%;
    line-height: 37px;
}

li.pa-user-login .dropdown-toggle::after {
    display: none;
}

li.pa-user-login .dropdown-menu {
    margin-top: 10px;
    background: #fff;
    border: none;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-width: 180px;
}

li.pa-user-login .dropdown-item {
    padding: 8px 16px;
    color: #404040;
    font-size: 14px;
}

li.pa-user-login .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #6caaa8;
}

li.pa-user-login .dropdown-divider {
    margin: 4px 0;
    border-color: #eee;
}

/* Fix z-index for dropdown visibility */
.pa-head-icon {
    position: relative;
    z-index: 1000;
    margin-left: 40px;
}

.pa-head-icon > ul > li:last-child {
    margin-right: 0;
}

.pa-head-icon ul li svg {
    width: 16px;
    height: 16px;
    fill: #000;
    cursor: pointer;
}

.pa-head-icon ul li span {
    background: #d9534f;
    min-width: 16px;
    height: 16px;
    position: absolute;
    text-align: center;
    line-height: 16px;
    font-size: 11px;
    border-radius: 100%;
    top: -2px;
    right: -12px;
    color: #ffffff;
    cursor: pointer;
}

/* Banner styles - optimized */
.pa-banner {
    padding: 200px 0 185px;
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3));
    background-color: #f8f8f8;
    background-position: left;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

/* Add background image via JavaScript for better LCP */
.pa-banner.loaded {
    background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/home.jpg);
}

.pa-banner-text {
    animation: slideInFromRight 3s;
    padding: 40px;
}

.pa-banner-text h4 {
    position: relative;
    margin-bottom: 25px;
    font-size: 22px;
    color: #d5bc5a;
    font-style: italic;
    letter-spacing: 1px;
}

.pa-banner-text h2 {
    font-size: 45px;
    margin-bottom: 30px;
    letter-spacing: 1px;
    font-weight: 600;
    text-transform: uppercase;
    color: #ffffff;
}

.pa-banner-text p {
    margin-bottom: 30px;
    color: #ffffff;
    font-size: 18px;
    line-height: 1.6;
}

.pa-banner-text .pa-view-all {
    display: inline-block;
    padding: 12px 25px;
    background-color: #d5bc5a;
    color: #fff;
    border-radius: 5px;
    transition: all 0.3s ease;
}

/* Swiper navigation */
.swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    transition: 0.3s;
    margin-bottom: 10px;
    outline: none;
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: #6caaa8;
    height: 20px;
    border-radius: 30px;
}

.swiper-button-next,
.swiper-button-prev {
    background-image: none;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #d5bc5a;
    outline: none;
    border-radius: 50%;
}

/* Specific positioning for related products slider */
.pa-related-product .swiper-button-prev {
    left: -20px;
}

.pa-related-product .swiper-button-next {
    right: -20px;
}

/* Adjust for mobile screens */
@media (max-width: 575px) {
    .pa-related-product .swiper-button-prev {
        left: 5px;
    }
    
    .pa-related-product .swiper-button-next {
        right: 5px;
    }
}

.swiper-button-next:after,
.swiper-button-prev:after {
    position: absolute;
    content: '';
    width: 10px;
    height: 10px;
    background: transparent;
    border-top: 2px solid #fff;
    border-right: 2px solid #fff;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}

.swiper-button-next:after {
    transform: rotate(45deg);
}

.swiper-button-prev:after {
    transform: rotate(-135deg);
}

.swiper-button-prev {
    left: 0;
}

.swiper-button-next {
    right: 0;
}

/* Medicine boxes */
.pa-medicine-box {
    text-align: center;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    padding: 30px;
    transition: 0.3s;
    border-bottom: 3px solid transparent;
}

.pa-medicine-box h2 {
    font-size: 22px;
    margin: 20px 0 10px;
    text-transform: capitalize;
}

.pa-medicine-box p {
    margin: 0;
    padding-top: 10px;
    color: rgb(149, 153, 167);
}

.pa-medicine-box:hover {
    border-color: #d5bc5a;
}

/* Section backgrounds */
.pa-why,
.pa-counter,
.pa-tesimonial,
.pa-why-pure,
.pa-team-home {
    background-color: #d5bc5a;
    position: relative;
    z-index: 0;
}

.swiper-container {
    z-index: 0;
}

.pa-why.spacer-top.spacer-bottom:before {
    position: absolute;
    content: "";
    background-color: transparent;
    background-image: radial-gradient(at right top, rgba(213, 188, 90, 0.54) 0%, rgba(213, 188, 90, 0.98) 75%);
    opacity: 1;
    transition: background 0.3s, border-radius 0.3s, opacity 0.3s;
    height: 100%;
    width: 100%;
    top: 0;
    z-index: -1;
}

/* Why section */
.pa-why-img {
    width: 380px;
    height: 380px;
    line-height: 380px;
    border: 1px dashed #95b494;
    border-radius: 100%;
    text-align: center;
    margin: 0 auto;
}

.pa-why-ul ul li {
    font-size: 20px;
    margin-bottom: 60px;
    font-weight: 500;
    position: relative;
    text-transform: capitalize;
}

.pa-why-ul ul li:last-child {
    margin-bottom: 0;
}

.pa-why-ul ul li:after {
    position: absolute;
    content: '';
    width: 145px;
    border: 0.5px dashed #95b494;
    top: 0;
    bottom: 0;
    height: 1px;
    margin: auto 0;
}

.pa-why-left ul li {
    text-align: right;
    padding-right: 150px;
}

.pa-why-right ul li {
    text-align: left;
    padding-left: 150px;
}

.pa-why-right ul li:after {
    left: 0;
}

.pa-why-left ul li:after {
    right: 0;
}

/* Various line rotations */
.pa-why-left ul li:first-child:after {
    transform: rotate(11deg) translate(70px, 0);
}

.pa-why-left ul li:nth-child(2):after {
    transform: rotate(8deg) translate(13px, 10px);
}

.pa-why-left ul li:nth-child(4):after {
    transform: rotate(-8deg) translate(10px, -10px);
}

.pa-why-left ul li:last-child:after {
    transform: rotate(-14deg) translate(55px, -10px);
}

.pa-why-right ul li:first-child:after {
    transform: rotate(-11deg) translate(-62px, 10px);
}

.pa-why-right ul li:nth-child(2):after {
    transform: rotate(-8deg) translate(-15px, 10px);
}

.pa-why-right ul li:nth-child(3):after {
    transform: rotate(0deg);
}

.pa-why-right ul li:nth-child(4):after {
    transform: rotate(8deg) translate(-11px, -10px);
}

.pa-why-right ul li:last-child:after {
    transform: rotate(16deg) translate(-50px, -10px);
}

/* Padding for items */
.pa-why-left ul li:first-child {
    padding-right: 85px;
}

.pa-why-left ul li:nth-child(2) {
    padding-right: 140px;
}

.pa-why-left ul li:nth-child(4) {
    padding-right: 145px;
}

.pa-why-left ul li:last-child {
    padding-right: 100px;
}

.pa-why-right ul li:first-child {
    padding-left: 95px;
}

.pa-why-right ul li:nth-child(2) {
    padding-left: 140px;
}

.pa-why-right ul li:nth-child(4) {
    padding-left: 145px;
}

.pa-why-right ul li:last-child {
    padding-left: 110px;
}

.pa-why-img img {
    width: 150px;
}

/* Product images */
.pa-product-img.pr-product-in2 {
    background-color: whitesmoke;
    padding: 30px 0 0;
}

.pa-product-img.pa-product-in00 {
    background-color: white;
    padding: 30px 0 0;
}

/* Product boxes */
.pa-product-box {
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    padding: 0 0 44px;
    text-align: center;
    position: relative;
    transition: 0.3s;
    margin-bottom: 30px;
}

.pa-product-content h4 a {
    text-decoration: none;
    transition: 0.3s;
}

.pa-product-content h4 a:hover {
    color: #222;
}

.pa-product-content p {
    margin-bottom: 0;
    font-weight: 500;
}

.pa-product-content p del {
    padding-right: 15px;
    font-weight: 400;
}

.pa-product-box:hover {
    transform: translateY(-5px);
}

.pa-product-cart ul li svg {
    width: 16px;
    height: 16px;
    transition: 0.3s;
}

.pa-product-cart ul li a:hover svg {
    fill: #222;
}

.pa-trending-product .pa-heading {
    margin-bottom: 31px;
}

.pa-product-content h4 {
    font-size: 20px;
    margin-bottom: 10px;
}

.pa-product-content {
    padding-top: 30px;
}

.pa-product-content p {
    margin-bottom: 0;
    font-weight: 500;
    color: #d5bc5a;
}

.pa-product-content p del {
    padding-right: 15px;
    font-weight: 400;
    color: #222;
}

p.pa-product-rating i {
    margin-right: 2px;
    font-size: 12px;
}

p.pa-product-rating {
    padding-bottom: 10px;
}

.pa-product-cart ul li svg {
    width: 16px;
    height: 16px;
    transition: 0.3s;
    fill: #ffffff;
}

.pa-product-cart ul li {
    display: inline-block;
    margin: 0 5px;
}

.pa-product-cart ul li a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    border: 2px solid #d5bc5a;
    text-align: center;
    display: block;
    border-radius: 50%;
    background: #d5bc5a;
}

.pa-product-cart ul li a:hover {
    border: 2px solid;
    background-color: white;
}

.pa-product-cart ul li a:hover svg {
    fill: #d5bc5a;
}

.pa-product-cart {
    bottom: -25px;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    transition: 0.3s;
    opacity: 0;
    visibility: hidden;
}

.pa-product-box:hover .pa-product-cart {
    bottom: -15px;
    opacity: 1;
    visibility: visible;
}

.pa-product-box:hover,
.pa-blog-box:hover,
.pa-counter-box:hover,
.pa-medicine-box:hover {
    transform: translateY(-5px);
}

.pa-product,
.pa-blog,
.pa-why-pure,
.pa-team,
.pa-price-plan {
    padding-bottom: 50px;
}

/* Counter boxes */
.pa-counter-box h1 {
    font-size: 35px;
    margin-bottom: 15px;
    display: inline-block;
}

.pa-counter-box span {
    font-size: 35px;
    font-weight: 600;
}

.pa-counter-box {
    text-align: center;
    background: #fff;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    padding: 30px 30px 23px;
    transition: 0.3s;
}

.pa-counter-box p {
    margin-bottom: 0;
    font-weight: 400;
    text-transform: capitalize;
}

.pa-counter {
    margin-bottom: 180px;
}

.pa-counter-main {
    margin-bottom: -180px;
}

.pa-counter-box svg {
    width: 50px;
    height: auto;
    fill: #d5bc5a;
    display: block;
    text-align: center;
    margin: 0 auto 10px;
}

/* Testimonials */
.pa-tesimonial-box {
    display: flex;
    max-width: 900px;
    justify-content: center;
    margin: auto;
    padding: 35px;
    align-items: center;
    position: relative;
}

.pas-testi-img {
    width: 300px;
    z-index: 1;
    position: relative;
}

.pas-testi-img img {
    border-radius: 10px;
}

.pas-testi-content {
    width: calc(100% - 300px);
    padding-left: 40px;
}

.pas-testi-content p {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: #fff;
}

.pas-testi-content h2 {
    font-size: 25px;
    margin-bottom: 20px;
    color: #fff;
}

.pas-testi-content .pa-view-all {
    font-size: 18px;
}

.pa-tesimonial-box:after {
    position: absolute;
    content: '';
    left: -8px;
    top: -2px;
    background-image: radial-gradient(#b6d2adeb 2px, transparent 2px);
    background-size: 20px 20px;
    width: 200px;
    bottom: -8px;
    z-index: 0;
}

.pa-tesimonial-button {
    width: 100px;
    right: 0;
    position: absolute;
    bottom: 53px;
}

.pas-testi-img:after {
    position: absolute;
    content: '';
    right: -90px;
    top: -35px;
    width: 150px;
    height: 150px;
    background: rgba(186, 213, 178, 0.12);
    z-index: -1;
    margin: auto;
    border-radius: 100%;
}

.pas-testi-img:before,
.pa-about-img:before,
.pa-services-img:before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: #d5bc5a;
    z-index: -1;
    border-radius: 10px;
    transition: 0.3s;
}

.pa-tesimonial-box:hover .pas-testi-img:before,
.pa-about-img:hover:before,
.pa-services-img:hover:before {
    top: -10px;
    left: -10px;
    bottom: 10px;
    right: 10px;
}

.pa-trending-product,
.pa-product-single {
    padding-bottom: 65px;
}

.pa-services {
    padding-bottom: 63px;
}

/* Blog styles */
.pa-blog-box {
    border-radius: 10px;
    transition: 0.3s;
    margin-bottom: 17px;
    display: flex;
}

.pa-blog-box img {
    border-radius: 10px;
}

.pa-img-00 img {
    width: 100%;
    border-radius: 10px;
}

.pa-blog-category {
    color: #d5bc5a;
    font-size: 14px;
    font-weight: 600;
    text-transform: capitalize;
}

.pa-blog-title {
    padding: 16px 25px 17px;
}

.pa-blog-box h2 {
    font-size: 20px;
    margin: 5px 0;
    line-height: 26px;
    font-weight: 600;
}

.pa-blog-date {
    font-size: 14px;
    color: #d5bc5a;
    font-weight: 600;
}

.pa-blog-view {
    padding: 10px 14px;
}

.pa-blog-view.pa-view2 {
    padding-left: 0;
}

.pa-blog-view ul li {
    display: inline-block;
    padding-right: 15px;
}

.pa-blog-view ul li a {
    font-size: 14px;
}

.pa-blog-view ul li a i {
    padding-right: 5px;
}

.pa-blog-page {
    padding-bottom: 75px;
}

/* Footer */
.pa-footer {
    background-size: cover;
    padding: 72px 0 42px;
    background-image: linear-gradient(135deg, #d5bc5a 0%, #d5bc5a 100%);
}

.pa-foot-box img,
.pa-foot-box {
    margin-bottom: 30px;
}

.pa-foot-box p,
.pa-foot-box a {
    color: #fff;
    font-weight: 500;
}

.pa-foot-box a:hover {
    color: #000000;
}

.pa-foot-title {
    color: #fff;
    font-size: 25px;
    margin-bottom: 30px;
    font-weight: 700;
}

.pa-foot-box ul li {
    margin-bottom: 10px;
}

.pa-foot-box ul li:last-child,
.pa-foot-box p {
    margin-bottom: 0;
}

.pa-newsletter {
    position: relative;
    margin-bottom: 25px;
}

.pa-newsletter .pa-btn {
    position: absolute;
    right: 5px;
    top: 5px;
    height: 40px;
    line-height: 36px;
}

.pa-newsletter input {
    padding-right: 170px;
    border: none;
}

/* Copyright */
.pa-copyright p,
.pa-copyright p a {
    margin: 0;
    color: #fff;
    font-weight: 400;
}

.pa-copyright {
    background-image: linear-gradient(135deg, #d5bc5a 0%, #d5bc5a 100%);
    padding: 12px 0;
    text-align: center;
    position: relative;
}

/* Breadcrumb */
.pa-breadcrumb {
    padding: 171px 0 93px;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(https://dummyimage.com/1143x240/);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

/* Various page backgrounds */
.welkomstconsult-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/voordeelkaarten-bg.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.voordeelkaarten-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/voordeelkaarten-bg.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.indian-foot-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/indian-foot-bg.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.compassiewandeling-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/compassiewandeling.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.admin-bg, .profile-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/contact.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.stemvorktherapie-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/stemvorktherapie.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.uitjehoofdinjelijf-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/uitjehoofdinjelijf.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.druppels-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/druppels.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.creme-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/creme-bg.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.olieroller-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/olierollers-bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.overmij-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/overmij.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.kruidenstempel-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/kruidenstempel.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.tienermassage-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/tienermassage.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.contact-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/contact.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.behandelingen-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/behandelingen.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.workshops-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/workshops.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.blog-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/blog.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.abhyanga-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/Abhyanga-massage.jpeg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.ontspanningsmassage-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/ontspanningsmassage.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.shop-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/shop.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.booking-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/contact.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.auraspray-bg {
    background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.3)), url(../images/auraspray-bg.jpg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.pa-breadcrumb-box {
    text-align: center;
}

.pa-breadcrumb-box ul li {
    display: inline-block;
    padding-right: 30px;
    position: relative;
    color: #fff;
    text-transform: capitalize;
}

.pa-breadcrumb-box ul li:last-child {
    padding: 0;
}

.pa-breadcrumb-box h1 {
    font-size: 30px;
    margin-bottom: 10px;
    color: #fff;
    text-transform: uppercase;
    font-weight: 700;
}

.pa-breadcrumb-box ul li a {
    color: #fff;
}

.pa-breadcrumb-box ul li:after {
    position: absolute;
    right: 10px;
    top: 4px;
    content: '';
    width: 8px;
    height: 8px;
    background: rgba(255, 0, 0, 0);
    border-top: 7px double #d5bc5a;
    border-right: 7px double #d5bc5a;
    transform: rotate(45deg);
    bottom: 0;
    margin: auto;
}

.pa-breadcrumb-box ul li:last-child:after {
    display: none;
}

/* About section */
.pa-about-content h2 {
    font-size: 25px;
    margin-bottom: 20px;
}

.pa-about-img {
    position: relative;
    width: fit-content;
}

.pa-about-img img {
    border-radius: 10px;
}

.pa-about-content .pa-heading,
.pa-services-content .pa-heading,
.pa-why-pure .pa-heading {
    text-align: left;
    margin-bottom: 25px;
}

.pa-about-content .pa-heading h1 {
    margin-top: 10px;
}

.pa-about-content p:last-child {
    margin: 0;
}

/* Team section */
.ps-team-box {
    text-align: center;
    background: #fff;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    padding-bottom: 13px;
    margin-bottom: 30px;
}

.ps-team-box h2 {
    font-size: 20px;
    margin: 12px 0 5px;
    text-transform: capitalize;
}

.ps-team-box p {
    margin-bottom: 0;
    text-transform: capitalize;
}

.ps-team-box img {
    border-radius: 10px 10px 0 0;
    width: 100%;
}

.pa-team-social li {
    display: inline-block;
    margin-right: 10px;
}

.pa-team-social li a {
    width: 28px;
    height: 28px;
    display: flex;
    border: 1px solid #d5bc5a;
    border-radius: 4px;
    justify-content: center;
    align-items: center;
}

.pa-team-social {
    background: #fff;
    padding: 20px 0 0;
    margin: 0 20px;
    z-index: 1;
    position: relative;
    border-radius: 4px 4px 0 0;
    transition: 0.3s;
}

.pa-team-social li a:hover {
    background: #d5bc5a;
    transform: translateY(-3px);
}

.ps-team-box:hover .pa-team-social {
    margin: -20px 20px 0;
}

.pa-team-social li a:hover svg {
    fill: #fff;
}

.pa-team-social li svg {
    fill: #d5bc5a;
    width: 14px;
}

.pa-team-social li:last-child {
    margin: 0;
}

/* Shop sidebar */
.pa-sidebar-title {
    font-size: 25px;
    margin-bottom: 30px;
    text-transform: uppercase;
    font-weight: 700;
}

.pa-widget ul li {
    margin-bottom: 15px;
    position: relative;
}

.pa-widget ul li:last-child {
    margin-bottom: 0;
}

.pa-product-sidebar {
    padding: 30px;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
}

.pa-widget {
    border-bottom: 1px solid #eee;
    margin-bottom: 22px;
    padding-bottom: 30px;
}

.pa-widget:last-child {
    border: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.pa-widget.pa-search .pa-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    min-width: 100px;
    height: 40px;
    line-height: 36px;
}

.pa-widget.pa-search {
    position: relative;
}

.pa-widget.pa-search input {
    padding-right: 110px;
}

.pa-widget ul li span {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    background: #d5bc5a;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 2px;
}

.pa-widget.pa-tag ul li {
    display: inline-block;
    margin-bottom: 5px;
    margin-right: 1px;
}

.pa-widget.pa-tag ul li a {
    border: 1px solid #d5bc5a;
    padding: 0 10px;
    min-height: 35px;
    display: block;
    line-height: 35px;
    border-radius: 4px;
    font-weight: 400;
    text-transform: capitalize;
}

.pa-product-widget ul li {
    display: flex;
    margin-bottom: 20px;
}

.pa-product-widget .pa-pro-wid-img {
    width: 70px;
}

.pa-product-widget .pa-pro-wid-content {
    width: calc(100% - 70px);
    padding-left: 15px;
}

.pa-pro-wid-content h4 {
    font-size: 16px;
    margin-bottom: 6px;
}

.pa-pro-wid-content p {
    margin-bottom: 0;
}

.pa-pro-wid-img img {
    border: 1px solid #d5bc5a;
    padding: 5px;
    border-radius: 4px;
}

.pa-widget.pa-shop-category {
    padding-bottom: 27px;
}

.pa-shop-category ul li a.active {
    font-weight: bold;
    text-decoration: underline;
}

/* Pricing plans */
.pa-plan-box {
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 70px;
    padding: 0 30px 50px;
    text-align: center;
    border-bottom: 3px solid #d5bc5a;
    position: relative;
    transform: translate(0, 37px);
    transition: 0.3s;
    margin-bottom: 30px;
}

.pa-plan-box.pa-popular-plan {
    transform: translate(0, 0);
}

.pa-popular-plan .pa-plan-title {
    margin-bottom: 39px;
}

.pa-plan-box:hover {
    transform: translateY(27px);
}

.pa-plan-box.pa-popular-plan:hover {
    transform: translateY(-5px);
}

.pa-plan-title {
    font-size: 25px;
    text-transform: capitalize;
    max-width: 194px;
    border-radius: 0 0 43px 43px;
    box-shadow: 0 19px 20px rgba(130, 180, 64, 0.23);
    background-color: #d5bc5a;
    margin: 0 auto 39px;
    padding: 24px 0;
    color: #ffffff;
}

.pa-plan-price {
    font-size: 50px;
    margin-bottom: 0;
    color: #d5bc5a;
}

.pa-plan-price span {
    font-size: 25px;
}

.pa-plan-price .pa-plan-month {
    display: block;
    font-size: 20px;
    text-transform: uppercase;
}

.pa-plan-box ul li {
    margin-bottom: 15px;
    text-transform: capitalize;
}

.pa-plan-box ul li:last-child {
    margin-bottom: 0;
}

.pa-plan-box ul {
    margin: 36px 0 40px;
    padding: 33px 0;
    border-top: 2px dashed #eee;
    border-bottom: 2px dashed #eee;
}

.pa-plan-offer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #d5bc5a;
    margin: 0;
    padding: 10px;
    color: #fff;
    border-radius: 10px 10px 0 0;
    font-weight: 600;
    font-size: 20px;
    letter-spacing: 1px;
    text-transform: capitalize;
}

.pa-popular-plan .pa-plan-price {
    font-size: 80px;
}

/* Services */
.pa-services-img {
    position: relative;
}

.pa-services-img img {
    border-radius: 10px;
    width: 100%;
}

.pa-services-icon svg {
    width: 40px;
    height: 40px;
    fill: #d5bc5a;
}

.pa-heading.pa-services-heading {
    margin-bottom: 14px;
}

.pa-services-content p {
    max-width: 421px;
    width: 100%;
    margin-bottom: 36px;
}

.pa-services-content ul {
    column-count: 2;
}

.pa-services-content ul li {
    display: flex;
    margin-bottom: 35px;
}

.pa-services-content ul li:last-child {
    margin-bottom: 0;
}

.pa-services-icon {
    width: 50px;
}

.pa-services-data {
    width: calc(100% - 50px);
    padding-left: 15px;
}

.pa-services-data h2 {
    font-size: 20px;
    text-transform: capitalize;
    font-weight: 600;
    margin-bottom: 6px;
}

.pa-services-data p {
    margin-bottom: 0;
}

/* Blog single */
.pa-blog-single .pa-blog-box {
    transform: translateY(0);
}

.pa-blog-title p {
    margin: 10px 0 0;
}

.pa-blog-user {
    display: flex;
    border: 1px solid #eee;
    padding: 30px;
    border-radius: 4px;
    margin-top: 43px;
}

.pa-blog-user-img {
    width: 100px;
}

.pa-blog-user-content {
    width: calc(100% - 100px);
    padding-left: 20px;
}

.pa-blog-user-content p {
    margin-bottom: 0;
}

.pa-blog-user-content h2 {
    font-size: 20px;
    margin-bottom: 8px;
}

.pa-blog-user-img img {
    border-radius: 100%;
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.pa-blog-comnt {
    margin: 42px 0 0;
}

.pa-cmnt-title {
    font-size: 25px;
    font-weight: 700;
    text-transform: uppercase;
    margin-bottom: 30px;
}

.pa-blog-comnt ul .pa-cmnt-box {
    display: flex;
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 4px;
}

.pa-blog-comnt ul li p {
    margin-bottom: 0;
}

.pa-cmnt-img {
    width: 100px;
}

.pa-cmnt-content {
    width: calc(100% - 100px);
    padding-left: 20px;
}

.pa-cmnt-content h3 {
    font-size: 20px;
    margin-bottom: 8px;
    display: inline-block;
}

.pa-cmnt-content p span {
    padding-right: 20px;
    position: relative;
    font-size: 14px;
    color: #d5bc5a;
}

.pa-cmnt-content p span:after {
    position: absolute;
    content: '';
    right: 10px;
    top: 2px;
    background: #d5bc5a;
    width: 1px;
    height: 14px;
}

.pa-cmnt-content p span:last-child:after {
    display: none;
}

.pa-cmnt-img img {
    border-radius: 100%;
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.pa-blog-comnt > ul > li:last-child .pa-cmnt-box {
    margin-bottom: 0;
}

.pa-blog-comnt ul li ul {
    margin-left: 50px;
}

.pa-cmnt-form input,
.pa-cmnt-form textarea,
.pa-contact-form input,
.pa-contact-form textarea {
    margin-bottom: 15px;
}

.pa-cmnt-form {
    margin-top: 50px;
}

.pa-cmnt-form textarea,
.pa-contact-form textarea {
    width: 100%;
    height: 200px;
    padding: 10px 20px;
    border: 1px solid rgba(130, 180, 64, 0.48);
    outline: none;
    border-radius: 10px;
    resize: none;
}

/* Contact */
.pa-contact-detail {
    padding-bottom: 50px;
}

.pa-contact-map iframe {
    width: 100%;
    height: 600px;
    border: none;
    border-radius: 10px;
    filter: grayscale(1);
}

.pa-contact-form {
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    padding: 30px;
    border-radius: 10px;
    transform: translate(-130px, 36px);
    z-index: 1;
    position: relative;
    background: #fff;
}

.pa-contact-box {
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    padding: 25px 30px 23px;
    border-radius: 10px;
    transition: 0.3s;
    margin-bottom: 30px;
    text-align: center;
}

.pa-contact-box:hover {
    transform: translateY(-5px);
}

.pa-contact-box h4 {
    font-size: 20px;
    margin-bottom: 10px;
}

.pa-contact-box p {
    margin-bottom: 0;
    max-width: 100%;
    width: 210px;
    margin: 0 auto;
}

.response {
    position: fixed;
    bottom: 45px;
    right: 40px;
    color: #f44336;
}

.response p {
    transform: translate(10px, 0);
    margin: 0;
}

.pa-contact {
    padding-bottom: 73px;
}

/* Product single */
.pa-prod-thumb-img {
    text-align: center;
    border: 1px solid #eee;
    padding: 30px;
    border-radius: 10px;
}

.pa-prod-title {
    font-size: 25px;
    margin-bottom: 15px;
}

.pa-prod-category,
.pa-prod-price {
    color: #d5bc5a;
}

.pa-prod-category {
    display: block;
    margin-bottom: 4px;
}

.pa-prod-category span,
.pa-prod-price span {
    color: #222;
    padding-right: 5px;
}

.pa-prod-price span {
    font-size: 16px;
    font-weight: 400;
}

.pa-prod-price {
    margin-bottom: 5px;
    font-size: 20px;
    font-weight: 600;
}

.pa-prod-count {
    display: flex;
    margin: 30px 0 22px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    padding: 30px 0;
    align-items: center;
}

.pa-prod-count .pa-cart-quantity {
    margin-right: 20px;
}

.pa-prod-content ul li svg {
    width: 18px;
    fill: #d5bc5a;
    margin-right: 10px;
    height: 18px;
}

.pa-prod-content ul li {
    margin-bottom: 5px;
}

.pa-prod-content ul {
    margin-top: 15px;
}

.pa-rating i {
    font-size: 14px;
    color: #d5bc5a;
}

.pa-related-product .pa-heading {
    text-align: left;
    margin-bottom: 0;
    margin-top: 18px;
}

.pa-prod-price span.pa-offer-price {
    padding-left: 10px;
    color: #d5bc5a;
}

/* Cart */
.pa-cart-box table,
.pa-bill-detail table {
    width: 100%;
}

.pa-cart-img img {
    width: 70px;
}

.pa-cart-box table th {
    padding: 12px 20px 13px;
    text-transform: capitalize;
    background: #d5bc5a;
    color: #fff;
    border-right: 1px solid #eee;
}

.pa-cart-box table td {
    padding: 20px;
    text-transform: capitalize;
    border: 1px solid #eee;
}

.pa-cart-box table th,
.pa-bill-detail table th {
    background: #d5bc5a;
    color: #fff;
}

.pa-cart-box table th {
    border-right: 1px solid #eee;
}

.pa-cart-box table td {
    border-top: none;
    max-width: 250px;
}

.pa-cart-quantity button {
    width: 25px;
    height: 25px;
    border: 1px solid rgba(130, 180, 64, 0.48);
    border-radius: 4px;
    background: #fff;
    position: relative;
    outline: none;
    transition: 0.3s;
}

.pa-cart-quantity button:hover {
    background: #d5bc5a;
}

.pa-cart-quantity button:hover:after,
.pa-cart-quantity button:hover:before {
    background: #fff;
}

.pa-cart-quantity button:after,
.pa-cart-quantity button:before {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    content: '';
    background: rgb(149, 180, 148);
    margin: auto;
    width: 8px;
    height: 2px;
    transition: 0.3s;
}

.pa-cart-quantity button.pa-add:before {
    width: 2px;
    height: 8px;
}

.pa-cart-quantity {
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content;
}

.pa-cart-quantity input {
    width: 50px;
    border-radius: 4px;
    height: 35px;
    margin: auto 4px;
    padding: 0 5px;
    text-align: center;
    line-height: 35px;
}

td.pa-cart-update,
td.pa-cart-total,
table.pa-garnd-total td:first-child {
    text-align: right;
}

.pa-cart-box p {
    margin: 0;
}

td.pa-cart-total-price p {
    font-size: 18px;
    font-weight: 600;
    color: #d5bc5a;
}

.pa-garnd-total p span:first-child {
    font-size: 20px;
}

.pa-garnd-total {
    text-align: right;
}

.pa-garnd-total p span:last-child {
    min-width: 143px;
    display: inline-block;
    text-align: left;
    color: #d5bc5a;
}

.pa-garnd-total p span {
    padding: 15px 20px;
    font-weight: 600;
}

.pa-garnd-total .pa-btn {
    margin-top: 20px;
}

.pa-garnd-total p {
    border: 1px solid #eee;
    border-top: 0;
}

.pa-cart-box table th:first-child,
.pa-bill-detail table th:first-child {
    border-radius: 10px 0 0 0;
}

.pa-cart-box table th:last-child,
.pa-bill-detail table th:last-child {
    border-radius: 0 10px 0 0;
    border: none;
}

.pa-garnd-total p:nth-child(3) {
    border-radius: 0 0 10px 10px;
}

/* Remove button in cart */
.remove-item {
    background: #d5bc5a;
    color: #fff;
    width: 40px;
    height: 40px;
    border: 2px solid #d5bc5a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.remove-item:hover {
    background: #c4ac49;
    border-color: #c4ac49;
    transform: translateY(-2px);
}

.remove-item svg {
    width: 20px;
    height: 20px;
    fill: #ffffff;
    transition: all 0.3s ease;
}

.remove-item:hover svg {
    transform: rotate(90deg);
}

.pa-cart-box table td .remove-item {
    margin: 0 auto;
}

/* Cart totals */
.cart-totals {
    text-align: right;
    padding: 15px 20px !important;
}

.cart-totals .totals-line {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-bottom: 5px;
}

.cart-totals .totals-line:last-child {
    margin-bottom: 0;
}

.cart-totals .totals-line span:first-child {
    margin-right: 15px;
}

.cart-totals .totals-line.total {
    margin-top: 10px;
    padding-top: 10px;
    font-weight: 600;
}

.cart-totals .totals-line.total span:last-child {
    color: #d5bc5a;
    font-size: 18px;
}

/* Checkout */
.pa-checkout {
    padding-top: 76px;
}

.pa-bill-form label {
    display: flex;
    margin-bottom: 20px;
}

.pa-bill-form label:last-child {
    margin-bottom: 0;
}

label.pa-bill-title,
p.pa-bill-title {
    font-size: 25px;
    font-weight: 600;
    margin-bottom: 40px;
}

.pa-bill-form label span {
    min-width: 120px;
}

.pa-bill-detail table th {
    padding: 12px 20px 13px;
    text-transform: capitalize;
    border-right: 1px solid #eee;
}

.pa-bill-detail table td {
    padding: 15px 20px;
    text-transform: capitalize;
    border: 1px solid #eee;
}

.pa-checkout-total td {
    font-size: 20px;
    font-weight: 600;
}

.pa-checkout-total td:last-child {
    color: #d5bc5a;
}

.pa-checkout-btn {
    text-align: right;
    margin-top: 20px;
}

.pa-checkout-btn .pa-btn {
    min-width: 200px;
    text-align: center;
}

/* Loader */
.pa-preloader {
    background-color: #fff;
    bottom: 0;
    height: 100%;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 9999;
}

.pa-ellipsis {
    margin: 0 auto;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
    width: 64px;
    text-align: center;
    z-index: 9999;
}

.pa-ellipsis span {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #95b494;
    animation: ball-pulse-sync 1s 0s infinite ease-in-out;
}

.pa-ellipsis span:nth-child(1) {
    animation: ball-pulse-sync 1s -0.14s infinite ease-in-out;
}

.pa-ellipsis span:nth-child(2) {
    animation: ball-pulse-sync 1s -70ms infinite ease-in-out;
}

@keyframes ball-pulse-sync {
    33% {
        transform: translateY(10px);
    }
}

/* Premium Voordeelkaarten Styles */
.premium-divider {
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #d5bc5a, #b8a347);
    border-radius: 2px;
}

.premium-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(213, 188, 90, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.premium-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.premium-card-header {
    padding: 30px 30px 20px;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid rgba(213, 188, 90, 0.1);
}

.premium-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #d5bc5a, #b8a347);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 5px 20px rgba(213, 188, 90, 0.3);
}

.premium-icon i {
    color: #fff;
    font-size: 24px;
}

.premium-card-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.premium-card-subtitle {
    color: #6c757d;
    font-size: 16px;
    margin-bottom: 0;
}

.premium-card-body {
    padding: 30px;
}

.pricing-package {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.pricing-package:hover {
    border-color: rgba(213, 188, 90, 0.3);
    background: #fff;
}

.package-badge {
    position: absolute;
    top: -10px;
    right: 20px;
    background: linear-gradient(135deg, #d5bc5a, #b8a347);
    color: #fff;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.package-badge.best-value {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.package-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

.price-comparison {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.original-price {
    font-size: 16px;
    color: #6c757d;
    text-decoration: line-through;
}

.discounted-price {
    font-size: 24px;
    font-weight: 700;
    color: #d5bc5a;
}

.savings-highlight {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #28a745;
    font-weight: 600;
    font-size: 14px;
}

.savings-highlight i {
    font-size: 16px;
}

.premium-card-footer {
    padding: 0 30px 30px;
    text-align: center;
}

.premium-btn {
    background: linear-gradient(135deg, #d5bc5a, #b8a347);
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(213, 188, 90, 0.3);
}

.premium-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(213, 188, 90, 0.4);
    background: linear-gradient(135deg, #b8a347, #d5bc5a);
}

.premium-btn-outline {
    background: transparent;
    border: 2px solid #d5bc5a;
    color: #d5bc5a;
}

.premium-btn-outline:hover {
    background: #d5bc5a;
    color: #fff;
}

/* Special Offer Card */
.special-offer {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 2px solid #d5bc5a;
}

.special-offer-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #d5bc5a, #b8a347);
    color: #fff;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.offer-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.offer-circle {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #d5bc5a, #b8a347);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #fff;
    box-shadow: 0 10px 30px rgba(213, 188, 90, 0.3);
}

.offer-text {
    font-size: 32px;
    font-weight: 700;
    line-height: 1;
}

.offer-circle small {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.premium-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* Welcome Offer Card */
.welcome-offer {
    background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
    border: 2px solid rgba(213, 188, 90, 0.2);
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 5px 20px rgba(231, 76, 60, 0.3);
}

.welcome-icon i {
    color: #fff;
    font-size: 32px;
}

.welcome-option {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

/* Newsletter form styling fixes */
.pa-newsletter .row .col-6 input {
    height: 50px;
    border-radius: 25px;
    border: none;
    padding: 0 20px;
    background: #fff;
}

.pa-newsletter .pe-2 {
    padding-right: 8px;
}

.pa-newsletter .ps-2 {
    padding-left: 8px;
}

.pa-newsletter .input-group input {
    border-radius: 25px !important;
    padding-right: 150px !important;
}

.welcome-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.option-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #d5bc5a, #b8a347);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.option-icon i {
    color: #fff;
    font-size: 20px;
}

.option-content h6 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.option-content p {
    color: #6c757d;
    margin-bottom: 0;
    font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .premium-card-header,
    .premium-card-body,
    .premium-card-footer {
        padding: 20px;
    }
    
    .premium-buttons {
        justify-content: center;
    }
    
    .premium-btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .offer-circle {
        width: 100px;
        height: 100px;
    }
    
    .offer-text {
        font-size: 24px;
    }
    
    .welcome-option {
        flex-direction: column;
        text-align: center;
    }
}
    66% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0);
    }
}

/* Banner two */
.pa-banner-two {
    padding: 89px 180px 184px;
    position: relative;
}

.pa-banner-two:after {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    width: 600px;
    content: '';
    background: #d5bc5a;
    z-index: -1;
    animation: 2s slideInFromRight;
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(+100%);
    }
    50% {
        transform: translateX(-5%);
    }
    100% {
        transform: translateX(0);
    }
}

.pa-banner-two-text h2 {
    font-size: 60px;
    margin: 20px 0 30px;
    font-weight: 600;
    text-transform: uppercase;
}

.pa-banner-two-text h4 {
    margin-bottom: 0;
    color: #d5bc5a;
    font-size: 22px;
    font-style: italic;
}

button.two-btn {
    border: 2px solid #fff;
}

.pa-banner-two-text p {
    margin-bottom: 30px;
    color: #9599A7;
}

.pa-banner-two-img {
    background: #fff;
    z-index: 0;
    position: relative;
    border-radius: 100%;
    width: 620px;
    height: 620px;
    text-align: center;
    line-height: 620px;
    animation: banner_popup 1s;
}

@keyframes banner_popup {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.pa-banner-two-img img {
    width: 450px;
}

.pa-banner-two .row {
    align-items: center;
}

.pa-banner-two-text {
    position: relative;
    margin-top: 47px;
}

.pa-banner-two-text:after {
    position: absolute;
    background-image: radial-gradient(hsla(86, 48%, 48%, 0.13) 3px, transparent 3px);
    background-size: 30px 30px;
    content: '';
    width: 300px;
    left: -60px;
    top: -60px;
    bottom: -70px;
    z-index: -1;
    animation: 3s installation infinite alternate;
}

@keyframes installation {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Fixed cart */
.pa-fixed-cart svg {
    width: 40px;
    fill: #d5bc5a;
}

.pa-fixed-cart {
    position: fixed;
    bottom: 130px;
    right: 30px;
    background: #fff;
    width: 80px;
    height: 80px;
    text-align: center;
    line-height: 80px;
    border-radius: 100%;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    animation: pa_cart 1s infinite alternate;
    cursor: pointer;
    z-index: 1;
}

.pa-fixed-cart span {
    background: #d5bc5a;
    min-width: 20px;
    height: 20px;
    position: absolute;
    text-align: center;
    line-height: 20px;
    font-size: 11px;
    border-radius: 100%;
    top: 0;
    right: 0;
    color: #ffffff;
    cursor: pointer;
    border: 1px solid #fff;
}

@keyframes pa_cart {
    0% {
        transform: translateY(5px);
    }
    30% {
        transform: translateY(-3px);
    }
    70% {
        transform: translateY(3px);
    }
    100% {
        transform: translateY(0);
    }
}

.pa-fixed-cart:hover {
    animation: none;
}

/* Header two */
.pa-main-header.pa-header-two {
    position: absolute;
    top: 25px;
    left: 0;
    right: 0;
    background: transparent;
    z-index: 1;
}

.pa-menu-two {
    position: fixed;
    right: 0;
    left: 0;
    top: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.83);
    z-index: 9;
    text-align: center;
    transition: 1s;
    height: 0;
    border-radius: 0 0 100% 100%;
    visibility: hidden;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    overflow-x: auto;
}

.pa-open-menu .pa-menu-two {
    width: 100%;
    height: 100%;
    border-radius: 0;
    opacity: 1;
    visibility: visible;
}

.pa-menu-two ul li {
    margin: 0 auto 35px;
    width: fit-content;
}

.pa-menu-two ul li a {
    font-size: 35px;
    color: #fff;
    text-transform: capitalize;
}

.pa-menu-two > ul {
    margin-top: 100px;
    visibility: hidden;
    opacity: 0;
}

.pa-open-menu .pa-menu-two > ul {
    opacity: 1;
    visibility: visible;
    transition: 1.5s;
    transition-delay: 1s;
}

ul.pa-submenu-two {
    margin-top: 30px;
}

.pa-menu-two ul li.pa-menu-tow-child {
    position: relative;
}

.pa-menu-two ul li.pa-menu-tow-child:after {
    position: absolute;
    content: '';
    right: -50px;
    top: 10px;
    border-left: 10px solid #fff;
    border-top: 10px solid transparent;
    transform: rotate(-45deg);
    cursor: pointer;
}

.pa-submenu-two {
    display: none;
}

.pa-toggle-nav2 {
    margin-left: 10px;
    vertical-align: middle;
    z-index: 10;
    position: relative;
    cursor: pointer;
}

.pa-toggle-nav2 span {
    display: block;
    width: 18px;
    height: 2px;
    background: #fff;
    margin-bottom: 4px;
    transition: 0.3s;
}

.pa-toggle-nav2 span:nth-child(3) {
    margin-bottom: 0;
}

.pa-open-menu .pa-toggle-nav2 span:nth-child(1) {
    transform: translate(1px, 6px) rotate(-48deg);
}

.pa-open-menu .pa-toggle-nav2 span:nth-child(2) {
    opacity: 0;
    visibility: hidden;
}

.pa-open-menu .pa-toggle-nav2 span:nth-child(3) {
    transform: translate(1px, -6px) rotate(48deg);
}

.pa-toggle-nav2,
.pa-head-icon > ul {
    display: inline-block;
}

.pa-header-two .pa-head-icon {
    text-align: right;
}

.pa-open-menu .pa-toggle-nav2 {
    position: fixed;
    top: 20px;
}

.pa-product-box.pa-product-box-222 {
    box-shadow: none;
    border: 1px solid #e4e4e4 !important;
    background-color: #ffffff !important;
}

/* Feature section */
.pa-feature ul li svg {
    width: 50px;
    fill: #d5bc5a;
}

.pa-feature ul {
    text-align: center;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    padding: 50px 10px 43px;
    border-radius: 10px;
    margin-top: -95px;
    background: #fff;
}

.pa-feature ul li {
    display: inline-block;
    width: 16%;
    border-right: 1px solid #eee;
    vertical-align: top;
}

.pa-feature ul li:last-child {
    border: none;
}

.pa-feature ul li a {
    font-size: 18px;
    margin-top: 20px;
    display: block;
    font-weight: 600;
}

.pa-feature ul li:hover svg {
    animation: pa_blink 0.7s;
}

@keyframes pa_blink {
    0% {
        transform: scale(0.9);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Product two */
.pa-product-two .pa-product-box {
    box-shadow: none;
    border: 1px dashed #f5f5f5;
    background-color: #f5f5f5;
}

/* Why pure section */
.pa-why-pure-shape ul li svg {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
    fill: #d5bc5a;
}

.pa-why-pure-shape ul li {
    background: #fff;
    border-radius: 10px;
    padding: 30px 30px 23px;
    margin-bottom: 30px;
    display: inline-block;
    text-align: center;
    transition: 0.3s;
}

.pa-why-pure-content {
    padding-left: 35px;
}

.pa-why-pure-shape ul li:hover {
    transform: translate(0, -5px);
}

.pa-why-pure-shape ul li:nth-child(3) {
    margin-top: 30px;
}

.pa-why-pure-shape ul {
    column-count: 2;
    column-gap: 30px;
}

.pa-why-pure-shape ul li p {
    margin: 0;
}

.pa-why-pure-shape ul li h3 {
    font-size: 23px;
    margin: 15px 0;
    font-weight: 700;
    text-transform: capitalize;
}

/* Reviews Page Styles */
.pa-review-box {
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    height: 100%;
    transition: transform 0.3s ease;
}

.pa-review-box:hover {
    transform: translateY(-5px);
}

.review-image img {
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.review-rating {
    color: #ffd700;
    margin-top: 5px;
}

.review-text {
    margin-top: 15px;
    font-style: italic;
    line-height: 1.6;
}

.pa-review-form {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
}

.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input {
    display: none;
}

.rating-input label {
    cursor: pointer;
    font-size: 25px;
    color: #ddd;
    margin: 0 5px;
}

.rating-input label:hover,
.rating-input label:hover ~ label,
.rating-input input:checked ~ label {
    color: #ffd700;
}
.pa-why-pure-content .pa-btn {
    margin-top: 20px;
}

/* Footer two */
.pa-footer-two {
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(https://dummyimage.com/1920x335/);
    background-size: cover;
    padding: 72px 0 42px;
}

/* Privacy Policy Styles */
.pa-privacy-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

.pa-privacy-content h2 {
    color: var(--primary-color);
    margin-bottom: 30px;
}

.pa-privacy-content h3 {
    color: var(--secondary-color);
    margin-top: 40px;
    margin-bottom: 20px;
    font-size: 1.4em;
}

.pa-privacy-content p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.pa-privacy-content ul {
    margin-bottom: 20px;
    padding-left: 20px;
}

.pa-privacy-content ul li {
    margin-bottom: 10px;
    line-height: 1.6;
}

.pa-privacy-content a {
    color: var(--primary-color);
    text-decoration: underline;
}

.pa-privacy-content a:hover {
    color: var(--secondary-color);
}

/* Submenu base styles */
ul.pa-submenu li a {
    color: #242424;
    padding: 8px 15px;
}

/* Business Hours table styles */
.table td {
    vertical-align: middle;
}

/* Checkbox container */
.table .form-check {
    width: fit-content;
    margin: 0 auto;
    padding: 0;
}

/* Checkbox input */
.form-check-input {
    padding: 0 !important;
    display: block !important;
}

/* Time input fields */
.time-input {
    width: 150px;
    border-radius: 4px;
    height: 36px;
    padding: 0.375rem 0.75rem;
}

/* Table cell alignment */
.table > tbody > tr > td {
    padding: 0.75rem;
    text-align: center;
}

/* Day name column */
.table > tbody > tr > td:first-child {
    min-width: 100px;
    text-align: left;
}

/* Active state for submenu items - text color change only */
ul.pa-submenu li.current-menu-item > a,
ul.pa-submenu li.active > a {
    color: #d5bc5a;
    font-weight: 500;
}

/* Hover state for submenu items */
ul.pa-submenu li a:hover {
    color: #d5bc5a;
}

/* Mobile submenu specific styles */
@media screen and (max-width: 991px) {
    .pa-menu-mobile-icons .pa-submenu li.current-menu-item > a,
    .pa-menu-mobile-icons .pa-submenu li.active > a {
        color: #d5bc5a;
        border-color: #d5bc5a;
    }
}

.pa-footer-two:before {
    position: absolute;
    top: 0;
    left: 0;
    background-image: linear-gradient(135deg, rgba(130, 180, 64, 0.8) 0%, rgba(130, 180, 64, 0.82) 100%);
    content: "";
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* Blog two */
.pa-blog-view.pa-blog-two-view {
    border-top: 0;
    border-bottom: 1px solid #eee;
}

/* Banner three */
.pa-banner-three {
    position: relative;
    overflow: hidden;
    background-image: radial-gradient(at right top, #d5bc5a 0%, #d5bc5a 75%);
    z-index: 0;
}

.pa-banner-three-text {
    padding: 80px 0 94px 150px;
    position: relative;
}

.pa-banner-three .col-lg-5 {
    align-items: center;
    display: flex;
}

.pa-banner-three-text:after {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
}

/* Admin page badges */
.pa-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.pa-badge-pending {
    background-color: #d5bc5a;
    color: #fff;
}

.pa-badge-processing {
    background-color: #6caaa8;
    color: #fff;
}

.pa-badge-completed {
    background-color: #82b440;
    color: #fff;
}

.pa-badge-cancelled {
    background-color: #d9534f;
    color: #fff;
}

/* Admin table styles */
.pa-cart-box {
    background: #ffffff;
    border-radius: 10px;
    padding: 20px;
}

.pa-cart-box table {
    margin-bottom: 0;
}

.pa-cart-box table th {
    border: none;
    padding: 15px;
    font-weight: 600;
}

.pa-cart-box table td {
    padding: 15px;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
}

/* Admin buttons */
.pa-btn-sm {
    padding: 5px 15px;
    font-size: 14px;
}

/* Admin search form */
.input-group .pa-btn {
    border-radius: 0 50px 50px 0;
}

.input-group .form-control {
    border-radius: 50px 0 0 50px;
    border: 1px solid #d5bc5a;
    border-right: none;
    padding: 10px 20px;
}

.pa-banner-three-text h1 {
    font-size: 48px;
    margin-bottom: 20px;
    color: #fff;
    text-transform: uppercase;
}

.pa-banner-three-text p {
    font-size: 18px;
    color: #fff;
    margin-bottom: 30px;
}

.pa-banner-three-text .pa-btn {
    background: #d5bc5a;
    color: #fff;
    padding: 12px 24px;
    border: none;
    border-radius: 50px;
    text-transform: uppercase;
    font-weight: 600;
    transition: all 0.3s ease;
}

.pa-banner-three-text .pa-btn:hover {
    background: #c4ac49;
}

.pa-banner-three-text .pa-btn:active {
    transform: translateY(2px);
}

.pa-banner-three-text .pa-btn:focus {
    outline: none;
}

/* Calendar icon styling */
.pa-menu ul li svg {
    width: 20px;
    height: 20px;
    fill: #F5F5DC;
    transition: 0.3s;
}

.pa-menu ul li a:hover svg {
    fill: #d5bc5a;
}

/* Wizard styles */
.wizard-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    position: relative;
    width: 100%;
    box-sizing: border-box;
}

.wizard-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: #e0e0e0;
    z-index: 1;
    transition: background 0.3s ease;
}

.wizard-progress .step {
    position: relative;
    z-index: 2;
    text-align: center;
    width: 33.333%;
    padding: 0 10px;
    box-sizing: border-box;
}

.step-icon {
    width: 40px;
    height: 40px;
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 50%;
    line-height: 36px;
    margin: 0 auto 10px;
    color: #888;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Active step styling */
.step.active .step-icon {
    background: #d5bc5a;
    border-color: #d5bc5a;
    color: #fff;
}

/* Completed step styling */
.step.completed .step-icon {
    background: #8bc34a;
    border-color: #8bc34a;
    color: #fff;
}

.step-label {
    font-size: 14px;
    color: #888;
}

.step.active .step-label {
    color: #d5bc5a;
}

.step.completed .step-label {
    color: #8bc34a;
}

/* User login submenu */
li.pa-user-login.pa-menu-child .pa-submenu {
    right: 0;
    left: auto;
    min-width: 180px;
    background: #fff;
    padding: 15px 20px;
    position: absolute;
    top: 49px;
    box-shadow: 0 10px 15px 0 rgba(0, 0, 0, 0.11);
    border-radius: 0 0 5px 5px;
    max-width: 250px;
    width: max-content;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
    transform: translateX(15px);
    z-index: 1;
}

li.pa-user-login.pa-menu-child .pa-submenu li {
    margin-bottom: 5px;
}

li.pa-user-login.pa-menu-child .pa-submenu li:last-child {
    margin-bottom: 0;
}

li.pa-user-login.pa-menu-child .pa-submenu li a {
    color: #404040;
    font-size: 14px;
}

/* Dropdown hover */
li.pa-user-login .dropdown:hover .dropdown-menu {
    display: block;
}

li.pa-user-login .dropdown-menu {
    margin-top: 0;
    display: none;
    background: #fff;
    border: none;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    min-width: 180px;
}

/* Cart layout */
.pa-cart-box {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
}

.pa-cart-box > table {
    flex: 1 1 70%;
    min-width: 0;
}

.pa-grand-total {
    flex: 0 0 25%;
    min-width: 250px;
    margin-left: auto;
}

/* Totals table */
.totals-table {
    width: 100%;
    border-collapse: collapse;
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.totals-table tr {
    border: 1px solid #eee;
}

.totals-table td {
    padding: 10px 15px;
    text-align: left;
    font-size: 14px;
    white-space: nowrap;
}

.totals-table td:last-child {
    text-align: right;
    color: #d5bc5a;
    font-weight: 600;
}

.totals-table .total-row {
    background-color: #f9f9f9;
    border-top: 2px solid #eee;
}

.totals-table .total-row td {
    font-size: 16px;
    font-weight: 600;
}

/* Wizard steps */
.wizard-step {
    display: none;
    animation: fadeIn 0.5s;
}

.wizard-step.active {
    display: block;
    opacity: 1;
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.wizard-container {
    position: relative;
    min-height: 400px;
}

.pa-booking .wizard-step {
    display: none !important;
}

.pa-booking .wizard-step.active {
    display: block !important;
}

.wizard-progress {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-bottom: 30px;
    counter-reset: step;
}

.wizard-progress .step {
    text-align: center;
    position: relative;
    z-index: 1;
    flex: 1;
}

.wizard-progress .step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    border: 2px solid #ddd;
    transition: all 0.3s ease;
}

.wizard-progress .step.active .step-icon {
    background-color: #d5bc5a;
    border-color: #d5bc5a;
    color: white;
}

.wizard-progress .step.completed .step-icon {
    background-color: #8bc34a;
    border-color: #8bc34a;
    color: white;
}

.wizard-progress .progress-bar {
    position: absolute;
    top: 20px;
    left: 20px; /* Adjusted to match padding */
    right: 20px; /* Adjusted to match padding */
    height: 2px;
    background-color: #ddd;
    width: auto; /* Changed from 100% to auto */
    z-index: 0;
}

.wizard-progress .step.active .step-label {
    font-weight: bold;
    color: #d5bc5a;
}

.wizard-progress .step.completed .step-label {
    color: #8bc34a;
}

/* Wizard container styling */
.wizard-step {
    display: none;
    animation: fadeIn 0.5s;
}

.wizard-step.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Booking summary and information box styling */
.sticky-cards-container {
    position: sticky;
    top: 20px;
    height: fit-content;
}

.information-card {
    margin-top: 20px;
}

@media (max-width: 767.98px) {
    .sticky-cards-container {
        position: static;
        margin-top: 30px;
    }
}

/* Rest of the shared styles */
.booking-summary,
.information-card {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    margin-bottom: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    background: #fff;
    border-bottom: 3px solid #d5bc5a;
}

.booking-summary .card-header,
.information-card .card-header {
    background-color: #f8f9fa;
    padding: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.booking-summary .card-header h3,
.information-card .card-header h3 {
    color: #111111;
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 600;
    text-transform: none;
}

.booking-summary .card-body,
.information-card .card-body {
    padding: 20px;
}

.information-card .card-body p {
    margin-bottom: 15px;
    color: #242424;
}

.information-card .card-body p:last-child {
    margin-bottom: 0;
}

.information-card .card-body i {
    color: #d5bc5a;
    width: 20px;
    margin-right: 8px;
}

.information-card .card-body a {
    color: #d5bc5a;
    text-decoration: none;
    transition: color 0.3s ease;
}

.information-card .card-body a:hover {
    color: #c4ac49;
}

/* Button styling */
.wizard-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.pa-btn {
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pa-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.pa-btn:active {
    transform: translateY(1px);
}

/* Form validation styling */
.form-control.error {
    border-color: #dc3545 !important;
    border-width: 1px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
    margin-bottom: 0;
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 6px;
    margin-bottom: 10px;
    padding-left: 16px;
    font-size: 14px;
    color: #dc3545;
}

/* Form group spacing */
.form-group {
    margin-bottom: 15px; /* Adjust overall spacing between form groups */
    position: relative;
}

/* Show feedback when field has error */
.form-control.error + .invalid-feedback {
    display: block;
}

/* Add warning icon before error message */
.invalid-feedback::before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 1px;
    font-size: 12px;
}

/* Form group spacing */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

/* Ensure proper spacing when error is shown */
.form-group.has-error {
    margin-bottom: 30px;
}

/* Make sure error messages don't push layout */
.form-group .invalid-feedback {
    position: absolute;
    top: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .invalid-feedback {
        font-size: 13px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
}

/* Accessibility improvements */
.form-select:focus,
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Booking form */
.booking-form {
    padding: 30px;
    background: #f9f9f9;
    border-radius: 8px;
}

.booking-form .form-group {
    margin-bottom: 20px;
}

.booking-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.booking-form select,
.booking-form input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.booking-form .submit-btn {
    background: #d5bc5a;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.booking-form .submit-btn:hover {
    background: #c4ac49;
}

.booking-summary {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-bottom: 3px solid #d5bc5a;
    margin-top: 20px;
}

.booking-summary h3 {
    color: #111111;
    font-size: 22px;
    margin-bottom: 15px;
    font-weight: 600;
}

.booking-summary p {
    margin: 10px 0;
    color: #242424;
}

.booking-summary strong {
    color: #111111;
}

/* Cart count */
.cart-count {
    display: inline-block;
    background: #d5bc5a;
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    position: absolute;
    top: -10px;
    right: -10px;
}

.pa-cart-grand-total {
    width: 25%;
    max-width: 250px;
    float: right;
}

.cart-count:empty,
.cart-count[style*="0"] {
    display: none;
}

/* Blog widget */
.pa-blog-widget {
    display: flex;
    border-bottom: 1px solid #eee;
    margin-bottom: 25px;
    padding-bottom: 20px;
}

.pa-blog-widget:last-child {
    border: none;
    padding: 0;
    margin-bottom: 22px;
}

.pa-blog-widget .pa-blog-title {
    padding: 0 0 0 20px;
    width: calc(100% - 100px);
}

.pa-blog-wimg {
    width: 100px;
}

.pa-blog-widget h2 {
    font-size: 18px;
    line-height: 24px;
}

.pa-blog-wimg img {
    border-radius: 10px;
}

/* Medicine icon */
.pa-medi-icon svg {
    width: 50px;
    height: 50px;
    fill: #d5bc5a;
}

.pa-medi-icon {
    width: 100px;
    height: 100px;
    margin: 10px auto;
    line-height: 100px;
    border-radius: 100%;
    box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.05);
    display: inline-block;
}

/* Footer three */
.pa-footer-three {
    background-image: linear-gradient(135deg, #d5bc5a 0%, #d5bc5a 100%);
    background-size: cover;
    padding: 72px 0 42px;
    background-position: center;
}

.pa-copyright.pa-copyright-two {
    background-image: linear-gradient(135deg, #d5bc5a 0%, #d5bc5a 100%);
}

/* Profile */
.pa-profile-box {
    box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.07);
    border-radius: 10px;
    padding: 30px;
    max-width: 600px;
    margin: auto;
}

.pa-profile-img {
    margin-bottom: 22px;
    display: flex;
}

.pa-profile-img img {
    border-radius: 100%;
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.pa-profile-data ul li {
    display: flex;
    margin-bottom: 22px;
    border-bottom: 1px solid #eee;
    padding-bottom: 23px;
}

.pa-profile-data ul li p {
    flex: 1;
    margin-bottom: 0;
}

/* reCAPTCHA styling */
.g-recaptcha {
    margin-bottom: 20px;
}

@media screen and (max-width: 400px) {
    .g-recaptcha {
        transform: scale(0.85);
        transform-origin: left center;
    }
}

.pa-profile-data .pa-btn {
    margin-top: 10px;
}

.pa-profile-data ul li:last-child {
    border: none;
    padding: 0;
    margin: 0;
}

.pa-profile-userimg {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border: 2px solid #d5bc5a;
    position: relative;
}

.pa-profile-userimg svg {
    width: 80%;
    height: 80%;
    fill: #d5bc5a;
    opacity: 0.9;
}

.pa-profile-name-box {
    width: calc(100% - 100px);
    padding-left: 30px;
}

.pa-profile-svg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s;
}

.pa-profile-svg svg {
    width: 25px;
    height: 25px;
    fill: #ffffff;
    z-index: 1;
    cursor: pointer;
    transition: 0.3s;
}

.pa-profile-svg input {
    width: 105px;
    z-index: 2;
    opacity: 0;
    transform: translate(0, -30px);
    cursor: pointer;
    height: 145px;
    border-radius: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.pa-profile-userimg:hover .pa-profile-svg {
    opacity: 1;
    visibility: visible;
}

.pa-profile-svg:after {
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: rgba(149, 180, 148, 0.63);
    border-radius: 100%;
}

.pa-profile-name,
.pa-edit-model .modal-title {
    font-size: 20px;
}

.pa-profile-name span {
    padding-left: 12px;
    font-weight: 600;
    color: #d5bc5a;
}

.pa-profile-name-box p {
    margin-bottom: 0;
}

.pa-edit-model ul li {
    display: flex;
    margin-bottom: 20px;
}

.pa-edit-model ul li:last-child {
    margin-bottom: 0;
}

.pa-edit-model ul li p {
    width: 280px;
}

.pa-edit-model ul li textarea {
    width: 100%;
    height: 90px;
    padding: 10px 20px;
    border: 1px solid rgba(130, 180, 64, 0.48);
    outline: none;
    border-radius: 10px;
    resize: none;
    line-height: normal;
}

.pa-edit-model .modal-body {
    padding: 30px;
}

.pa-edit-model {
    border-radius: 10px;
}

.pa-edit-model .modal-footer {
    padding: 26px;
}

.pa-edit-model .modal-header {
    padding: 22px 30px 23px;
}

.btn-close:focus {
    box-shadow: none;
}

/* Login */
.pa-login-close {
    position: absolute;
    top: -10px;
    right: -10px;
    opacity: 1;
    z-index: 5;
    outline: none;
}

.pa-login-close span {
    background: #6caaa8;
    width: 30px;
    height: 30px;
    display: block;
    border-radius: 100%;
    line-height: 30px;
    color: #fff;
}

.close:not(:disabled):not(.disabled):focus,
.close:not(:disabled):not(.disabled):hover {
    opacity: 1;
    outline: none;
}

.pa-login-form input {
    margin-bottom: 20px;
}

.pa-login-title {
    margin-bottom: 24px;
    font-size: 32px;
    font-weight: 700;
    text-align: center;
}

.pa-login-model .modal-body {
    padding: 41px 50px 50px;
}

.pa-login-model form {
    margin-bottom: 0;
}

.pa-remember input {
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
    margin-bottom: 0;
}

.pa-remember .s_checkbox {
    position: absolute;
    top: 4px;
    left: 0;
    border: 1px solid rgb(233, 237, 250);
    border-radius: 4px;
    background-color: rgb(248, 249, 253);
    box-shadow: 0 0 40px 0 rgba(0, 0, 0, 0.04);
    width: 17px;
    height: 17px;
}

.pa-remember {
    margin: 15px 0 27px;
    position: relative;
}

.pa-remember .s_checkbox:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid #d5bc5a;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}

.pa-remember input:checked ~ .s_checkbox:after {
    display: block;
}

.pa-remember .s_checkbox:after {
    content: "";
    position: absolute;
    display: none;
}

.pa-remember label {
    padding-left: 30px;
    cursor: pointer;
}

.pa-login-btn {
    position: relative;
}

.pa-forgot-password,
.pa-login-btn p {
    position: absolute;
    right: 0;
    top: 0;
}

.pa-login-btn p {
    margin-bottom: 0;
    bottom: 0;
    display: flex;
    align-items: center;
}

.pa-login-btn p a {
    color: #d5bc5a;
    padding-left: 5px;
    font-weight: 600;
}

.pa-login-model .modal-content {
    border: none;
    border-radius: 10px;
}

.modal.pa-login-model {
    overflow-y: auto;
}

/* Before image */
.pa-before-img {
    position: relative;
    bottom: -189px;
    overflow: hidden;
}

.pa-before-img svg {
    fill: #fff;
    transform-origin: center;
    transform: rotateY(0deg);
    width: calc(125% + 1.3px);
}

/* View all button */
.pa-view-all {
    font-size: 18px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    line-height: 1;
    background-color: #d5bc5a;
    font-size: 15px;
    padding: 12px 24px;
    border-radius: 30px;
    color: #fff;
    border: 1px solid transparent;
    transition: all 0.3s;
}

.pa-view-all:hover {
    color: #fff;
    background-color: white;
    border: 1px solid #d5bc5a;
    color: #d5bc5a;
}

/* Counter box */
.pa-counter.spacer-top.spacer-bottom.pa-count-box {
    background-image: url(https://dummyimage.com/1920x768/);
    background-position: bottom;
}

.pa-counter.spacer-top.spacer-bottom.pa-count-box::before {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.51);
    top: 0;
    left: 0;
    right: 0;
    z-index: -1;
}

.pa-heading.pa-benefit-heading h1 {
    color: #fff;
}

.pa-copyright:before {
    position: absolute;
    content: "";
    width: 70%;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.43);
    left: 0;
    right: 0;
    margin: 0 auto;
    top: 0;
}

.pa-tesimonial.spacer-top.spacer-bottom.pa-swiper-wrapper::before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.pa-pure-text-00 {
    text-align: center;
    margin-bottom: 30px;
    background: #d5bc5a;
    padding: 20px;
}

.pa-pure-text-00 h2 {
    font-size: 23px;
    font-weight: 700;
    color: #fff;
    margin-top: 20px;
}

.pa-pure-text-00 p {
    max-width: 290px;
    width: 100%;
    margin: 0 auto;
    color: #fff;
}

.pa-banner-img img {
    width: 100%;
}

.pa-heading.pa-benefit-heading.pa-choose-us {
    text-align: start;
}

.pa-team.pa-team-home.spacer-top {
    background-image: none;
}

.pa-blog-box.pa-three-blog {
    display: block;
    box-shadow: 0 30px 60px 0 rgba(0, 0, 0, 0.1);
}

.pa-blog-box.pa-three-blog .pa-blog-title {
    padding: 21px 0 0 15px;
}

.pa-blog-box.pa-three-blog img {
    width: 100%;
}

.pa-pure-img img {
    width: 100%;
}

.pa-head-icon.pa-about-icon ul li svg {
    fill: #ffffff;
}

.pa-widget.pa-tag ul li a:hover {
    background-color: #d5bc5a;
    color: #ffffff;
}

.pa-blog-box.pa-blog-single-img {
    display: block;
}

.pa-blog-box.pa-blog-single-img .pa-blog-title {
    padding: 20px 0 0;
}

.btn-close {
    position: absolute;
    right: 23px;
    top: 19px;
    z-index: 10;
}

.pa-icone-call {
    display: flex;
    justify-content: start;
    align-items: center;
}

.pa-box-icon i {
    font-size: 40px;
    color: #ffffff;
    padding-right: 26px;
}

.pa-box-title a {
    font-size: 20px;
    color: #fff;
}

.pa-box-contect p {
    font-size: 35px;
    font-weight: 500;
    padding-top: 10px;
}

li.pa-list-item i, li.pa-list-item span i {
    color: #d5bc5a;
    padding-right: 6px;
}

/* Specific positioning for related products slider */
.pa-related-product .swiper-button-prev {
    left: 20px;  
}

.pa-related-product .swiper-button-next {
    right: 20px; 
}

/* Adjust for mobile screens */
@media (max-width: 575px) {
    .pa-related-product .swiper-button-prev {
        left: 10px; 
    }
    
    .pa-related-product .swiper-button-next {
        right: 10px; 
    }
}
span.pa-box-text {
    font-size: 20px;
    font-weight: 400;
}

.pa-check-item {
    padding-top: 20px;
    display: flex;
    justify-content: start;
    align-items: center;
    flex-wrap: wrap;
}

ul.pa-flex-item {
    padding-right: 55px;
}

li.pa-list-item {
    padding-bottom: 5px;
}

.blog_pagination_wrapper ul li {
    display: inline-block;
    font-size: 18px;
}

.blog_pagination_wrapper ul li a, .blog_pagination_wrapper ul li.dot:hover a {
    font-weight: 700;
    width: 40px;
    height: 40px;
    line-height: 42px;
    border-radius: 100%;
    display: inline-block;
    text-align: center;
    background: transparent;
}

.blog_pagination_wrapper ul li.blog_page_prev a, .blog_pagination_wrapper ul li.blog_page_next a {
    width: auto;
    height: 40px;
}

.blog_pagination_wrapper ul li.active a, .blog_pagination_wrapper ul li:hover a {
    color: #ffffff;
    background: #d5bc5a;
}

.blog_pagination_wrapper ul li.blog_page_prev:hover a, .blog_pagination_wrapper ul li.blog_page_next:hover a {
    background: transparent;
}

/* Hide desktop menu on mobile */
@media screen and (max-width: 991px) {
    .pa-head-icon > ul {
        display: none !important;
    }
    
    /* Show mobile menu icons */
    .pa-menu-mobile-icons {
        display: block !important;
    }
}

/* Show desktop menu on larger screens */
@media screen and (min-width: 992px) {
    .pa-menu-mobile-icons {
        display: none !important;
    }
    
    .pa-head-icon > ul {
        display: inline-block !important;
    }
}
    color: #d5bc5a;
}

.blog_pagination_wrapper {
    padding-top: 35px;
}

.pa-contact-box span svg {
    height: 60px;
    width: 60px;
    fill: #d5bc5a;
    margin-bottom: 15px;
}

.pa-blog-view.pa-m0 {
    padding-left: 0;
}

.pa-product-img {
    position: relative;
    background-color: #ecefe9;
    padding: 30px 20px;
}

/* Go to top button */
.go_top {
    position: fixed;
    bottom: 50px;
    right: 30px;
    cursor: pointer;
    z-index: 999;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.15) 0 0 20px;
    text-align: center;
}

.go_top.item-bounce img {
    width: 40px;
}

.item-bounce {
    animation: bounce 3s infinite ease-in-out;
}

@keyframes bounce {
    0% {
        transform: translateY(-5px);
    }
    50% {
        transform: translateY(10px);
    }
    100% {
        transform: translateY(-5px);
    }
}

.char-count {
    color: #6c757d;
    font-size: 0.875rem;
}

.char-count.warning {
    color: #ffc107;
}

.char-count.danger {
    color: #dc3545;
}

.form-group {
    position: relative;
    margin-bottom: 1rem;
}

.invalid-feedback {
    display: none;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.error .invalid-feedback {
    display: block;
}

.error input,
.error textarea {
    border-color: #dc3545;
}

/* Responsive styles */
@media screen and (max-width: 1399px) {
    .pa-main-header,
    .pa-top-header {
        padding: 0 100px;
    }

    .pa-banner-two {
        padding: 89px 80px 184px;
    }

    .pa-banner-two-img {
        width: 488px;
        height: 488px;
        line-height: 488px;
    }

    .pa-banner-two-img img {
        width: 330px;
    }

    .pa-banner-two:after {
        width: 450px;
    }

    .pa-banner-three-text {
        padding: 65px 0 79px 100px;
    }

    .pa-banner-three-img {
        padding: 80px 100px 80px 0;
    }
}

@media screen and (max-width: 1199px) {
    .pa-main-header,
    .pa-top-header {
        padding: 0 80px;
    }

    .pa-banner {
        padding: 88px 0 75px;
    }

    .pa-why-img {
        width: 320px;
        height: 320px;
        line-height: 320px;
        margin-top: 15px;
    }

    .pa-why-ul ul li {
        margin-bottom: 53px;
    }

    .pa-banner-two-img {
        width: 450px;
        height: 450px;
        line-height: 450px;
    }

    .pa-banner-two-img img {
        width: 330px;
    }

    .pa-banner-two:after {
        width: 300px;
    }

    .pa-banner-two-text h2 {
        font-size: 36px;
    }

    .pa-banner-three-text h1 {
        font-size: 35px;
        line-height: 45px;
    }
}

@media screen and (max-width: 1035px) {
    .pa-banner-three-text {
        padding: 128px 0 79px 100px;
    }

    .pa-banner-three-img {
        padding: 130px 100px 80px 0;
    }
}

@media screen and (min-width: 992px) {
    ul.pa-submenu {
        background: #fff;
        padding: 15px 20px;
        position: absolute;
        top: 49px;
        left: 0;
        box-shadow: 0 10px 15px 0 rgba(0, 0, 0, 0.11);
        min-width: 120px;
        border-radius: 0 0 5px 5px;
        max-width: 250px;
        width: max-content;
        opacity: 0;
        visibility: hidden;
        transition: 0.3s;
        transform: translateX(15px);
        z-index: 1;
    }

    ul.pa-submenu li:last-child {
        margin-bottom: 0;
    }

    ul.pa-submenu li {
        margin-bottom: 5px;
    }

    li.pa-menu-child:hover ul.pa-submenu {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .pa-menu > ul > li:last-child ul.pa-submenu {
        left: auto;
        right: 0;
    }
}

@media screen and (max-width: 991px) {
    .pa-menu {
        padding-bottom: 20px;
    }
    
    .pa-menu-mobile-icons {
        display: block !important;
        padding: 15px;
        border-top: 1px solid #eee;
        margin-top: 20px;
    }
    
    .pa-menu-mobile-icons ul {
        display: flex;
        justify-content: space-around;
    }
    
    .pa-menu-mobile-icons li.pa-user-login, 
    .pa-menu-mobile-icons li.pa-cart-icon {
        margin-right: 0;
    }

    .pa-menu-mobile-icons .pa-cart-icon .cart-count {
        background: #d9534f;
        min-width: 16px;
        height: 16px;
        position: relative;
        text-align: center;
        line-height: 16px;
        font-size: 11px;
        border-radius: 100%;
        top: -20px;
        right: -12px;
        color: #ffffff;
        cursor: pointer;
    }
    
    .pa-menu-mobile-icons .pa-submenu {
        display: block;
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        background: transparent;
        padding: 0;
        width: 100%;
        text-align: center;
        margin-top: 15px;
    }

    .pa-menu-mobile-icons li.pa-user-login {
        position: relative;
        flex-direction: column;
        display: flex;
        align-items: center;
    }

    .pa-menu-mobile-icons .pa-submenu li {
        display: block;
        margin: 10px 0;
        width: 100%;
    }

    .pa-menu-mobile-icons .pa-submenu li a {
        color: #404040;
        font-size: 14px;
        padding: 8px 15px;
        border-radius: 4px;
        background: #f5f5f5;
        display: block;
        text-align: center;
        border: 1px solid #eee;
    }
    
    .pa-menu-mobile-icons .pa-submenu li a:hover {
        background: #d5bc5a;
        color: #fff;
        border-color: #d5bc5a;
    }

    .pa-menu-mobile-icons .logged-in .pa-submenu {
        display: block;
    }

    .pa-head-icon {
        display: flex;
    }
    
    .pa-head-icon > ul > li {
        display: none;
    }

    .pa-main-header,
    .pa-top-header {
        padding: 0;
    }

    .pa-menu {
        position: fixed;
        top: 0;
        bottom: 0;
        left: -270px;
        width: 260px;
        background: #fff;
        z-index: 9;
        transition: 0.3s;
        overflow-y: auto;
    }

    .pa-menu > ul li {
        display: block;
        margin: 0;
        border-bottom: 1px solid #eee;
    }

    .pa-menu > ul > li a {
        color: #222;
        padding: 10px 15px;
        display: block;
    }

    ul.pa-submenu li {
        padding-left: 20px;
    }

    .pa-menu > ul li:last-child {
        border-bottom: 0;
    }

    ul.pa-submenu li:first-child {
        border-top: 1px solid #eee;
    }

    .pa-submenu,
    .pa-banner-two:after,
    .pa-why-ul ul li:after,
    .pa-banner-three-text:after {
        display: none;
    }

    .pa-toggle-nav {
        margin-left: 10px;
        vertical-align: middle;
    }

    .pa-toggle-nav span {
        display: block;
        width: 18px;
        height: 2px;
        background: #fff;
        margin-bottom: 4px;
        transition: 0.3s;
    }

    .pa-toggle-nav span:nth-child(3),
    .col-md-6:last-child .pa-medicine-box {
        margin-bottom: 0;
    }

    .pa-open-menu .pa-toggle-nav span:nth-child(1) {
        transform: translate(1px, 6px) rotate(-48deg);
    }

    .pa-open-menu .pa-toggle-nav span:nth-child(2) {
        opacity: 0;
        visibility: hidden;
    }

    .pa-open-menu .pa-toggle-nav span:nth-child(3) {
        transform: translate(1px, -6px) rotate(48deg);
    }

    .pa-toggle-nav,
    .pa-head-icon > ul {
        display: inline-block;
    }

    .pa-open-menu .pa-menu {
        left: 0;
    }

    .pa-banner {
        background-position: right;
    }

    .pa-medicine-box,
    .pa-blog-widget:last-child,
    .pa-about-img {
        margin-bottom: 30px;
    }

    .pa-why-img {
        width: 210px;
        height: 210px;
        line-height: 210px;
        margin-top: 30px;
    }

    .pa-why-img img {
        width: 100px;
    }

    .pa-why-ul ul li,
    .pa-why-left ul li:first-child,
    .pa-why-left ul li:nth-child(2),
    .pa-why-left ul li:nth-child(4),
    .pa-why-left ul li:last-child,
    .pa-why-right ul li:first-child,
    .pa-why-right ul li:nth-child(2),
    .pa-why-right ul li:nth-child(4),
    .pa-why-right ul li:last-child {
        padding: 0;
    }

    .pa-why .pr-0,
    .pa-why .p-0 {
        padding-right: 15px !important;
    }

    .pa-why .pl-0,
    .pa-why .p-0 {
        padding-left: 15px !important;
    }

    .pa-why-left ul li {
        text-align: left;
    }

    .pa-why-right ul li {
        text-align: right;
    }

    .pa-why-ul ul li {
        margin-bottom: 35px;
    }

    .pa-counter-box p {
        line-height: normal;
    }

    .pa-counter.spacer-bottom {
        padding-bottom: 50px;
    }

    .pa-counter-main {
        margin-bottom: 0;
    }

    .pa-counter-box {
        margin-bottom: 30px;
        padding: 30px 30px 26px;
    }

    .pa-counter {
        margin-bottom: 80px;
    }

    .pa-nav-bar {
        padding-top: 8px;
    }

    .pa-toggle-nav2 span,
    .pa-head-three .pa-toggle-nav span {
        background: #95b494;
    }

    .pa-header-two .pa-head-icon ul li svg {
        fill: #ffffff;
    }

    .pa-banner-two-img {
        border: 1px dashed #ffffff;
        background: transparent;
    }

    .pa-banner-two-text {
        margin: 42px 0 80px;
    }

    .pa-feature ul {
        margin-top: 0;
        padding-bottom: 13px;
    }

    .pa-banner-two {
        padding: 86px 0 80px;
    }

    .pa-banner-two-text:after {
        background-size: 24px 24px;
        width: 200px;
        left: -20px;
        top: -30px;
        bottom: -30px;
    }

    .pa-why-pure-shape,
    .pa-product-sidebar,
    .pa-services-content,
    .pa-blog-sidebar {
        margin-top: 30px;
    }

    .pa-feature ul li {
        width: 32%;
        margin-bottom: 30px;
    }

    .pa-feature ul li:nth-child(3) {
        border: none;
    }

    .pa-open-menu .pa-toggle-nav2 span {
        background: #fff;
    }

    .pa-open-menu .pa-toggle-nav2 {
        right: 20px;
    }

    .pa-menu-two ul li a {
        font-size: 25px;
    }

    .pa-menu-two ul li.pa-menu-tow-child:after {
        top: 5px;
        border-left-width: 8px;
        border-top-width: 8px;
    }

    .pa-banner-three-text {
        padding: 0;
    }

    .pa-banner-three-img {
        padding: 0;
        text-align: center;
    }

    .pa-banner-three {
        padding: 80px 0;
    }

    .pa-plan-box {
        transform: translate(0);
    }

    .pa-popular-plan .pa-plan-price {
        font-size: 50px;
    }

    .pa-plan-box:hover {
        transform: translateY(-5px);
    }

    .pa-about {
        padding-bottom: 73px;
    }

    .pa-why {
        padding-bottom: 74px;
    }

    .pa-product-single .pa-product-sidebar {
        margin-top: 15px;
    }

    .pa-blog-single {
        padding-bottom: 55px;
    }

    .pa-garnd-total p span:last-child {
        min-width: 90px;
    }

    .pa-bill-form {
        margin-bottom: 46px;
    }

    label.pa-bill-title {
        margin-bottom: 28px;
    }

    p.pa-bill-title {
        margin-bottom: 36px;
    }

    .pa-contact-form {
        transform: translate(-60px, 36px);
    }

    .pa-img-00 {
        margin-bottom: 30px;
    }

    .pa-toggle-nav.pa-toggle02 span {
        background-color: #000000;
    }

    .pa-toggle-nav.pa-three-toggle span {
        background-color: #ffffff;
    }
}

.pa-newsletter {
    position: relative;
    margin-bottom: 25px;
}

.pa-newsletter .pa-btn {
    position: absolute;
    border-radius: 25px !important;
}

li.pa-user-login:hover,
li.pa-cart-icon:hover,
li.pa-user-login.logged-in:hover {
    background-color: #000;
    right: 5px;
    top: 5px;
    height: 40px;
    line-height: 40px;
    min-width: auto;
    padding: 0 20px;
    background: #fff;
    color: #d5bc5a;
    border: none;
    font-size: 14px;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.pa-newsletter .pa-btn:hover {
    background: #000;
    border:2px solid #000;
    color: #fff;
}

.pa-newsletter input {
    padding-right: 150px;
    border: none;
    height: 50px;
    border-radius: 25px;
    background: #fff;
}

.pa-foot-subscribe {
    margin-top: 0;
}

.pa-menu.pa-three-nav > ul > li > a {
    color: #000 !important;
}

.pa-cart-box {
    flex-direction: column;
}

.pa-grand-total {
    flex: 0 0 250px;
    margin-left: auto;
    margin-right: 0;
}

@media screen and (max-width: 767px) {
    .pa-header-address,
    .pa-header-call {
        text-align: center;
    }

    .pa-top-header {
        margin: 5px 0;
        height: auto;
    }

    .pa-banner {
        padding: 68px 0 53px;
    }

    .pa-blog-box img {
        width: 100%;
    }

    .pas-testi-content {
        width: calc(100% - 200px);
    }

    .pas-testi-img {
        width: 200px;
    }

    .pa-counter-box h1,
    .pa-counter-box span {
        font-size: 25px;
    }

    .pa-counter {
        margin-bottom: 60px;
    }

    .spacer-bottom,
    .pa-product-single,
    .pa-contact {
        padding-bottom: 60px;
    }

    .spacer-top {
        padding-top: 60px;
    }

    .pa-why .col-md-4:nth-child(2) {
        order: -1;
    }

    .pa-why-img {
        margin: 0 auto 30px;
    }

    .pa-product,
    .pa-blog,
    .pa-why-pure,
    .pa-counter.spacer-bottom,
    .pa-team,
    .pa-price-plan {
        padding-bottom: 30px;
    }

    .pa-trending-product {
        padding-bottom: 45px;
    }

    .pa-services {
        padding-bottom: 53px;
    }

    .pa-footer,
    .pa-footer-two,
    .pa-footer-three {
        padding: 52px 0 23px;
    }

    .pa-why {
        padding-bottom: 54px;
    }

    .pa-feature ul li {
        width: 49%;
    }

    .pa-feature ul li:nth-child(2),
    .pa-feature ul li:nth-child(4) {
        border: none;
    }

    .pa-feature ul li:nth-child(3) {
        border-right: 1px solid #eee;
    }

    .pa-banner-two-text:after {
        display: none;
    }

    .pa-banner-two {
        padding: 65px 0 60px;
    }

    .pa-banner-two-text {
        margin: 42px 26px 50px;
    }

    .pa-feature ul {
        padding: 40px 10px 3px;
    }

    .pa-foot-title,
    .pa-foot-box img {
        margin-bottom: 20px;
    }

    .pa-login-title {
        font-size: 25px;
    }

    .pa-banner-three {
        padding: 48px 0 60px;
    }

    .pa-banner-three-text {
        margin-bottom: 50px;
    }

    .pa-about {
        padding-bottom: 53px;
    }

    .pa-blog-page {
        padding-bottom: 55px;
    }

    .pa-prod-count {
        margin: 23px 0 22px;
    }

    .pa-cart-box table td {
        max-width: 100%;
        border: none;
        border-bottom: 1px solid #eee;
    }

    .pa-cart-box table td:last-child,
    .pa-cart-box table thead tr,
    .pa-bill-detail table td,
    .pa-bill-detail table td:last-child,
    .pa-cart-box table th,
    .pa-cart-box table th:last-child {
        border: none;
    }

    .pa-cart-box table tr,
    .pa-bill-detail table tr {
        display: flex;
        flex-direction: column;
        width: 100%;
        margin-bottom: 20px;
        border: 1px solid #eee;
        border-radius: 10px;
    }

    .pa-cart-box table th:first-child,
    .pa-bill-detail table th:first-child {
        border-radius: 10px 10px 0 0;
    }

    td.pa-cart-update,
    td.pa-cart-total,
    table.pa-garnd-total td:first-child,
    .pa-garnd-total {
        text-align: left;
    }

    .pa-garnd-total p:first-child {
        border-top: 1px solid #eee;
        border-radius: 10px 10px 0 0;
    }

    .pa-cart-box table th:last-child,
    .pa-bill-detail table th:last-child {
        border-radius: 0 0 10px 10px;
    }

    .pa-cart-box table th,
    .pa-bill-detail table th,
    .pa-bill-detail table td {
        border-bottom: 1px solid #eee;
    }

    .pa-bill-detail table th {
        border-right: none;
    }

    .pa-place-order-btn {
        text-align: left;
        margin-top: 10px;
    }

    .pa-checkout {
        padding-top: 56px;
    }

    .pa-contact-detail {
        padding-bottom: 30px;
    }

    .pa-banner-three .pa-contact-form {
        transform: translate(0, 0);
        margin-top: 30px;
    }

    .pa-banner-three-text {
        padding: 61px 20px 0;
    }

    .pa-pure-img {
        margin-bottom: 45px;
    }

    .pa-blog-box {
        flex-wrap: wrap;
    }

    .pa-banner-img {
        padding: 57px 0 0;
    }

    .pa-contact-form {
        transform: translate(4px, 36px);
    }
}

@media screen and (max-width: 795px) {
    .pa-banner-three-text {
        padding: 60px 0 0 34px;
    }
}

@media screen and (max-width: 576px) {
    .pa-grand-total {
        flex: 0 0 100%;
        min-width: 0;
    }
    
    .totals-table {
        width: 250px;
        margin-left: auto;
    }
}

@media screen and (max-width: 575px) {
    .pa-why-right ul li,
    .pa-why-left ul li {
        text-align: center;
        border-bottom: 1px solid #e5ecec;
        padding-bottom: 20px !important;
        margin-bottom: 20px;
    }

    .pa-why-right ul li:last-child {
        border-bottom: none;
        padding-bottom: 0 !important;
    }

    .pa-why-right ul {
        margin-top: 30px;
    }

    .pa-tesimonial-box {
        padding: 0 15px;
        display: block;
    }

    .pas-testi-content {
        width: 100%;
        padding-top: 20px;
        padding-left: 0;
    }

    .pa-tesimonial-box:after,
    .pas-testi-img:after {
        display: none;
    }

    .pa-tesimonial-button {
        right: 15px;
        bottom: 18px;
    }

    .pas-testi-content p {
        margin-bottom: 2rem;
    }

    .swiper-button-prev {
        left: 5px;
    }

    .swiper-button-next {
        right: 5px;
    }

    .pa-newsletter input {
        padding-right: 145px;
    }

    .pa-newsletter .pa-btn {
        padding: 0 15px;
        min-width: 135px;
    }

    .pa-login-btn p {
        position: unset;
        padding-top: 10px;
        display: block;
    }

    .pa-forgot-password {
        position: unset;
        padding-left: 30px;
        display: block;
    }

    .pa-login-model .modal-body {
        padding: 21px 30px 30px;
    }

    .pa-remember {
        margin: 5px 0 17px;
    }

    .pa-login-close {
        top: -5px;
        right: -5px;
    }

    .pa-feature ul li a {
        font-size: 16px;
        font-weight: 400;
    }

    .pa-banner-two-img {
        width: auto;
        height: auto;
        line-height: unset;
    }

    .pa-banner-two-img img {
        width: 100%;
    }

    .pa-fixed-cart {
        width: 70px;
        height: 70px;
        line-height: 70px;
    }

    .pa-fixed-cart svg {
        width: 30px;
    }

    .pa-copyright p,
    .pa-copyright p a {
        line-height: normal;
    }

    .pa-menu-two ul li a {
        font-size: 20px;
    }

    .pa-blog-wimg {
        width: 70px;
    }

    .pa-blog-widget .pa-blog-title {
        width: calc(100% - 70px);
    }

    .pa-services-content ul {
        column-count: 1;
    }

    .pa-prod-content {
        margin-top: 22px;
    }
}

@media screen and (max-width: 480px) {
    .pa-banner-text h2 {
        font-size: 36px;
    }

    .pa-why-pure-shape ul {
        column-count: 1;
        column-gap: 0;
    }

    .pa-why-pure-shape ul li:nth-child(3) {
        margin-top: 0;
    }

    .pa-blog-widget:last-child {
        margin-bottom: 22px;
    }

    .pa-blog-comnt ul .pa-cmnt-box,
    .pa-bill-form label,
    .pa-profile-data ul li,
    .pa-profile-img,
    .pa-edit-model ul li,
    .pa-blog-user {
        display: block;
    }

    .pa-cmnt-content,
    .pa-profile-name-box,
    .pa-blog-user-content {
        padding: 0;
        margin-top: 15px;
        width: 100%;
    }

    .pa-blog-comnt ul li ul {
        margin-left: 20px;
    }

    .pa-bill-form label span {
        margin-bottom: 10px;
        display: block;
    }
}

@media screen and (max-width: 375px) {
    .pa-head-icon > ul > li:nth-child(1) {
        display: none;
    }
}

/* User and cart icons - base styles */
li.pa-user-login,
li.pa-cart-icon {
    height: 40px;
    width: 40px;
    background-color: #d5bc5a;
    border-radius: 50%;
    text-align: center;
    line-height: 37px;
    margin-right: 15px;
    transition: background-color 0.3s ease;
}

li.pa-user-login.logged-in {
    background-color: #6caaa8;
}

li.pa-user-login svg,
li.pa-cart-icon svg {
    fill: #ffffff !important;
    width: 16px;
    height: 16px;
    vertical-align: middle;
}

/* Hover styles - ONLY change background color */
li.pa-user-login:hover,
li.pa-cart-icon:hover,
li.pa-user-login.logged-in:hover {
    background-color: #000;
}

/* Remove ALL other hover styles */
li.pa-user-login:hover,
li.pa-cart-icon:hover,
li.pa-user-login.logged-in:hover {
    background-color: #000;
    right: auto;
    top: auto;
    height: 40px;
    line-height: 37px;
    min-width: auto;
    padding: 0;
    color: inherit;
    border: none;
    font-size: inherit;
    border-radius: 50%;
    transform: none;
}

/* Override any mobile menu hover effects */
@media screen and (max-width: 991px) {
    .pa-menu-mobile-icons li.pa-user-login:hover,
    .pa-menu-mobile-icons li.pa-cart-icon:hover {
        background-color: #000;
        transform: none;
        right: auto;
        top: auto;
    }
}

/* Remove any transform effects that might be inherited */
.pa-menu ul li:hover,
.pa-menu-mobile-icons ul li:hover {
    transform: none;
}

/* Ensure appointment button doesn't move */
.pa-view-all {
    font-size: 15px;
    padding: 12px 24px;
    border-radius: 30px;
    background-color: #d5bc5a;
    color: #fff;
    border: 1px solid transparent;
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.pa-view-all:hover {
    background-color: #000;
    color: #fff;
    border-color: #000;
    transform: none;
}

/* Checkbox container */
.form-check {
    max-width: fit-content;
}
