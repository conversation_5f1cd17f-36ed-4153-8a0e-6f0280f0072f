<?php
/**
 * reCAPTCHA Configuration Validator
 * 
 * This script validates the reCAPTCHA configuration and provides
 * helpful information about the current setup.
 */

require_once 'includes/recaptcha_config.php';

echo "<h1>reCAPTCHA Configuration Validator</h1>\n";

// Validate configuration
$validation = RecaptchaConfig::validateConfig();

echo "<h2>Configuration Status</h2>\n";
if ($validation['valid']) {
    echo "<p style='color: green;'><strong>✅ Configuration is valid!</strong></p>\n";
} else {
    echo "<p style='color: red;'><strong>❌ Configuration has issues:</strong></p>\n";
    echo "<ul>\n";
    foreach ($validation['errors'] as $error) {
        echo "<li style='color: red;'>$error</li>\n";
    }
    echo "</ul>\n";
}

echo "<h2>Current Configuration</h2>\n";
echo "<table border='1' cellpadding='10' cellspacing='0'>\n";
echo "<tr><th>Setting</th><th>Value</th><th>Description</th></tr>\n";

echo "<tr>\n";
echo "<td><strong>Site Key</strong></td>\n";
echo "<td><code>" . RecaptchaConfig::getSiteKey() . "</code></td>\n";
echo "<td>Public key used in frontend JavaScript</td>\n";
echo "</tr>\n";

echo "<tr>\n";
echo "<td><strong>Secret Key</strong></td>\n";
echo "<td><code>" . substr(RecaptchaConfig::getSecretKey(), 0, 10) . "...</code></td>\n";
echo "<td>Private key used for server-side verification (partially hidden)</td>\n";
echo "</tr>\n";

echo "<tr>\n";
echo "<td><strong>Default Min Score</strong></td>\n";
echo "<td><code>" . RecaptchaConfig::getDefaultMinScore() . "</code></td>\n";
echo "<td>Minimum score required for reCAPTCHA v3 (0.0-1.0)</td>\n";
echo "</tr>\n";

echo "<tr>\n";
echo "<td><strong>Verify URL</strong></td>\n";
echo "<td><code>" . RecaptchaConfig::getVerifyUrl() . "</code></td>\n";
echo "<td>Google's verification endpoint</td>\n";
echo "</tr>\n";

echo "<tr>\n";
echo "<td><strong>Verify Timeout</strong></td>\n";
echo "<td><code>" . RecaptchaConfig::getVerifyTimeout() . " seconds</code></td>\n";
echo "<td>Timeout for verification requests</td>\n";
echo "</tr>\n";

echo "</table>\n";

echo "<h2>Generated Code Examples</h2>\n";

echo "<h3>HTML Script Tag</h3>\n";
echo "<pre><code>" . htmlspecialchars(RecaptchaConfig::getScriptTag()) . "</code></pre>\n";

echo "<h3>JavaScript Execute Code</h3>\n";
echo "<pre><code>" . htmlspecialchars(RecaptchaConfig::getExecuteScript('contact', 'function(token) { console.log("Token:", token); }')) . "</code></pre>\n";

echo "<h2>Files Using reCAPTCHA Configuration</h2>\n";
echo "<ul>\n";
echo "<li><strong>includes/recaptcha_config.php</strong> - Main configuration file</li>\n";
echo "<li><strong>includes/spam_protection.php</strong> - Uses secret key for verification</li>\n";
echo "<li><strong>contact.php</strong> - Uses site key for contact form</li>\n";
echo "<li><strong>includes/footer.php</strong> - Loads reCAPTCHA script on all pages</li>\n";
echo "<li><strong>assets/js/custom.js</strong> - Uses site key in JavaScript</li>\n";
echo "<li><strong>ajaxmail.php</strong> - Server-side verification for contact form</li>\n";
echo "<li><strong>subscribe.php</strong> - Server-side verification for newsletter</li>\n";
echo "</ul>\n";

echo "<h2>How to Update Keys</h2>\n";
echo "<ol>\n";
echo "<li>Go to <a href='https://www.google.com/recaptcha/admin' target='_blank'>Google reCAPTCHA Console</a></li>\n";
echo "<li>Create a new site or use existing site</li>\n";
echo "<li>Copy the Site Key and Secret Key</li>\n";
echo "<li>Edit <code>includes/recaptcha_config.php</code></li>\n";
echo "<li>Update the <code>SITE_KEY</code> and <code>SECRET_KEY</code> constants</li>\n";
echo "<li>Run this validator again to confirm changes</li>\n";
echo "</ol>\n";

echo "<h2>Testing Recommendations</h2>\n";
echo "<ul>\n";
echo "<li><strong>Test Contact Form:</strong> Submit the contact form and check for successful submission</li>\n";
echo "<li><strong>Test Newsletter:</strong> Subscribe to newsletter and verify it works</li>\n";
echo "<li><strong>Check Browser Console:</strong> Look for any JavaScript errors related to reCAPTCHA</li>\n";
echo "<li><strong>Test Spam Protection:</strong> Run <code>test_spam_protection.php</code></li>\n";
echo "<li><strong>Monitor Logs:</strong> Check server logs for reCAPTCHA verification results</li>\n";
echo "</ul>\n";

echo "<h2>Troubleshooting</h2>\n";
echo "<h3>Common Issues:</h3>\n";
echo "<ul>\n";
echo "<li><strong>Domain Mismatch:</strong> Ensure your domain is registered in Google reCAPTCHA Console</li>\n";
echo "<li><strong>Key Mismatch:</strong> Site key and secret key must be from the same reCAPTCHA site</li>\n";
echo "<li><strong>JavaScript Errors:</strong> Check browser console for reCAPTCHA loading issues</li>\n";
echo "<li><strong>Network Issues:</strong> Ensure server can reach Google's verification endpoint</li>\n";
echo "</ul>\n";

// Test network connectivity to Google
echo "<h3>Network Connectivity Test:</h3>\n";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'timeout' => 5
    ]
]);

$test_response = @file_get_contents('https://www.google.com/recaptcha/api/siteverify', false, $context);
if ($test_response !== false) {
    echo "<p style='color: green;'>✅ Can connect to Google reCAPTCHA service</p>\n";
} else {
    echo "<p style='color: red;'>❌ Cannot connect to Google reCAPTCHA service</p>\n";
    echo "<p>This may indicate network connectivity issues or firewall restrictions.</p>\n";
}

echo "<hr>\n";
echo "<p><em>Configuration validated on: " . date('Y-m-d H:i:s') . "</em></p>\n";
?>
