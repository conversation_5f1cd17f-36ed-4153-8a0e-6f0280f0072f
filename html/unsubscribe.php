<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

$message = '';
$success = false;

if (isset($_GET['email'])) {
    $email = trim($_GET['email']);
    
    if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $pdo = getPDO();
        
        try {
            // Check if email exists and is active
            $stmt = $pdo->prepare("SELECT id FROM newsletter_subscriptions WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                // Update status to unsubscribed
                $stmt = $pdo->prepare("UPDATE newsletter_subscriptions SET status = 'unsubscribed', unsubscribed_at = NOW() WHERE email = ?");
                $stmt->execute([$email]);
                
                $message = "U bent succesvol uitgeschreven van onze nieuwsbrief.";
                $success = true;
            } else {
                $message = "Dit e-mailadres is niet gevonden of al uitgeschreven.";
            }
        } catch (Exception $e) {
            $message = "Er is een fout opgetreden. Probeer het later opnieuw.";
        }
    } else {
        $message = "Ongeldig e-mailadres.";
    }
} else {
    $message = "Geen e-mailadres opgegeven.";
}

$page_title = "Uitschrijven Nieuwsbrief";
include 'includes/header.php';
?>

<div class="pa-breadcrumb-wrap">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="pa-breadcrumb">
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li>Uitschrijven</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="pa-contact spacer-top spacer-bottom">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="pa-contact-form">
                    <div class="text-center">
                        <h2>Nieuwsbrief Uitschrijven</h2>
                        
                        <div class="alert alert-<?= $success ? 'success' : 'danger' ?> mt-4">
                            <?= htmlspecialchars($message) ?>
                        </div>
                        
                        <?php if ($success): ?>
                            <p class="mt-4">Bedankt dat u gebruik heeft gemaakt van onze nieuwsbrief.</p>
                            <a href="index.php" class="pa-btn mt-3">Terug naar Home</a>
                        <?php else: ?>
                            <p class="mt-4">Als u problemen ondervindt, neem dan contact met ons op.</p>
                            <a href="contact.php" class="pa-btn mt-3">Contact</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>