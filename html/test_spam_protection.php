<?php
/**
 * Test script for spam protection functionality
 * 
 * This script tests the various spam protection measures:
 * - Honeypot detection
 * - Rate limiting
 * - reCAPTCHA verification (mock)
 * 
 * Usage: Run this script from command line or browser to test functionality
 */

require_once 'includes/spam_protection.php';

echo "<h1>Spam Protection Test Suite</h1>\n";

// Test 1: Honeypot Detection
echo "<h2>Test 1: Honeypot Detection</h2>\n";

$clean_post = ['email' => '<EMAIL>', 'name' => 'Test User'];
$spam_post = ['email' => '<EMAIL>', 'name' => 'Test User', 'website' => 'http://spam.com'];

echo "Clean POST data (no honeypot): " . (checkHoneypot($clean_post) ? "SPAM DETECTED" : "CLEAN") . "\n";
echo "Spam POST data (honeypot filled): " . (checkHoneypot($spam_post) ? "SPAM DETECTED" : "CLEAN") . "\n";

// Test 2: Rate Limiting
echo "<h2>Test 2: Rate Limiting</h2>\n";

$test_ip = '*************';
$form_type = 'test_form';

// Clear any existing rate limit data for test
$rate_limit_file = sys_get_temp_dir() . '/' . $form_type . '_rate_limit_' . md5($test_ip);
if (file_exists($rate_limit_file)) {
    unlink($rate_limit_file);
}

// Test multiple submissions
for ($i = 1; $i <= 5; $i++) {
    $rate_check = checkRateLimit($test_ip, $form_type, 3, 300);
    echo "Submission $i: " . ($rate_check['allowed'] ? "ALLOWED" : "BLOCKED") . 
         " (Remaining: {$rate_check['remaining']})\n";
    
    if ($rate_check['allowed']) {
        recordSubmission($test_ip, $form_type, $rate_check['submissions']);
    }
}

// Test 3: reCAPTCHA Verification (Mock)
echo "<h2>Test 3: reCAPTCHA Verification (Mock)</h2>\n";

// Test with empty response
$result = verifyRecaptcha('', $test_ip);
echo "Empty reCAPTCHA: " . ($result['success'] ? "VALID" : "INVALID") . 
     " (Error: {$result['error']})\n";

// Note: Real reCAPTCHA testing requires valid tokens from Google
echo "Note: Real reCAPTCHA testing requires valid tokens from the Google reCAPTCHA service.\n";

// Test 4: Comprehensive Spam Check
echo "<h2>Test 4: Comprehensive Spam Check</h2>\n";

// Test with honeypot spam
$spam_data = [
    'email' => '<EMAIL>',
    'name' => 'Test User',
    'website' => 'http://spam.com',
    'g-recaptcha-response' => ''
];

$spam_result = checkSpamProtection($spam_data, $test_ip, 'test_comprehensive');
echo "Honeypot spam test: " . ($spam_result['allowed'] ? "ALLOWED" : "BLOCKED") . 
     " (Message: {$spam_result['error_message']})\n";

// Test with clean data but no reCAPTCHA
$clean_data = [
    'email' => '<EMAIL>',
    'name' => 'Test User',
    'g-recaptcha-response' => ''
];

$clean_result = checkSpamProtection($clean_data, $test_ip, 'test_comprehensive2');
echo "Clean data, no reCAPTCHA: " . ($clean_result['allowed'] ? "ALLOWED" : "BLOCKED") . 
     " (Message: {$clean_result['error_message']})\n";

// Test 5: Rate Limit Reset Time
echo "<h2>Test 5: Rate Limit Information</h2>\n";

$rate_info = checkRateLimit($test_ip, $form_type, 3, 300);
echo "Current submissions: " . count($rate_info['submissions']) . "\n";
echo "Remaining submissions: " . $rate_info['remaining'] . "\n";
echo "Reset time: " . ($rate_info['reset_time'] > 0 ? date('Y-m-d H:i:s', $rate_info['reset_time']) : 'N/A') . "\n";

// Cleanup
if (file_exists($rate_limit_file)) {
    unlink($rate_limit_file);
}

$test_file2 = sys_get_temp_dir() . '/test_comprehensive_rate_limit_' . md5($test_ip);
if (file_exists($test_file2)) {
    unlink($test_file2);
}

$test_file3 = sys_get_temp_dir() . '/test_comprehensive2_rate_limit_' . md5($test_ip);
if (file_exists($test_file3)) {
    unlink($test_file3);
}

echo "<h2>Test Complete</h2>\n";
echo "All spam protection components have been tested.\n";
echo "Remember to test with real reCAPTCHA tokens in a live environment.\n";
?>
