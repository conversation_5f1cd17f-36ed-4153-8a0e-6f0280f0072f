<?php
require_once 'includes/config.php';

header('Content-Type: application/xml; charset=utf-8');

// Get all dynamic content from database
$pdo = getPDO();

// Get products
$products = $pdo->query("SELECT slug, updated_at FROM products")->fetchAll();

// Get blog posts
$posts = $pdo->query("SELECT slug, updated_at FROM blog_posts")->fetchAll();

// Get treatments
$treatments = $pdo->query("SELECT slug, updated_at FROM treatments")->fetchAll();

// Get workshops
$workshops = $pdo->query("SELECT slug, updated_at FROM workshops")->fetchAll();

// Static pages
$static_pages = [
    '' => '1.0',
    'over-mij.php' => '0.8',
    'contact.php' => '0.8',
    'shop.php' => '0.9',
    'blog.php' => '0.9',
    'behandelingen.php' => '0.9',
    'workshops.php' => '0.9',
    'algemene-voorwaarden.php' => '0.3'
];

echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <?php
    // Static pages
    foreach ($static_pages as $page => $priority): 
        $url = $config['site']['url'] . '/' . $page;
    ?>
    <url>
        <loc><?php echo htmlspecialchars($url); ?></loc>
        <changefreq>weekly</changefreq>
        <priority><?php echo $priority; ?></priority>
    </url>
    <?php endforeach; ?>

    <?php foreach ($products as $product): ?>
    <url>
        <loc><?php echo $config['site']['url']; ?>/producten/<?php echo htmlspecialchars($product['slug']); ?>.php</loc>
        <lastmod><?php echo date('Y-m-d', strtotime($product['updated_at'])); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; ?>

    <?php foreach ($posts as $post): ?>
    <url>
        <loc><?php echo $config['site']['url']; ?>/blog/<?php echo htmlspecialchars($post['slug']); ?>.php</loc>
        <lastmod><?php echo date('Y-m-d', strtotime($post['updated_at'])); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
    </url>
    <?php endforeach; ?>

    <?php foreach ($treatments as $treatment): ?>
    <url>
        <loc><?php echo $config['site']['url']; ?>/behandelingen/<?php echo htmlspecialchars($treatment['slug']); ?>.php</loc>
        <lastmod><?php echo date('Y-m-d', strtotime($treatment['updated_at'])); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; ?>

    <?php foreach ($workshops as $workshop): ?>
    <url>
        <loc><?php echo $config['site']['url']; ?>/workshops/<?php echo htmlspecialchars($workshop['slug']); ?>.php</loc>
        <lastmod><?php echo date('Y-m-d', strtotime($workshop['updated_at'])); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
    <?php endforeach; ?>
</urlset>