# Enable URL rewriting
RewriteEngine On

# Set default index file
DirectoryIndex index.php

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Remove trailing slashes
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)/$ /$1 [L,R=301]

# Remove .php extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME}\.php -f
RewriteRule ^(.*)$ $1.php [L]

# WordPress rules
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L]

# Set default charset
AddDefaultCharset UTF-8

# Enable Keep-Alive
KeepAlive On

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript application/x-javascript application/json
</IfModule>

# Set browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    
    # Images - 1 year
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # Fonts - 1 year
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # CSS and JS - 1 year (since you use versioning)
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType text/javascript "access plus 1 year"
    
    # HTML and dynamic content - no cache
    ExpiresByType text/html "access plus 0 seconds"
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# Add Cache-Control headers (fix the existing section)
<IfModule mod_headers.c>
    # Static assets - 1 year cache
    <FilesMatch "\.(ico|jpe?g|png|gif|webp|svg|woff2?|ttf|eot|otf|css|js)$">
        Header set Cache-Control "public, max-age=31536000, immutable"
        Header unset ETag
    </FilesMatch>
    
    # Versioned assets get longer cache
    <FilesMatch "\.(css|js)\?v=">
        Header set Cache-Control "public, max-age=31536000, immutable"
    </FilesMatch>
    
    # Dynamic content - no cache
    <FilesMatch "\.(html|htm|xml|json|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-XSS-Protection "1; mode=block"
    
    # Frame control policies
    Header set X-Frame-Options "SAMEORIGIN"
    Header set Content-Security-Policy "frame-ancestors 'self';"
    
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Cross-Origin Opener Policy
    Header set Cross-Origin-Opener-Policy "same-origin"
    
    # HTTP Strict Transport Security (HSTS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Content Security Policy
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://www.google.com https://www.gstatic.com https://www.googletagmanager.com https://www.google-analytics.com 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https://www.google-analytics.com; font-src 'self'; connect-src 'self' https://www.google-analytics.com; frame-src 'self' https://www.google.com; object-src 'none'; frame-ancestors 'self'"
</IfModule>

# Add MIME type for webmanifest files
AddType application/manifest+json .webmanifest
