<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/breadcrumb.php';

// Set CSRF token for form security
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$page_title = "Vragenlijst";

include 'includes/header.php';
include 'includes/menu.php';

renderBreadcrumb(
    $page_title,
    'contact-bg',
    []
); 
?>

<div class="pa-booking spacer-top spacer-bottom">
    <div class="container">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <!-- Wizard Progress -->
                <div class="wizard-progress mb-4">
                    <?php for($i = 1; $i <= 8; $i++): ?>
                        <div class="step <?php echo $i === 1 ? 'active' : ''; ?>" data-step="<?php echo $i; ?>">
                            <div class="step-icon"><?php echo $i; ?></div>
                            <div class="step-label"><?php echo $i <= 7 ? "Centrum $i" : "Gegevens"; ?></div>
                        </div>
                    <?php endfor; ?>
                    <div class="progress-bar"></div>
                </div>

                <!-- Questionnaire Form -->
                <form id="questionnaireForm" method="post" action="includes/process-questionnaire.php" class="needs-validation" novalidate>
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <!-- Centrum 1 -->
                    <div class="wizard-step active" data-step="1">
                        <h3 class="mb-4">Centrum 1</h3>
                        <?php
                        $questions1 = [
                            "Heeft u problemen met artrose of osteoporose?",
                            "Heeft u last van rugklachten, zoals een hernia, discopathie-aandoeningen, scoliose?",
                            "Heeft u regelmatig spasmen, spierkrampen of chronische spierpijn in het algemeen?",
                            "Heeft u bloedarmoede, bloedstoornissen of een sterke neiging tot het ontwikkelen van virussen en chronische vermoeidheid",
                            "Heeft u huidproblemen, zoals eczeem, psoriasis, acne of iets dergelijks?",
                            "Heeft u de neiging om meer te geven dan u ontvangt",
                            "Vindt u het moeilijk om van andere mensen te houden of ze te waarderen?",
                            "Als u iemand ziet die pijn heeft, probeert u hem dan te helpen?",
                            "Bent u onhandig in u sociale relaties, in die zin dat u moeite heeft tactvol te zijn?",
                            "Was u een verlegen persoon of bent u dat nog steeds?",
                            "Heeft uw gezondheidstoestand de neiging om te verzwakken met de wisseling van seizoenen?",
                            "Maken de veranderingen en het onverwachte in uw leven u nerveus?",
                            "Heeft u de neiging om anderen niet te laten zien hoe u zich voelt?",
                            "Voelt u zich het zwarte schaap van de familie?",
                            "Bent u de persoon met wie mensen automatisch komen praten als ze een probleem hebben?",
                            "Heeft u de neiging om snel de banden te verbreken in een relatie als er een probleem is met een andere persoon?"
                        ];
                        
                        foreach($questions1 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q1_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q1_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <div></div>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Centrum 2 -->
                    <div class="wizard-step" data-step="2">
                        <h3 class="mb-4">Centrum 2</h3>
                        <?php
                        $questions2 = [
                            "Heeft u gynaecologische problemen? ( voor mannen prostaat, teelbalproblemen).",
                            "Vaginale problemen zoals vaginitis, droogheid of een ander probleem op dit gebied?",
                            "Voelt u een afwezigheid van seksueel verlangen?",
                            "Als u geld leent aan iemand van wie u houdt, heeft u dan de neiging rente in rekening te brengen?",
                            "Komt u vaak in een moeilijke financiële situatie of in de schulden, bv voor een vakantie?",
                            "Bent u iemand die competitie hoog in het vaandel heeft staan?",
                            "Hebben de mensen om u heen de neiging om u de schuld te geven van dit aspect?",
                            "Heeft u ooit een relatie verbroken voor uw professionele werk?",
                            "Heeft u een lange levensfase meegemaakt waarin u in een buitensporige beperkende relatie zat, waarin u zich ondergewaardeerd voelde?"
                        ];
                        
                        foreach($questions2 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q2_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q2_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Centrum 3 -->
                    <div class="wizard-step" data-step="3">
                        <h3 class="mb-4">Centrum 3</h3>
                        <?php
                        $questions3 = [
                            "Heeft u spijsverteringsproblemen?",
                            "Heeft u verslavingsproblemen zoals alcohol, tabak, drugs?"
                        ];
                        
                        foreach($questions3 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q3_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q3_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <!-- Special question for weight/height -->
                        <div class="form-group mb-4">
                            <label class="form-label">Wat zijn uw lengte en uw gewicht?</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label class="form-label">Lengte (cm):</label>
                                    <input type="number" class="form-control" name="height" placeholder="in cm">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Gewicht (kg):</label>
                                    <input type="number" class="form-control" name="weight" placeholder="in kg">
                                </div>
                            </div>
                        </div>

                        <?php
                        $questions3_continued = [
                            "Heeft u last van boulimie of anorexia?",
                            "Vindt u het moeilijk om uzelf in de spiegel te zien?",
                            "Heeft u de neiging om mensen met een verslavingsstoornis in uw leven aan te trekken?",
                            "Geeft u om gewicht?",
                            "Heeft u dwangmatig gedrag om uzelf te kalmeren, zoals een stuk chocola of winkelen?",
                            "Vindt u het belangrijk om altijd in de mode te zijn, of het om u kleding, uitdrukkingen , uw persoonlijke stijl of kennissen gaat?"
                        ];
                        
                        foreach($questions3_continued as $index => $question): 
                            $question_index = $index + count($questions3) + 1; // Continue numbering after weight/height question
                        ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q3_<?php echo $question_index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q3_<?php echo $question_index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Centrum 4 -->
                    <div class="wizard-step" data-step="4">
                        <h3 class="mb-4">Centrum 4</h3>
                        <?php
                        $questions4 = [
                            "Heeft u arteriële, veneuze problemen: doorbloedingsstoornissen, oedeem, spataderen?",
                            "Heeft u atherosclerose?",
                            "Heeft u een hoge bloeddruk?",
                            "Heeft u een te hoog cholesterolgehalte?",
                            "Heeft u ooit een hartaanval gehad of heeft u ooit een waarschuwingssignaal gekregen?",
                            "Heeft u astma of andere luchtwegallergieën of heeft u dat gehad?",
                            "Probeert u regelmatig te weten te komen hoe de mensen van wie u houdt zich voelen?",
                            "Is u gemoedstoestand gevoelig voor het weer, voor de wisseling van de seizoenen?",
                            "Heeft u ooit gehuild vanwege uw werk?",
                            "Huilt u vaak en gemakkelijk?",
                            "Is het moeilijk voor u om boos te worden op een dierbare, ook al is dat terecht?",
                            "Bent u geneigd om snel geïrriteerd en boos te worden?",
                            "Als er iets mis is en u voelt zich slecht, heeft u dan de neiging om uzelf te isoleren en afstand te nemen van anderen?"
                        ];
                        
                        foreach($questions4 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q4_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q4_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Centrum 5 -->
                    <div class="wizard-step" data-step="5">
                        <h3 class="mb-4">Centrum 5</h3>
                        <?php
                        $questions5 = [
                            "Heeft u gebitsproblemen of heeft u deze in het verleden gehad?",
                            "Heeft u last van schildklierproblemen?",
                            "Heeft u last van de cervicale regio?",
                            "Heeft u vaak keelpijn?",
                            "Heeft u andere keelproblemen gehad?",
                            "Had u moeite om instructies op te volgen toen u jong was?",
                            "Heeft u in uw huidige leven moeite om de regels, de richtlijnen te volgen?",
                            "Vindt u het moeilijk om u concentratie vast te houden als u aan het telefoneren bent?",
                            "Heeft u moeite om uzelf verstaanbaar te maken als u met mensen om u heen praat?",
                            "Heeft u moeite om uzelf in het openbaar uit te drukken?",
                            "Heeft u de neiging om ja te zeggen, zelfs als u andersom denkt, zodat u uzelf niet hoeft te verantwoorden?",
                            "Heeft u last van dyslexie, spraak of uitdrukkingsproblemen wanneer u zich ongemakkelijk, gestrest voelt?"
                        ];
                        
                        foreach($questions5 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q5_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q5_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Centrum 6 -->
                    <div class="wizard-step" data-step="6">
                        <h3 class="mb-4">Centrum 6</h3>
                        <?php
                        $questions6 = [
                            "Heeft u last van slapeloosheid?",
                            "Heeft u last van hoofdpijn en migraine?",
                            "Bent u gestrest over het ouder worden?",
                            "Heeft u last van ernstig geheugenverlies?",
                            "Heeft u last van staar gehad of heeft u er momenteel last van?",
                            "Heeft u last van duizeligheid?",
                            "Als u een beperkte spreektijd krijgt, heeft u er dan moeite mee om deze te respecteren?",
                            "Heeft u moeite met het beantwoorden van vragenlijsten met meerkeuzevragen?",
                            "Heeft u het gevoel dat u geest vaak afdwaalt?",
                            "Bent u terughoudend om nieuwe technologie of nieuwe manieren van zakendoen te leren?",
                            "Heeft u ooit een significant trauma of misbruik meegemaakt?",
                            "Voelt u zich fijn in de natuur, heeft u soms de indruk er één mee te zijn?"
                        ];
                        
                        foreach($questions6 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q6_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q6_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Centrum 7 -->
                    <div class="wizard-step" data-step="7">
                        <h3 class="mb-4">Centrum 7</h3>
                        <?php
                        $questions7 = [
                            "Heeft u een chronische ziekte?",
                            "Heeft u een ernstige of ongeneeslijke ziekte waarvan de diagnose is gesteld?",
                            "Lijdt u aan kanker?",
                            "Bent u in een zeer kritieke gezondheidstoestand met een nogal pessimistische mening van de artsen?",
                            "Bent u iemand die gesloten is voor spiritualiteit of religies?",
                            "Bent u het type persoon dat altijd naar zijn werk gaat zonder een dag vrij te nemen, zelfs als u zich niet lekker voelt?",
                            "Raakt u in paniek als u nadenkt over de zin en het doel van uw leven?",
                            "Heeft u de neiging om gezondheidsproblemen en morele problemen aan elkaar te koppelen?",
                            "Heeft u het gevoel dat uw dierbaren de neiging hebben om van u weg te lopen en u in de steek laten als u met een probleem wordt geconfronteerd?"
                        ];
                        
                        foreach($questions7 as $index => $question): ?>
                            <div class="form-group mb-4">
                                <label class="form-label"><?php echo $question; ?></label>
                                <div class="d-flex gap-3">
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q7_<?php echo $index; ?>" value="ja" required>
                                        <label class="form-check-label">Ja</label>
                                    </div>
                                    <div class="form-check">
                                        <input type="radio" class="form-check-input" name="q7_<?php echo $index; ?>" value="nee" required>
                                        <label class="form-check-label">Nee</label>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="button" class="pa-btn next-step">Volgende</button>
                        </div>
                    </div>

                    <!-- Personal Information Step -->
                    <div class="wizard-step" data-step="8">
                        <h3 class="mb-4">Uw Gegevens</h3>
                        
                        <div class="form-group mb-4">
                            <label class="form-label">Naam <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="full_name" required>
                        </div>

                        <div class="form-group mb-4">
                            <label class="form-label">E-mail <span class="text-danger">*</span></label>
                            <input type="email" class="form-control" name="email" required>
                        </div>

                        <div class="form-group mb-4">
                            <label class="form-label">Telefoon</label>
                            <input type="tel" class="form-control" name="phone">
                        </div>

                        <div class="form-group mb-4">
                            <label class="form-label">Adres</label>
                            <input type="text" class="form-control" name="address">
                        </div>

                        <div class="form-group mb-4">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" required>
                                <label class="form-check-label" for="privacyConsent">
                                    Ik ga akkoord met de <a href="privacy-policy.php" target="_blank">privacyverklaring</a> <span class="text-danger">*</span>
                                </label>
                            </div>
                        </div>

                        <div class="wizard-buttons">
                            <button type="button" class="pa-btn prev-step">Vorige</button>
                            <button type="submit" class="pa-btn">Verzenden</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
    <script src="assets/js/questionnaire.js"></script>
</body>
