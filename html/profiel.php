<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/config.php'; 
require_once 'includes/functions.php';
require_once 'includes/admin-auth.php';
require_once 'includes/breadcrumb.php';

if (!isLoggedIn()) {
    header('Location: index.php');
    exit();
}

// Get user data
$user = getUserData($_SESSION['user_id']);
if (!$user) {
    header('Location: index.php');
    exit();
}

// Check if user has admin access
$isAdmin = hasAdminAccess();

// Format the creation date
$creationDate = !empty($user['created_at']) ? date('d-m-Y', strtotime($user['created_at'])) : 'Unknown';

$page_title = "Mijn Profiel";
?>

<?php include 'includes/header.php'; ?> 
<?php include 'includes/menu.php'; ?> 

<?php 
renderBreadcrumb(
    $page_title,
    'profile-bg',
    []
); 
?>

    <!-- profile start -->
    <div class="pa-profile spacer-top spacer-bottom">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="pa-profile-box">
                        <div class="pa-profile-img">
                            <div class="pa-profile-userimg">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                                </svg>
                            </div>
                            <div class="pa-profile-name-box">
                                <h2 class="pa-profile-name">Welkom, <span><?php echo htmlspecialchars($user['username']); ?></span></h2>
                                <p class="pa-profile-member">Lid sinds: <?php echo $creationDate; ?></p>
                            </div>
                        </div>
                        <div class="pa-profile-data">
                            <ul>
                                <li>
                                    <p>Gebruikersnaam:</p>
                                    <p><?php echo htmlspecialchars($user['username']); ?></p>
                                </li>
                                <li>
                                    <p>Email:</p>
                                    <p><?php echo htmlspecialchars($user['email']); ?></p>
                                </li>
                                <?php if (!empty($user['phone'])): ?>
                                <li>
                                    <p>Telefoon:</p>
                                    <p><?php echo htmlspecialchars($user['phone']); ?></p>
                                </li>
                                <?php endif; ?>
                                <?php if (!empty($user['address'])): ?>
                                <li>
                                    <p>Adres:</p>
                                    <p><?php echo htmlspecialchars($user['address']); ?></p>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </div>

                        <!-- Edit Profile Modal -->
                        <div class="pa-edit-model">
                            <div class="modal fade" id="editProfileModal">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Profiel bewerken</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <form action="update-profile.php" method="post">
                                            <div class="modal-body">
                                                <ul>
                                                    <li>
                                                        <p>Gebruikersnaam:</p>
                                                        <input type="text" name="username" value="<?php echo htmlspecialchars($user['username']); ?>" required />
                                                    </li>
                                                    <li>
                                                        <p>Email:</p>
                                                        <input type="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required />
                                                    </li>
                                                    <li>
                                                        <p>Telefoon:</p>
                                                        <input type="tel" name="phone" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>" />
                                                    </li>
                                                    <li>
                                                        <p>Adres:</p>
                                                        <input type="text" name="address" value="<?php echo htmlspecialchars($user['address'] ?? ''); ?>" />
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="submit" class="pa-btn">Opslaan</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($isAdmin): ?>
                        <!-- Admin Links Section -->
                        <div class="pa-admin-links mt-4">
                            <h3>Beheer</h3>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="list-group">
                                        <a href="admin/orders.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-shopping-cart"></i> Bestellingen
                                        </a>
                                        <a href="admin/appointments.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-calendar-check"></i> Afspraken
                                        </a>
                                        <a href="admin/business-hours.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-clock"></i> Openingstijden
                                        </a>
                                        <a href="admin/newsletter-subscribers.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-envelope"></i> Nieuwsbrief Abonnees
                                        </a>
                                        <a href="admin/newsletter-send.php" class="list-group-item list-group-item-action">
                                            <i class="fas fa-paper-plane"></i> Nieuwsbrief Versturen
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- profile end -->

<?php include 'includes/footer.php'; ?>
