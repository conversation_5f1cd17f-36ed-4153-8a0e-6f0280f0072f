<?php
session_start();
require_once '../includes/config.php';
require_once '../includes/breadcrumb.php';

// SEO data for this page
$seo_data = [
    'title' => 'Empower olieroller',
    'description' => 'Natuurlijke essentiële olie roller voor meer vitaliteit en energie. Met biologische oliën van Pompelmoes, Rozemarijn, Zwarte Peper & Zwarte Spar voor directe werking.',
    'image' => '/assets/images/products/empower-olieroller.png',
    'type' => 'product'
];

// Structured data for this product
$structured_data_type = 'Product';
$structured_data = [
    'name' => 'Empower olieroller',
    'description' => 'Handige olieroller met krachtige mix van essentiële oliën voor meer vitaliteit en doorzettingsvermogen. Perfect voor tijdens het werk, op school of onderweg.',
    'image' => '/assets/images/products/empower-olieroller.png',
    'price' => '16.95'
];

$page_title = "Empower olieroller";

include '../includes/header.php';
include '../includes/menu.php';

renderBreadcrumb(
    $page_title,
    'olieroller-bg',
    [['url' => 'shop.php', 'name' => 'Producten']]
); 
?>

        <!-- product single start -->
        <div class="pa-product-single spacer-top">
            <div class="container">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="row">
                            <div class="col-sm-5">
                                <div class="pa-prod-thumb-img">
                                    <img src="../assets/images/empower-olieroller.png" alt="image" class="img-fluid" />
                                </div>
                            </div>
                            <div class="col-sm-7">
                                <div class="pa-prod-content">
                                    <h2 class="pa-prod-title">Empower olieroller</h2>
                                    <p class="pa-prod-price"><span>Prijs: 16,95</p>
                                    <a href="../shop.php?category=olierollers" class="pa-prod-category"><span>Categorie:</span> olieroller</a>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="pa-prod-content">
                                    <div class="pa-prod-count">
                                        <div class="pa-cart-quantity">
                                            <button class="pa-sub"></button>
                                            <input type="number" id="1" value="1" min="1" max="3">
                                            <button class="pa-add"></button>
                                        </div>
                                       <button class="pa-btn add-to-cart" data-product-id="26">Voeg toe aan winkelwagen</button>
                                    </div>
                                    <p>Voel je je uitgeput, lusteloos of stagneert je leven? De Empower Roller geeft je direct meer vitaliteit, frisse moed en doorzettingsvermogen om weer richting te geven aan je leven.</p>
                                    <p>Deze handige roller zorgt voor transformatie en helpt je weer in beweging te komen. Perfect voor tijdens het werk, op school of onderweg. De krachtige mix van essentiële oliën reset je energie in slechts enkele minuten.</p>
                                    <p>Samenstelling: 100% organische essentiële oliën van Pompelmoes, Rozemarijn, Zwarte Peper & Zwarte Spar in biologische jojoba olie, verrijkt met Vitamine E. Vegan en dierproefvrij.</p>
                                    <p>Geschikt voor volwassenen en kinderen vanaf 7 jaar. Niet geschikt voor zwangere vrouwen.</p>
                                </div>
                            </div>
                        </div>
                        <!-- product start -->
                        <div class="pa-related-product">
                            <div class="container">
                                <div class="pa-heading">
                                    <h1>Gerelateerde producten</h1>
                                </div>
                                <div class="row">
                                    <div class="swiper-container">
                                        <div class="swiper-wrapper">
                                            <?php 
                                            $products = [
                                                [
                                                    'name' => 'Empower massage crème',
                                                    'image' => '../assets/images/empower-creme.png',
                                                    'price' => '26,95',
                                                    'link' => 'empower-massage-creme.php'
                                                ],
                                                [
                                                    'name' => 'Kruidendruppels Empower',
                                                    'image' => '../assets/images/empower-kruidendruppels.png',
                                                    'price' => '18,95',
                                                    'link' => 'empower-kruiden-druppels.php'
                                                ]
                                            ];

                                            foreach($products as $product): ?>
                                            <div class="swiper-slide">
                                                <div class="pa-product-box">
                                                    <div class="pa-product-img">
                                                        <div class="pa-product-cart">
                                                            <ul>
                                                                <li><a href="#"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 469.333 469.333"><g><path d="M434.979,42.667H85.333c-1.053,0-2.014,0.396-3.001,0.693l-8.594-28.241C71.005,6.138,62.721,0,53.333,0H10.667C4.776,0,0,4.776,0,10.667V32c0,5.891,4.776,10.667,10.667,10.667h26.865l66.646,219.01l-24.891,29.039c-9.838,11.477-14.268,27.291-9.74,41.713c5.791,18.445,22.07,30.237,40.839,30.237H416c5.891,0,10.667-4.776,10.667-10.667v-21.333c0-5.891-4.776-10.667-10.667-10.667H110.385l33.813-39.448c0.85-0.992,1.475-2.112,2.12-3.219h206.703c16.533,0,31.578-9.548,38.618-24.507l74.434-158.17c2.135-4.552,3.26-9.604,3.26-14.615v-3.021C469.333,58.048,453.952,42.667,434.979,42.667z"/><circle cx="128" cy="426.667" r="42.667"/><circle cx="384" cy="426.667" r="42.667"/></g></svg></a></li>
                                                                <li><a href="#"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><g><path d="M376,30c-27.783,0-53.255,8.804-75.707,26.168c-21.525,16.647-35.856,37.85-44.293,53.268c-8.437-15.419-22.768-36.621-44.293-53.268C189.255,38.804,163.783,30,136,30C58.468,30,0,93.417,0,177.514c0,90.854,72.943,153.015,183.369,247.118c18.752,15.981,40.007,34.095,62.099,53.414C248.38,480.596,252.12,482,256,482s7.62-1.404,10.532-3.953c22.094-19.322,43.348-37.435,62.111-53.425C439.057,330.529,512,268.368,512,177.514C512,93.417,453.532,30,376,30z"/></g></svg></a></li>
                                                            </ul>
                                                        </div>
                                                        <img src="<?php echo $product['image']; ?>" alt="<?php echo $product['name']; ?>" class="img-fluid" />
                                                    </div>
                                                    <div class="pa-product-content">
                                                        <h4><a href="<?php echo $product['link']; ?>"><?php echo $product['name']; ?></a></h4>
                                                        <p>€<?php echo $product['price']; ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <!-- Add Arrows -->
                                        <div class="swiper-button-next"></div>
                                        <div class="swiper-button-prev"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- product end -->
                    </div>
                    <div class="col-lg-4">
                        <div class="pa-product-sidebar">
                            <div class="pa-widget pa-search">
                                <input type="text" placeholder="Zoek product">
                                <button class="pa-btn">Zoek</button>
                            </div>
                            <div class="pa-widget pa-shop-category">
                                <h2 class="pa-sidebar-title">Categorieen</h2>
                                <ul>
                                    <li><a href="../shop.php?category=cremes">Crèmes<span>4</span></a></li>
                                    <li><a href="../shop.php?category=druppels20ml">Druppels (20ml) <span>12</span></a></li>
                                    <li><a href="../shop.php?category=druppels30ml">Druppels (30ml) <span>6</span></a></li>
                                    <li><a href="../shop.php?category=olierollers">Olie rollers<span>4</span></a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- product single end -->
        <?php include '../includes/footer.php'; ?>
