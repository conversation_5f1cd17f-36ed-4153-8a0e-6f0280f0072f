{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "Payment return initiated",
    "data": {
        "order_id": null,
        "session_id": "12291fd0a60c0c636647a381d4837ad9"
    }
}
---
{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "Attempting to get order details",
    "data": {
        "order_id": null
    }
}
---
{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "Error getting order details",
    "data": {
        "error": "Empty order ID",
        "order_id": null
    }
}
---
{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "Payment return error",
    "data": {
        "error": "Empty order ID",
        "trace": "#0 {main}",
        "order_id": null
    }
}
---
{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "PHP Error",
    "data": {
        "errno": 2,
        "message": "Undefined array key \"HTTP_HOST\"",
        "file": "\/Users\/<USER>\/Downloads\/Main File\/html\/includes\/seo.php",
        "line": 21
    }
}
---
{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "PHP Error",
    "data": {
        "errno": 2,
        "message": "Undefined array key \"REQUEST_URI\"",
        "file": "\/Users\/<USER>\/Downloads\/Main File\/html\/includes\/seo.php",
        "line": 21
    }
}
---
{
    "timestamp": "2025-05-07 16:21:03",
    "ip": "unknown",
    "session_id": "12291fd0a60c0c636647a381d4837ad9",
    "message": "PHP Error",
    "data": {
        "errno": 2,
        "message": "Undefined array key \"REQUEST_METHOD\"",
        "file": "\/Users\/<USER>\/Downloads\/Main File\/html\/includes\/cart_handler.php",
        "line": 477
    }
}
---
