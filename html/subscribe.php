<?php
session_start();
require_once 'includes/config.php';
require_once 'includes/email_helper.php';
require_once 'includes/db.php';
require 'vendor/autoload.php';

// Check if it's an AJAX request
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    header('Location: index.php');
    exit;
}

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_var($_POST['email'] ?? '', FILTER_VALIDATE_EMAIL);
    $first_name = trim($_POST['first_name'] ?? '');
    $last_name = trim($_POST['last_name'] ?? '');
    
    if (!$email) {
        echo json_encode(['success' => false, 'message' => 'Vul een geldig e-mailadres in.']);
        exit;
    }
    
    try {
        $pdo = getPDO();
        if (!$pdo) {
            throw new Exception('Database verbinding mislukt');
        }
        
        // Check if email already exists
        $stmt = $pdo->prepare("SELECT id, status FROM newsletter_subscriptions WHERE email = ?");
        $stmt->execute([$email]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            if ($existing['status'] === 'active') {
                echo json_encode(['success' => false, 'message' => 'Dit e-mailadres is al ingeschreven voor de nieuwsbrief.']);
                exit;
            } else {
                // Reactivate subscription
                $stmt = $pdo->prepare("UPDATE newsletter_subscriptions SET status = 'active', subscribed_at = NOW(), unsubscribed_at = NULL, first_name = ?, last_name = ? WHERE email = ?");
                $stmt->execute([$first_name, $last_name, $email]);
            }
        } else {
            // Insert new subscription
            $stmt = $pdo->prepare("INSERT INTO newsletter_subscriptions (email, first_name, last_name) VALUES (?, ?, ?)");
            $stmt->execute([$email, $first_name, $last_name]);
        }
        
        $emailBody = "
            <p>Er is een nieuwe inschrijving voor de nieuwsbrief:</p>
            <p>E-mail: {$email}</p>
            <p>Datum: " . date('d-m-Y H:i:s') . "</p>
        ";
        
        if (sendEmail('Nieuwe nieuwsbrief inschrijving', $emailBody, '', '<EMAIL>')) {
            echo json_encode(['success' => true, 'message' => 'Bedankt voor je inschrijving op de nieuwsbrief!']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Er is een probleem opgetreden. Probeer het later opnieuw.']);
        }
    } catch (Exception $e) {
        error_log("Newsletter subscription error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Er is een probleem opgetreden. Probeer het later opnieuw.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
}
