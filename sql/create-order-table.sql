-- Drop tables if they exist (in reverse order to avoid foreign key constraints)
DROP TABLE IF EXISTS order_items;
DROP TABLE IF EXISTS orders;

-- Create orders table
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    status VARCHAR(50) NOT NULL,
    payment_id VARCHAR(255),
    shipping_address JSON NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    email_sent TINYINT(1) DEFAULT 0,
    delivery_method VARCHAR(20) DEFAULT 'shipping',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id)
);

-- Create order_items table
CREATE TABLE order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    product_id INT NOT NULL,
    quantity INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    FOREIG<PERSON> KEY (order_id) REFERENCES orders(id),
    FOREI<PERSON><PERSON> KEY (product_id) REFERENCES products(id)
);
