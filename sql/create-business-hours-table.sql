CREATE TABLE business_hours (
    id INT AUTO_INCREMENT PRIMARY KEY,
    day_of_week TINYINT NOT NULL COMMENT '0=Sunday, 1=Monday, ..., 6=Saturday',
    start_time TIME,
    end_time TIME,
    created_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_day (day_of_week)
);

-- Add indexes to appointments table for better performance
ALTER TABLE appointments 
ADD INDEX idx_appointment_datetime (appointment_date, appointment_time),
ADD INDEX idx_status (status);