-- Clear existing business hours
TRUNCATE TABLE business_hours;

-- Insert business hours for Monday through Friday (1-5)
-- Sunday = 0, Monday = 1, ..., Saturday = 6
INSERT INTO business_hours (day_of_week, start_time, end_time) VALUES
(1, '09:00', '15:00'),  -- Monday
(2, '09:00', '15:00'),  -- Tuesday
(3, '09:00', '15:00'),  -- Wednesday
(4, '09:00', '15:00'),  -- Thursday
(5, '09:00', '15:00');  -- Friday

-- Note: Saturday (6) and Sunday (0) are not included, making them non-bookable days