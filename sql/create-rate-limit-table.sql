-- Create rate_limits table for tracking request frequency in MySQL
CREATE TABLE IF NOT EXISTS `rate_limits` (
  `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  `identifier` VARCHAR(255) NOT NULL COMMENT 'IP address, user ID, or other identifier',
  `action` VARCHAR(64) NOT NULL COMMENT 'The action being rate limited (e.g., login, registration)',
  `created_at` DATETIME NOT NULL COMMENT 'When the attempt occurred',
  
  -- Indexes for faster lookups
  INDEX `idx_identifier_action` (`identifier`(191), `action`),
  INDEX `idx_created_at` (`created_at`),
  INDEX `idx_action_created_at` (`action`, `created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Tracks rate limiting for various actions';

-- Optional: Add a procedure to clean up old rate limit records
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `cleanup_rate_limits`()
BEGIN
    -- Delete records older than 24 hours
    DELETE FROM `rate_limits` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 24 HOUR);
END //
DELIMITER ;

-- Optional: Add an event to automatically run the cleanup procedure daily
-- First make sure event scheduler is enabled
SET GLOBAL event_scheduler = ON;

-- Then create the event
DROP EVENT IF EXISTS `daily_rate_limit_cleanup`;
CREATE EVENT `daily_rate_limit_cleanup`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
    CALL cleanup_rate_limits();