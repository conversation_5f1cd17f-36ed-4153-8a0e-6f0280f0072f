<!doctype html>
<!--[if IE 6 ]><html lang="en-us" class="ie6"> <![endif]-->
<!--[if IE 7 ]><html lang="en-us" class="ie7"> <![endif]-->
<!--[if IE 8 ]><html lang="en-us" class="ie8"> <![endif]-->
<!--[if (gt IE 7)|!(IE)]><!-->
<html lang="en-us">
<!--<![endif]-->

<head>
	<meta charset="utf-8">
	<title>Jacqueline T<PERSON>sens - Responsive HTML Template</title>
	<meta name="description" content="Market Monster - Responsive HTML Template">
	<meta name="author" content="PixelNx">
	<meta name="copyright" content="PixelNx">
	<meta name="generator" content="Documenter v2.0 http://rxa.li/documenter">
	<link rel="stylesheet" href="assets/css/documenter_style.css" media="all">
	<link rel="stylesheet" href="assets/js/google-code-prettify/prettify.css" media="screen">
	<script src="assets/js/google-code-prettify/prettify.js"></script>
	<link rel="shortcut icon" type="image/x-icon" href="assets/images/Fav.png" />
	<script src="assets/js/jquery.js"></script>
	<script src="assets/js/jquery.scrollTo.js"></script>
	<script src="assets/js/jquery.easing.js"></script>
	<script>document.createElement('section'); var duration = '450', easing = 'easeOutExpo';</script>
	<script src="assets/js/script.js"></script>
	<style>
		html {
			background-color: #F3F3F3;
			color: #585858;
		}

		::-moz-selection {
			background: #111111;
			color: #F1F1F1;
		}

		::selection {
			background: #111111;
			color: #F1F1F1;
		}

		#documenter_sidebar #documenter_logo {
			background-image: url(assets/images/Logo.png);
			/* background-color: #2c2e44; */
			/* background-size: 90%; */
		}

		a {
			color: #111111;
		}

		.btn {
			border-radius: 3px;
		}

		.btn-primary {
			background-image: -moz-linear-gradient(top, #585858, #3B3B3B);
			background-image: -ms-linear-gradient(top, #585858, #3B3B3B);
			background-image: -webkit-gradient(linear, 0 0, 0 585858%, from(#333333), to(#3B3B3B));
			background-image: -webkit-linear-gradient(top, #585858, #3B3B3B);
			background-image: -o-linear-gradient(top, #585858, #3B3B3B);
			background-image: linear-gradient(top, #585858, #3B3B3B);
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#585858', endColorstr='#3B3B3B', GradientType=0);
			border-color: #3B3B3B #3B3B3B #bfbfbf;
			color: #F9F9F9;
		}

		.btn-primary:hover,
		.btn-primary:active,
		.btn-primary.active,
		.btn-primary.disabled,
		.btn-primary[disabled] {
			border-color: #585858 #585858 #bfbfbf;
			background-color: #3B3B3B;
		}

		hr {
			border-top: 1px solid #E5E5E5;
			border-bottom: 1px solid #F9F9F9;
		}

		#documenter_sidebar,
		#documenter_sidebar ul a {
			background-color: #fff;
			color: #000;
			/* http: //static.revaxarts-themes.com/noise.gif; */
		}

		/* order-top: 1px solid #ffffff;
		} */

		#documenter_sidebar ul a {
			border-top: 1px solid #ffffff;
			border-bottom: 0px solid #ffffff;
			color: #000;
			font-size: 14px;
			-webkit-transition: all 0.3s;
			-moz-transition: all 0.3s;
			-ms-transition: all 0.3s;
			-o-transition: all 0.3s;
			transition: all 0.3s;
			font-weight: 500;
			text-transform: capitalize;
		}

		#documenter_sidebar ul a:hover {
			background: #d5bc5a;
			color: #fff;
			border-top: 1px solid #ffffff;
			-webkit-transition: all 0.3s;
			-moz-transition: all 0.3s;
			-ms-transition: all 0.3s;
			-o-transition: all 0.3s;
			transition: all 0.3s;
		}

		#documenter_sidebar ul a.current {
			background: #d5bc5a;
			color: #fff;
			border-top: 1px solid #ffffff;
		}

		#documenter_copyright {
			color: #000;
			display: block !important;
			visibility: visible !important;
		}

		#documenter_sidebar ul a {
			display: block;
			border-top: 1px solid #ddd;
			border-bottom: 1px solid #aaa;
			padding: 10px 15px 10px 0;
			text-align: right;
		}
	</style>
</head>

<body class="documenter-project-documenter-v20">
	<div id="documenter_sidebar"> <a href="#documenter_cover" id="documenter_logo"></a>


		<ul id="documenter_nav">
			<li><a class="current" href="#documenter_cover">Start</a></li>
			<li><a href="#features" title="Features">Features</a></li>
			<li><a href="#Structure" title="Contents">HTML Structure</a></li>
			<li><a href="#CSS" title="css">CSS Files and Table of contents</a></li>
			<li><a href="#JS" title="css">Java Scripts </a></li>
			<li><a href="#Fonts" title="Fonts">Used Fonts</a></li>
			<li><a href="#Credits" title="Credits">Sources and Credits</a></li>
			<li><a href="#Conclusion" title="Conclusion">Conclusion</a></li>
		</ul>
		<div id="documenter_copyright">Copyright Jacqueline Tjassens HTML Website Template . All Rights Reserved<br>
		</div>
	</div>
	<div id="documenter_content">
		<section id="documenter_cover">
			<h1 class="mylogo">Jacqueline Tjassens<span class="mylogocolor"> Responsive HTML Template </h1>
			<hr>
			<ul>
				<li>Created: 26/11/2022</li>
				<li>Updated: 17/07/2024</li>
				<li>By: <a href="https://www.templatemonster.com/authors/pixelnx/">PixelNx</a></li>
			</ul>
			<p><b>Jacqueline Tjassens Responsive HTML Template</b> - The unique and most featured template for herbalist Jacqueline Tjassenss, Herbal companies, Medical herbal Centers, Herbal Health clinics, and health Centers is now in the template market.It supports every browser and has a working contact form that can help small businesses to go online and connect to customers.</p>

			<p>Jacqueline Tjassens HTML template’s seven valid pages help your customer know your business and ingredients, with responsive behavior to any screen as an additional feature.</p>
			<p>Seems like a Good fit? Go get the product now!</p>
			<p>Thank you for purchasing my Template. If you have any questions that are beyond the scope of this help
				file, please feel free to raise a ticket. Thank you so much!</p>
			<br>
			<br>
			<p>If you like this template...</p>
			<img alt="wink" height="20" src="assets/images/icon-star.png"> <br>
			<strong>Please Don’t Forget to Rate it!</strong>
		</section>
		<hr>
		<section id="features">
			<div class="page-header">
				<h3>Features</h3>
				<hr class="notop">
			</div>
			<ul>
				<li> Bootstrap 5 Framework </li>
				<li> Google Fonts </li>
				<li> 7 HTML5 Valid Pages (CSS3) </li>
				<li> Working Contact Form </li>
				<li> Amazing Support </li>
				<li> Compatible With Major Modern Browsers </li>
				<li> Retina Ready </li>
				<li> Multiple Page Website </li>
				<li> 24/7 Days Support </li>
				<li> Fully Responsive Template </li>
				<li> Speed Optimized </li>

			</ul>
		</section>
		<hr>
		<section id="Structure">
			<div class="page-header">
				<h3>HTML Structure</h3>
				<hr class="notop">
			</div>
			<div>&nbsp;</div>
			<img src="assets/images/html.png" style="border: 1px solid #DDD;">
			
			<div>&nbsp;</div>
		</section>
		<hr>
		<section id="CSS">
			<div class="page-header">
				<h3>CSS Files and Table of Contents</h3>
				<hr class="notop">
			</div>
			<img src="assets/images/p-css-1.png" style="border: 1px solid #DDD;">
			
		</section>
		<hr>
		<section id="JS">
			<div class="page-header">
				<h3>Java Scripts </h3>
				<hr class="notop">
			</div>
			<ul>
				<li>jquery.min.js</li>
				<li>bootstrap.min.js</li>
				<li>SmoothScroll.min.js</li>
				<li>swiper.min.js</li>
				<li>SmoothScroll.min.js</li>
				<li>validator.min.js</li>
				<li>custom.js</li>
			</ul>
		</section>
		<hr>
		<section id="Fonts">
			<div class="page-header">
				<h3>Fonts</h3>
				<hr class="notop">
			</div>
			<h5>We Have Used ''Poppins', sans-serif Fonts in this HTML Template</h5>
			<div style="width: 500px;">
				<ul>
					<li>Fonts used in this Template is the standard <a
							href="https://fonts.google.com/?query=hind">Free fonts –'Hind', sans-serif
							.</a></li>
				</ul>
			</div>
		</section>
		<hr>
		<section id="Credits">
			<div class="page-header">
				<h3>Source and Credit </h3>
				<hr class="notop">
			</div>
			<h5>We are Happy to Credit the Below : </h5>
			<a href="http://www.google.com/fonts/"> <img src="assets/images/googlefonts.jpg"
					style="border: 1px solid #DDD;"> </a>&nbsp;&nbsp;
			<a href="http://fortawesome.github.io/Font-Awesome/"> <img src="assets/images/font-awesome-logo.jpg"
					style="border: 1px solid #DDD;"></a>&nbsp;&nbsp;
			<a href="http://jquery.com/"> <img src="assets/images/jquery.jpg"
					style="border: 1px solid #DDD;"></a>&nbsp;&nbsp;
			<a href="http://getbootstrap.com/"> <img src="assets/images/bs.jpg" style="border: 1px solid #DDD;"></a>
			<ol>
				<li><a href="http://www.google.com/fonts/">Google fonts</a></li>
				<li><a href="http://fortawesome.github.io/Font-Awesome/">Font Awesome</a></li>
				<li><a href="http://jquery.com/">JQuery</a></li>
				<li><a href="http://getbootstrap.com/">Bootstrap</a></li>
			</ol>
		</section>
		<hr>
		<section id="Conclusion">
			<div class="page-header">
				<h3>Conclusion</h3>
				<hr class="notop">
			</div>
			<p>Once again, thank you so much for purchasing this Template. As I said at the beginning, I'd be glad to
				help you if you have any questions relating to this Template. No guarantees, but we'll do our best to
				assist.</p>
			<hr>
			<h5 class="ky1">PixelNx</h5>

			<br>
			<br>

		</section>
</body>

</html>